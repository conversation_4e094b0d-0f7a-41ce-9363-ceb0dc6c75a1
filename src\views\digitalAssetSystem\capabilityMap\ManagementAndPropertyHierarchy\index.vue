<!--产权与管理层级树-->
<template>
  <div class="management-property-hierarchy" :style="containerStyle">
    <CardBox :height="containerStyle.height || '100vh'" :show-header="false">
      <el-tabs v-model="activeTab" type="card" class="hierarchy-tabs" @tab-click="handleTabClick">

        <!-- 表格视图 Tab -->
        <el-tab-pane label="表格视图" name="table">
          <div class="tab-content">
            <HierarchyTable
              :table-data="tableData"
              :loading="tableLoading"
              :error="tableError"
              :table-height="tableHeight"
              :data-source="tableDataSource"
              loading-text="正在加载表格数据..."
              empty-text="暂无表格数据"
              @data-source-change="handleTableDataSourceChange"
              @load-children="handleTableLoadChildren"
              @row-detail="handleRowDetail"
              @retry="loadTableData" />
          </div>
        </el-tab-pane>

        <!-- 树形视图 Tab -->
        <el-tab-pane label="树形视图" name="tree-view">
          <div class="tab-content">
            <!-- 数据源切换控制 -->
            <div class="tree-view-controls">
              <el-radio-group v-model="treeDataSource" @change="handleTreeDataSourceChange" size="small">
                <el-radio-button label="property">产权层级</el-radio-button>
                <el-radio-button label="management">管理层级</el-radio-button>
              </el-radio-group>
            </div>

            <!-- 树形图表组件 -->
            <div class="tree-chart-container">
              <HierarchyTreeChart
                :chart-data="currentTreeData"
                :loading="currentTreeLoading"
                :error="currentTreeError"
                :loading-text="currentLoadingText"
                :empty-text="currentEmptyText"
                @retry="handleTreeRetry"
                @node-click="handleNodeClick"
                @chart-initialized="handleChartInitialized" />
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>


    </CardBox>
  </div>
</template>

<script>
import CardBox from '@/views/common/CardBox.vue'
import HierarchyTreeChart from './components/HierarchyTreeChart.vue'
import HierarchyTable from './components/HierarchyTable.vue'
import { getManagementHierarchyTree, getPropertyHierarchyTree } from '@/api/companyHierarchy'
import { calculateContainerHeight, ResponsiveHeightCalculator } from '@/utils'

export default {
  name: 'ManagementAndPropertyHierarchy',
  components: {
    CardBox,
    HierarchyTreeChart,
    HierarchyTable
  },
  data() {
    return {
      activeTab: 'table', // 默认显示表格视图

      // 树形视图数据源控制
      treeDataSource: 'property', // 树形视图数据源：property 或 management

      // 数据状态管理
      propertyData: [],
      managementData: [],
      propertyLoading: false,
      managementLoading: false,
      propertyError: null,
      managementError: null,

      // 表格相关数据
      tableDataSource: 'property', // 表格数据源：property 或 management
      tableData: [],
      tableLoading: false,
      tableError: null,
      tableHeight: 400,

      // 响应式高度计算器
      heightCalculator: null,

      // 容器样式
      containerStyle: {}
    }
  },
  computed: {
    // 当前树形视图的数据
    currentTreeData() {
      return this.treeDataSource === 'property' ? this.propertyData : this.managementData
    },

    // 当前树形视图的加载状态
    currentTreeLoading() {
      return this.treeDataSource === 'property' ? this.propertyLoading : this.managementLoading
    },

    // 当前树形视图的错误状态
    currentTreeError() {
      return this.treeDataSource === 'property' ? this.propertyError : this.managementError
    },

    // 当前加载文本
    currentLoadingText() {
      return this.treeDataSource === 'property' ? '正在加载产权层级数据...' : '正在加载管理层级数据...'
    },

    // 当前空数据文本
    currentEmptyText() {
      return this.treeDataSource === 'property' ? '暂无产权层级数据' : '暂无管理层级数据'
    }
  },
  watch: {
    // 监听Tab切换
    activeTab: {
      handler(newTab, oldTab) {
        if (newTab !== oldTab) {
          if (newTab === 'tree-view') {
            // 切换到树形视图时，确保当前数据源的数据已加载
            this.ensureTreeDataLoaded()
          } else if (newTab === 'table') {
            this.initTableView()
          }

          // Tab切换后重新计算高度
          this.$nextTick(() => {
            this.calculateTableHeight()
          })
        }
      },
      immediate: false
    },

    // 监听树形视图数据源切换
    treeDataSource: {
      handler() {
        this.ensureTreeDataLoaded()
      },
      immediate: false
    },

    // 监听表格数据变化，确保高度计算正确
    tableData: {
      handler() {
        if (this.activeTab === 'table') {
          this.$nextTick(() => {
            this.calculateTableHeight()
          })
        }
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initTableView()
      // 初始化响应式高度计算器
      this.initializeHeightCalculator()

      // 延迟加载默认数据源的数据，确保Tab DOM完全渲染
      setTimeout(() => {
        this.ensureTreeDataLoaded()
      }, 100)
    })

    // 开发环境下暴露调试方法到全局
    if (process.env.NODE_ENV === 'development') {
      window.debugHierarchyPage = this
      console.log('调试提示: 可在控制台使用 window.debugHierarchyPage.debugRecalculateHeights() 来调试高度计算')
    }
  },
  beforeDestroy() {
    // 清理响应式高度计算器
    if (this.heightCalculator) {
      this.heightCalculator.destroy()
      this.heightCalculator = null
    }

    // 清理全局调试引用
    if (process.env.NODE_ENV === 'development' && window.debugHierarchyPage === this) {
      delete window.debugHierarchyPage
    }

    // 组件销毁时的清理工作已移至子组件中处理
  },

  // keep-alive 相关的生命周期钩子
  activated() {
    // 组件被激活时重新计算高度
    if (this.heightCalculator) {
      this.heightCalculator.recalculate()
    }
  },

  deactivated() {
    // 组件被停用时暂停高度计算器（可选）
  },
  methods: {
    /**
     * Tab切换处理
     */
    handleTabClick(tab) {
      // Tab切换逻辑已移至watch中处理，这里保留兼容性
      console.log('Tab切换:', tab.name)
    },

    /**
     * 处理树形视图数据源切换
     */
    handleTreeDataSourceChange() {
      this.ensureTreeDataLoaded()
    },

    /**
     * 确保当前树形视图数据源的数据已加载
     */
    ensureTreeDataLoaded() {
      if (this.treeDataSource === 'property') {
        if (this.propertyData.length === 0 && !this.propertyLoading) {
          this.loadPropertyData()
        }
      } else if (this.treeDataSource === 'management') {
        if (this.managementData.length === 0 && !this.managementLoading) {
          this.loadManagementData()
        }
      }
    },

    /**
     * 处理树形视图重试
     */
    handleTreeRetry() {
      if (this.treeDataSource === 'property') {
        this.loadPropertyData()
      } else {
        this.loadManagementData()
      }
    },

    /**
     * 处理图表节点点击事件
     */
    handleNodeClick(event) {
      console.log('节点点击事件:', event)
    },

    /**
     * 处理图表初始化完成事件
     */
    handleChartInitialized(chart) {
      console.log('图表初始化完成:', chart)
    },

    /**
     * 处理表格数据源切换
     */
    handleTableDataSourceChange(dataSource) {
      this.tableDataSource = dataSource
      this.loadTableData()
    },

    /**
     * 处理表格懒加载子节点
     */
    handleTableLoadChildren({ tree, treeNode, resolve }) {
      console.log({ tree, treeNode, resolve })
      this.loadTableChildren(tree, treeNode, resolve)
    },

    /**
     * 处理表格行详情
     */
    handleRowDetail(row) {
      console.log('查看行详情:', row)
    },

    /**
     * 加载产权层级数据
     */
    async loadPropertyData() {
      this.propertyLoading = true
      this.propertyError = null

      try {
        const response = await getPropertyHierarchyTree()
        if (response && response.code == 200) {
          this.propertyData = response.data || []

          // 数据加载完成，子组件会自动渲染
        } else {
          throw new Error(response?.message || '获取产权层级数据失败')
        }
      } catch (error) {
        console.error('加载产权层级数据失败:', error)
        this.propertyError = error.message || '网络请求失败，请稍后重试'
      } finally {
        this.propertyLoading = false
      }
    },

    /**
     * 加载管理层级数据
     */
    async loadManagementData() {
      this.managementLoading = true
      this.managementError = null

      try {
        const response = await getManagementHierarchyTree()
        if (response && response.code == 200) {
          this.managementData = response.data || []

          // 数据加载完成，子组件会自动渲染
        } else {
          throw new Error(response?.message || '获取管理层级数据失败')
        }
      } catch (error) {
        console.error('加载管理层级数据失败:', error)
        this.managementError = error.message || '网络请求失败，请稍后重试'
      } finally {
        this.managementLoading = false
      }
    },


    /**
     * 初始化响应式高度计算器
     */
    initializeHeightCalculator() {
      this.$nextTick(() => {
        // 查找主容器元素
        const container = this.$el?.querySelector('.management-property-hierarchy .card-box') ||
                         this.$el?.querySelector('.management-property-hierarchy') ||
                         this.$el

        if (container) {
          this.heightCalculator = new ResponsiveHeightCalculator({
            container,
            safeGap: 16, // 页面底部安全间距
            minHeight: 400, // 最小高度
            maxHeightRatio: 0.95, // 最大高度比例
            fixedDeduction: 0, // 无固定减去值，因为使用100vh
            enableDebug: process.env.NODE_ENV === 'development',
            componentName: this.$options.name,
            onHeightChange: (result) => {
              this.applyContainerHeight(result)
            }
          })

          this.heightCalculator.start()
        } else {
          // 如果容器还没有准备好，延迟初始化
          setTimeout(() => {
            this.initializeHeightCalculator()
          }, 100)
        }
      })
    },

    /**
     * 应用容器高度
     */
    applyContainerHeight(result) {
      if (result.success) {
        // 更新容器样式
        this.containerStyle = {
          height: result.height
        }

        // 计算表格高度（根据当前激活的Tab调整计算逻辑）
        const tabHeaderHeight = 56 // Tab头部高度
        const padding = 24 // 内边距（减少内边距）

        // 树形视图有控制栏，表格视图没有控制栏
        let availableHeight = result.heightValue - tabHeaderHeight - padding

        if (this.activeTab === 'tree-view') {
          // 树形视图需要减去控制栏高度
          const controlsHeight = 60 // 控制栏高度（树形视图的单选按钮区域）
          availableHeight -= controlsHeight
        } else if (this.activeTab === 'table') {
          // 表格视图可能有自己的控制栏（如果显示数据源切换）
          const tableControlsHeight = 0 // 表格视图当前没有控制栏
          availableHeight -= tableControlsHeight
        }

        // 确保最小高度
        this.tableHeight = Math.max(350, availableHeight)

        if (process.env.NODE_ENV === 'development') {
          console.log(`[${this.$options.name}] 高度计算完成`, {
            activeTab: this.activeTab,
            containerHeight: result.height,
            containerHeightValue: result.heightValue,
            tabHeaderHeight,
            padding,
            availableHeight,
            finalTableHeight: this.tableHeight,
            strategy: '根据Tab类型动态调整高度计算'
          })
        }
      }
    },

    /**
     * 计算表格高度（保持向后兼容）
     */
    calculateTableHeight() {
      if (this.heightCalculator) {
        this.heightCalculator.recalculate()
      } else {
        // 如果高度计算器未初始化，使用传统方法
        this.tableHeight = Math.max(300, window.innerHeight * 0.65 - 200)
      }
    },

    /**
     * 初始化表格视图
     */
    initTableView() {
      if (this.tableData.length === 0) {
        this.loadTableData()
      }
    },

    /**
     * 加载表格数据
     */
    async loadTableData() {
      this.tableLoading = true
      this.tableError = null

      try {
        let response
        if (this.tableDataSource === 'property') {
          response = await getPropertyHierarchyTree()
        } else {
          response = await getManagementHierarchyTree()
        }

        if (response && response.code == 200) {
          this.tableData = this.transformDataForTable(response.data || [])
        } else {
          throw new Error(response?.message || '获取表格数据失败')
        }
      } catch (error) {
        console.error('加载表格数据失败:', error)
        this.tableError = error.message || '网络请求失败，请稍后重试'
      } finally {
        this.tableLoading = false
      }
    },

    /**
     * 转换数据为表格格式
     * 说明：为了支持懒加载到第3级（及更深），我们将完整的下一层子节点保存在“__children”中，
     * 实际渲染用的“children”按需填充，展开时从“children”或“__children”中取数据。
     */
    transformDataForTable(data) {
      if (!data || data.length === 0) return []

      const transformNode = (node) => {
        const transformed = {
          zcbiId: node.zcbiId,
          zcbiName: node.zcbiName || '未知企业',
          zcbiShortName: node.zcbiShortName,
          zcbiZcd: node.zcbiZcd,
          zcbiFddbr: node.zcbiFddbr,
          zcbiJyzt: node.zcbiJyzt,
          zcbiZyhy1: node.zcbiZyhy1,
          zcbiZyhy2: node.zcbiZyhy2,
          zcbiZyhy3: node.zcbiZyhy3,
          hasChildren: Array.isArray(node.children) && node.children.length > 0,
          children: []
        }

        // 递归转换原始children，完整保存在 __children 中，供懒加载使用
        if (Array.isArray(node.children) && node.children.length > 0) {
          const nextLevel = node.children.map(child => transformNode(child))
          // 用于实际渲染：仅在第一层直接挂载下一层，第二层及更深由懒加载触发
          transformed.children = nextLevel.map(n => ({ ...n, children: [] }))
          // 完整缓存，用于后续懒加载
          transformed.__children = nextLevel
        }

        return transformed
      }

      return data.map(transformNode)
    },

    /**
     * 表格懒加载子节点
     */
    loadTableChildren(tree, _treeNode, resolve) {
      try {
        // 异步模拟：优先使用已存在的可见children；若为空则使用缓存的 __children
        setTimeout(() => {
          if (Array.isArray(tree.children) && tree.children.length > 0) {
            resolve(tree.children)
          } else if (Array.isArray(tree.__children) && tree.__children.length > 0) {
            resolve(tree.__children)
          } else {
            resolve([])
          }
        }, 0)
      } catch (error) {
        console.error('加载子节点失败:', error)
        resolve([])
      }
    },

    /**
     * 调试方法：手动触发高度重新计算（可在控制台调用）
     */
    debugRecalculateHeights() {
      console.log('=== 开始调试高度计算 ===')

      // 输出当前页面基本信息
      console.log('页面基本信息:', {
        windowInnerHeight: window.innerHeight,
        windowInnerWidth: window.innerWidth,
        documentHidden: document.hidden,
        hasHeightCalculator: !!this.heightCalculator,
        activeTab: this.activeTab,
        treeDataSource: this.treeDataSource,
        strategy: '使用工具函数的响应式高度计算'
      })

      // 输出当前样式状态
      console.log('当前样式状态:', {
        containerStyle: this.containerStyle,
        tableHeight: this.tableHeight
      })

      // 输出高度计算器状态
      if (this.heightCalculator) {
        console.log('高度计算器状态:', {
          isActive: this.heightCalculator.isActive,
          lastHeight: this.heightCalculator.lastHeight,
          retryCount: this.heightCalculator.retryCount
        })
      }

      // 强制重新计算
      console.log('🔄 开始强制重新计算...')
      this.calculateTableHeight()

      console.log('=== 高度计算调试完成 ===')

      // 返回当前组件实例，方便链式调用
      return this
    }

  }
}
</script>

<style lang="scss" scoped>
.management-property-hierarchy {
  height: 100%;

  .hierarchy-tabs {
    height: 100%;

    ::v-deep .el-tabs__header {
      margin: 0 0 16px 0;

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        height: 40px;
        line-height: 40px;
        padding: 0 20px;
        border: 1px solid #dcdfe6;
        border-radius: 4px 4px 0 0;
        margin-right: 2px;
        background: #f5f7fa;
        color: #606266;
        font-weight: 500;
        transition: all 0.3s;

        &:hover {
          color: #CC1214;
          border-color: #CC1214;
        }

        &.is-active {
          color: #CC1214;
          border-color: #CC1214;
          background: #fff;
          border-bottom-color: #fff;
          position: relative;
          z-index: 1;

          &::before {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #fff;
          }
        }
      }
    }

    ::v-deep .el-tabs__content {
      height: calc(100% - 56px);
      border: 1px solid #dcdfe6;
      border-radius: 0 4px 4px 4px;
      background: #fff;
      overflow: hidden;
    }

    ::v-deep .el-tab-pane {
      height: 100%;
    }
  }

  .tab-content {
    height: 100%;
    position: relative;
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  // 树形视图控制栏样式
  .tree-view-controls {
    padding: 16px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  // 树形图表容器样式
  .tree-chart-container {
    flex: 1;
    overflow: hidden;
  }

  // 子组件样式已移至各自组件中
}

// 响应式设计
@media (max-width: 768px) {
  .management-property-hierarchy {
    .hierarchy-tabs {
      ::v-deep .el-tabs__header {
        .el-tabs__item {
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
