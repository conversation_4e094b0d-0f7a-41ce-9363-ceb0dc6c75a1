<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <el-tabs v-model="activeName" type="border-card" @tab-click="handleTabsClick">
        <el-tab-pane label="股权投资规划目标" name="first">
          <tzPlanTargets :currentOdId="currentOdId" tptType="投资" :type="type" />
        </el-tab-pane>
        <el-tab-pane label="军工投资规划目标" name="second">
          <JgPlanTargets :currentOdId="currentOdId" type="军工" />
        </el-tab-pane>
         <el-tab-pane label="自筹投资规划目标" name="third">
          <JgPlanTargets :currentOdId="currentOdId" type="自筹" />
        </el-tab-pane>
        <el-tab-pane label="融资规划目标" name="fourth">
          <tzPlanTargets :currentOdId="currentOdId" tptType="融资" />
        </el-tab-pane>
        <el-tab-pane label="资产处置与盘活" name="fiveth">
          <tzPlanListedTarget :currentOdId="currentOdId" tftType="资产处置与盘活" treeLpvType="ZCCZYPH" :showSecond="false" />
        </el-tab-pane>
        <el-tab-pane label="投资能力体系规划" name="fixth">
          <tzPlanListedTarget :currentOdId="currentOdId" tftType="投资能力体系规划" treeLpvType="TZNLTX" :showSecond="false" />
        </el-tab-pane>
        <el-tab-pane label="上市公司市值管理目标" name="seventh">
          <tzPlanListedTarget :currentOdId="currentOdId" tftType="上市公司市值管理目标" treeLpvType="SSGSYJJ" />
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>

<script>
  import CardBox from 'common/CardBox'
  import tzPlanTargets from '../tzPlanTargets'
  import JgPlanTargets from '../JgPlanTargets'
  import rzPlanTargets from '../rzPlanTargets'
  import tzPlanListedTarget from '../tzPlanListedTarget'
  import tzPlanAbility from '../tzPlanAbility'
  import { mapGetters } from 'vuex'
  export default {
    name: "GroupCompany",
    props: {
      type: {
        type: String
      }
    },
    components: {
      CardBox,
      tzPlanTargets,
      JgPlanTargets,
      rzPlanTargets,
      tzPlanListedTarget,
      tzPlanAbility
    },
    data() {
      return {
        activeName: 'first',
        currentOdId:''
      }
    },
    computed: {
      ...mapGetters({
        loginUser: 'user/loginUser'
      }),
    },
    created(){
      this.currentOdId = this.loginUser.orgId
    },
    methods: {
      handleTabsClick() {

      }
    },
  }
</script>

<style lang="scss" scoped>

</style>