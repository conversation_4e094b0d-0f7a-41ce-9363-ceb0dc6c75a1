<template>
  <div class="container">
    <el-row :gutter="24">
      <el-col :span="6">
        <SmallCard :title="'业务板块情况划分'">
          <div slot="rightTitle">
            <el-radio-group v-model="ywbkRadio" >
              <el-radio-button label="数量"></el-radio-button>
              <el-radio-button label="原值"></el-radio-button>
              <el-radio-button label="净值"></el-radio-button>
            </el-radio-group>
          </div>
          <YwbkDetail :radio="ywbkRadio" home="资产分析"/>
        </SmallCard>
      </el-col>
      <el-col :span="6">
        <SmallCard :title="'资产状态情况划分'">
          <div slot="rightTitle">
            <el-radio-group v-model="zcztRadio" >
              <el-radio-button label="数量"></el-radio-button>
              <el-radio-button label="原值"></el-radio-button>
              <el-radio-button label="净值"></el-radio-button>
            </el-radio-group>
          </div>
          <ZcztDetail :radio="zcztRadio"  home="资产分析"/>
        </SmallCard>
      </el-col>
      <el-col :span="6">
        <SmallCard :title="'经费来源情况划分'">
          <div slot="rightTitle">
            <el-radio-group v-model="jflyRadio" >
              <el-radio-button label="数量"></el-radio-button>
              <el-radio-button label="原值"></el-radio-button>
              <el-radio-button label="净值"></el-radio-button>
            </el-radio-group>
          </div>
          <JflyDetail :radio='jflyRadio' home="资产分析"/>
        </SmallCard>
      </el-col>
      <el-col :span="6">
        <SmallCard :title="'国别情况划分'">
          <div slot="rightTitle">
            <el-radio-group v-model="gbqkRadio" >
              <el-radio-button label="数量"></el-radio-button>
              <el-radio-button label="原值"></el-radio-button>
              <el-radio-button label="净值"></el-radio-button>
            </el-radio-group>
          </div>
          <GbqkDetail :radio="gbqkRadio" home="资产分析"/>
        </SmallCard>
      </el-col>
    </el-row>
    
    <el-row style="margin-top: 24px;" :gutter="24">
      <el-col :span="12">
        <SmallCard :title="'资产用途情况划分'">
          <YtqkDetail />
        </SmallCard>
      </el-col>
      <el-col :span="12">
        <SmallCard :title="'资产类别情况划分'">
          <ZclbDetail :year="year"/>
        </SmallCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import SmallCard from '@/views/common/SmallCard'
  import YwbkDetail from './YwbkDetail'
  import ZcztDetail from './ZcztDetail'
  import JflyDetail from './JflyDetail'
  import GbqkDetail from './GbqkDetail'
  import YtqkDetail from './YtqkDetail'
  import ZclbDetail from './ZclbDetail'
  export default {
    name: "UseDetail",
    components: {
      SmallCard,
      YwbkDetail,
      ZcztDetail,
      JflyDetail,
      GbqkDetail,
      YtqkDetail,
      ZclbDetail
    },
    data() {
      return {
        ywbkRadio: '数量',
        zcztRadio: '数量',
        jflyRadio: '数量',
        gbqkRadio: '数量',
      }
    },
    methods: {

    },
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
  }
</style>