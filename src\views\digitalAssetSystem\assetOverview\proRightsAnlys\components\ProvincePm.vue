<template>
  <div class="provincePm-container">
    <el-table
      border
      :data="tableDataProvince"
      height="495px"
      show-summary
      ref="table"
    >
      <el-table-column
        align="center"
        label="序号"
        type="index"
        width="50"
      />
      <el-table-column
        align="center"
        label="省市"
        min-width="35%"
        prop="zcciAddress"
      />
      <el-table-column
        align="center"
        label="户数"
        min-width="30%"
        prop="zcciAddressCount"
      />
      <el-table-column
        align="center"
        label="占比"
        min-width="25%"
        prop="zcciAddressPer"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.zcciAddressPer + '%' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { searchzcciAddress } from '@api/sam/property.js'
  export default {
    name: "ProvincePm",
    components: {},
    props: ['year'],
    data() {
      return {
        tableDataProvince: []
      }
    },
    mounted() {
      this.getsearchzcciAddress()
    },
    methods: {
      async getsearchzcciAddress() {
        let params = {
          year: this.year,
          start: 0
        }
        // let { data } = await searchzcciAddress(params)
        //   this.tableDataProvince = data
      },
    },
    updated() {
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    }
  }
</script>

<style lang="scss" scoped>

</style>