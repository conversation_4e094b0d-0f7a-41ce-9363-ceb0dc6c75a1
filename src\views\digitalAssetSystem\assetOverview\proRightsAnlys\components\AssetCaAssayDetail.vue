<template>
  <div class="assetCaAssayDetail-container">
    <div class="container-header">
      <span class="pathText">您的位置:</span>
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item :to="{ path: '/views/cockpit/assetCqAssay/index' }">产权分析-产权分析总览</el-breadcrumb-item>
        <el-breadcrumb-item>
          {{ title + '详情' }} 
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="container-body">
      <div class="leftCont">
        <el-table
          :data="tableData"
          height="590"
          ref="companyTable"
          border
          highlight-current-row
          @row-click="handleRowClick"
          style="width: 100%">
          <el-table-column
            prop="title"
            v-if="title == '企业总数'"
            label="分类" 
          />
          <el-table-column
            prop="nums"
            v-if="title == '企业总数'"
            label="户数" 
          />
          <el-table-column
            prop="zcciEnterpriseType"
            v-if="title == '企业类别分析'"
            label="企业类别" />
          <el-table-column
            prop="zcciEnterpriseTypeCount"
            v-if="title == '企业类别分析'"
            label="户数" />
          
          <el-table-column
            prop="zcciLevel"
            v-if="title == '管理级次分布'"
            label="企业类别" 
          />
          <el-table-column
            align="center"
            v-if="title == '管理级次分布'"
            label="户数"
            prop="zcciLevelCount"
          />

          <el-table-column
            align="center"
            label="产权级次"
            v-if="title == '产权级次分布'"
            prop="zcciLegalLevel"
          />
          <el-table-column
            align="center"
            label="户数"
            v-if="title == '产权级次分布'"
            prop="zcciLegalLevelCount"
          />

          <el-table-column
            align="center"
            label="持股比例"
            v-if="title == '持股比例情况'"
            prop="stockBetween"
          />
          <el-table-column
            align="center"
            label="户数"
            v-if="title == '持股比例情况'"
            prop="stockBetweenCount"
          />

          <el-table-column
            align="center"
            label="形成方式"
            v-if="title == '企业形成方式'"
            prop="zcciFormatWay"
          />
          <el-table-column
            align="center"
            label="户数"
            v-if="title == '企业形成方式'"
            prop="zcciFormatWayCount"
          />
        </el-table>
      </div>
      <div class="rightCont">
        <AssetCqAssayDetailRight 
          :title="title" 
          :currentName="currentName"
          :name="name"
          :companyShareholding="companyShareholding"
          :year="year"/>
      </div>
    </div>
  </div>
</template>

<script>
  import AssetCqAssayDetailRight from './AssetCqAssayDetailRight';
  export default {
    name: "AssetCaAssayDetail",
    components: {
      AssetCqAssayDetailRight,
    },
    data() {
      return {
        title: '',
        tableData: [],
        year: '',
        currentName: '',
        companyShareholding: '',
        name: []
      }
    },
    created() {
      this.title = this.$route.query.title
      this.currentName = this.$route.query.currentName
      // this.companyShareholding = this.$route.params.companyShareholding
      this.tableData = JSON.parse(this.$route.query.data)
      this.name = this.$route.query.name
      this.$nextTick(() => {
        this.$refs.companyTable.setCurrentRow(this.$refs.companyTable.data[this.$route.query.tableIndex],true)
      })
    },
    methods: {
      handleRowClick(row, column, event) {
        this.companyShareholding = null
        if(this.title == '企业总数') {
          this.currentName = row.title
        }else if(this.title == '企业类别分析') {
          this.currentName = row.zcciEnterpriseType
        }else if(this.title == '管理级次分布') {
          this.currentName = row.zcciLevel
        }else if(this.title == '产权级次分布') {
          this.currentName = row.zcciLegalLevel
        }else if(this.title == '持股比例情况') {
          this.currentName = null
          this.name = row.stockBetween
        }else if(this.title == '企业形成方式') {
          this.currentName = row.zcciFormatWay
        }
      }
    },
    
  }
</script>

<style lang="scss" scoped>

  v-deep .el-table .tr-red {
    color: red !important;
  }
  .assetCaAssayDetail-container {
    width: 100%;
    height: calc(100vh - 76px - 90px - 48px - 55px);
    background: #F6F8F9!important;
    padding: 0px!important;
    .container-header {
      width: 100%;
      height: 32px;
      color: #909399;
      line-height: 32px;
      font-size: 16px;
      display: flex;
      align-items: center;
    }
    .container-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: calc(100% - 32px);
      .leftCont {
        width: 420px;
        height: 100%;
        background: #FFFFFF;
        box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
        border-radius: 4px;
        border: 1px solid #E4E7ED;
        padding: 24px;
      }
      .rightCont {
        width: calc(100% - 420px - 24px);
        height: 100%;
        overflow-y: auto;
      }
    }
  }
</style>