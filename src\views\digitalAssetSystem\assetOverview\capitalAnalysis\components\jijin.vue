<template>
  <div class="container">
    <SmallCard
      :style="{ marginBottom: '24px' }"
      :title="`中国电科共主导发起并管理私募股权投资基金${total}支`"
    >
      <el-row :gutter="24" :style="{ marginBottom: '24px' }">
        <el-col :span="8">
          <div class="box">
            <div class="titles">
              认缴总规模 (即本期规模)
              <span>{{ rjzgmVal }}</span>
              万元
            </div>
            <VabChartPie :option="option1" :title="'中国电科主导'"/>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="box">
            <div class="titles">
              累计已投资金额
              <span>{{ ljytzjeVal }}</span>
              万元
            </div>
            <VabChartPie :option="option2" :title="'中国电科主导'" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class="box">
            <div class="titles">
              累计已投资项目
              <span>{{ ljytzxmVal }}</span>
              个
            </div>
            <VabChartPie :option="option3" :title="'中国电科主导'" />
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="8">
          <div class="box">
            <div class="titles">
              实缴总规模
              <span>{{ sjzgmVal }}</span>
              万元
            </div>
            <VabChartPie :option="option4" :title="'中国电科主导'" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class="box">
            <div class="titles">
              累计收回金额
              <span>{{ ljhsjeVal }}</span>
              万元
            </div>
            <VabChartPie :option="option5" :title="'中国电科主导'" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class="box">
            <div class="titles">
              累计退出项目
              <span>{{ ljtcxmVal }}</span>
              个
            </div>
            <VabChartPie :option="option6" :title="'中国电科主导'" />
          </div>
        </el-col>
      </el-row>
    </SmallCard>
    <SmallCard :title="`中国电科参与基金 ${total1} 支`">
      <el-row :gutter="24">
        <el-col :span="8">
          <div class='cursorPointer'>
            <VabChartPie :option="option7" :title="'中国电科参与'" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class='cursorPointer'>
            <VabChartPie :option="option8" :title="'中国电科参与'" />
          </div>
        </el-col>
        <el-col :span="8">
          <div class='cursorPointer'>
            <VabChartPie :option="option9" :title="'中国电科参与'" />
          </div>
        </el-col>
      </el-row>
    </SmallCard>
    <dialogCard 
      :dialogTableVisible="dialogVisible"
      ref="jijinDetail" 
      destroy-on-close
      v-if="dialogVisible"
      :close="closeDialigCard"
      :title="'基金情况详细信息'">
      <jijinDetail slot="content" :type.sync='type'/>
    </dialogCard>
  </div>
</template>

<script>
  // import {
  //   getGroupParticipationInformation,
  //   getGroupLeadingInformation,
  // } from '@/api/sam/assetCapitalAnalysis'
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  import SmallCard from '@/views/common/SmallCard'
  import dialogCard from '@/views/common/DialogCard'
  import jijinDetail from './jijinDetail.vue'
  export default {
    name: 'jijin',
    components: {
      VabChartPie,
      SmallCard,
      dialogCard,
      jijinDetail
    },
    data() {
      return {
        type:"",
        dialogVisible:false,
        option1: {},
        option2: {},
        option3: {},
        option4: {},
        option5: {},
        option6: {},
        option7: {},
        option8: {},
        option9: {},
        total: 0,
        total1: 0,
        rjzgmVal: 0,
        ljytzjeVal: 0,
        ljytzxmVal: 0,
        sjzgmVal: 0,
        ljhsjeVal: 0,
        ljtcxmVal: 0,
      }
    },
    mounted() {
      this.getAllOption()
    },
    created() {
      this.$baseEventBus.$on('echartClick',(params,title) => {
        if(title == '中国电科主导') {
          this.type = 1
          this.dialogVisible = true
        }else if(title == '中国电科参与') {
          this.type = 2
          this.dialogVisible = true
        }
      })
    },
    methods: {
      async getAllOption() {
        // const { data } = await getGroupLeadingInformation()
        // const data1 = await getGroupParticipationInformation()
        // this.rjzgmVal = data[0].maximum
        // this.ljytzjeVal = data[3].maximum
        // this.ljytzxmVal = data[4].maximum
        // this.sjzgmVal = data[1].maximum
        // this.ljhsjeVal = data[2].maximum
        // this.ljtcxmVal = data[5].maximum
        // let list = data1.data || []
        // this.option1 = this.getOption([
        //   { 
        //     value: data[0].total, 
        //     name: data[0].name, 
        //     maxMun: data[0].maximum 
        //   }
        // ])
        // this.option2 = this.getOption([
        //   {
        //     value: data[3].total,
        //     name: data[3].name,
        //     maxMun: data[3].maximum,
        //   }],
        //   '#F6BD16'
        // )
        // this.option3 = this.getOption(
        //   [
        //     {
        //       value: data[4].total,
        //       name: data[4].name,
        //       maxMun: data[4].maximum,
        //     },
        //   ],
        //   '#5AD8A6',
        //   '个'
        // )
        // this.option4 = this.getOption([
        //   { value: data[1].total, name: data[1].name, maxMun: data[1].maximum },
        // ])
        // this.option5 = this.getOption(
        //   [
        //     {
        //       value: data[2].total,
        //       name: data[2].name,
        //       maxMun: data[2].maximum,
        //     },
        //   ],
        //   '#F6BD16'
        // )
        // this.option6 = this.getOption(
        //   [
        //     {
        //       value: data[5].total,
        //       name: data[5].name,
        //       maxMun: data[5].maximum,
        //     },
        //   ],
        //   '#5AD8A6',
        //   '个'
        // )
        // this.total = data[6].allData
        // this.option7 = this.getOption([
        //   {
        //     value: list[2].total,
        //     name: list[2].name,
        //     maxMun: list[2].maximum,
        //   },
        // ])
        // this.option8 = this.getOption([
        //   {
        //     value: list[0].total,
        //     name: list[0].name,
        //     maxMun: list[0].maximum,
        //   },
        // ])
        // this.option9 = this.getOption([
        //   {
        //     value: list[1].total,
        //     name: list[1].name,
        //     maxMun: list[1].maximum,
        //   },
        // ])
        // this.total1 = list[3].allData
      },
      getOption(
        data = [
          {
            value: 150,
            name: '其中集团认缴金额',
            maxMun: 200,
          },
        ],
        color = '#5B8FF9',
        danwei = '万元'
      ) {
        return {
          series: [
            {
              type: 'gauge',
              legendHoverLink: true,
              max: data[0].maxMun,
              startAngle: 180,
              radius: '100%',
              splitNumber: 2,
              center: ['50%', '60%'],
              endAngle: 0,
              progress: {
                show: true,
                width: 40,
                itemStyle: {
                  color: color,
                },
              },
              axisLine: {
                lineStyle: {
                  width: 40,
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: color,
                },
              },
              axisLabel: {
                show: false,
              },
              pointer: {
                width: 2,
                itemStyle: {
                  color: color,
                },
              },
              anchor: {
                show: true,
                size: 6,
                showAbove: true,
                itemStyle: {
                  borderWidth: 10,
                  borderColor: color,
                },
              },
              title: {
                show: true,
                fontWeight: 400,
              },
              detail: {
                valueAnimation: true,
                fontSize: 33,
                offsetCenter: [0, '50%'],
                formatter: function (value) {
                  return value + danwei
                },
              },
              data: data,
            },
          ]
        }
      },
      closeDialigCard() {
        this.dialogVisible = false
      }
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
    .box {
      *{
        cursor:pointer !important;
      }
      .titles {
        background: #f5f7fa;
        border: 1px solid #e4e7ed;
        height: 56px;
        line-height: 56px;
        font-size: 20px;
        font-weight: 400;
        text-align: center;
        span {
          font-weight: 500;
          font-size: 24px;
        }
      }
      border: 1px solid #e4e7ed;
      border-top: 0px;
    }
    
  }
  :deep() {
    .echarts {
      width: 100%;
      height: 252px;
      cursor: pointer!important;
    }
  }
</style>
