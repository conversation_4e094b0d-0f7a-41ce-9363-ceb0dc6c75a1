<template>
  <div class="assetCapitalAnalysis-container">

    <div class="staffStockDetail">
      <CardBox :title="'员工持股情况'">
        <StaffStockDetail />
      </CardBox>
    </div>

    <div class="outfinancing">
      <CardBox :title="'外部权益性融资管理'">
        <Outfinancing :year="year" />
      </CardBox>
    </div>

    <div class="jijin">
      <CardBox :title="'基金情况'">
        <div style='text-align:center;'>
          <div class="title" @click='openPage'>
            <span>截至</span>
            <span class='yearMonth'>{{ year }}</span>
            <span>年</span>
            <span class='yearMonth'>{{ month }}</span>
            <span>月底</span>
          </div>
        </div>
        <jijin />
      </CardBox>
    </div>

    <div class="stockCompanyInfo">
      <CardBox :title="'上市公司'">
        <StockCompanyInfo />
      </CardBox>
    </div>
  </div>
</template>

<script>
  import CardBox from '@/views/common/CardBox'
  import StaffStockDetail from './components/StaffStockDetail'
  import Outfinancing from './components/Outfinancing'
  import StockCompanyInfo from './components/StockCompanyInfo'
  import jijin from './components/jijin.vue'
  // import { getZcglDateManage } from '@/api/projectManagement/fixedAssets'
  export default {
    name: 'AssetCapitalAnalysis',
    components: {
      CardBox,
      StaffStockDetail,
      Outfinancing,
      StockCompanyInfo,
      jijin,
    },
    data() {
      return {
        dialogVisible:false,
        year: '',
        month: new Date().getMonth() + 1,
      }
    },
    created() {
      this.getSetYear()
    },
    methods: {
      openPage(){
        this.dialogVisible = true
      },
      async getSetYear() {
        // const data = await getZcglDateManage()
        // this.year = data.zdmYearQt
      },
    },
  }
</script>

<style lang="scss" scoped>
  .headCont {
    width: 100%;
    background: #FFFFFF;
    border-bottom: 1px solid #E4E7ED;
    padding-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .titleLeft {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #CC1214;
      img {
        margin-right: 8px;
      }
    }
    .closeBtn {
      cursor: pointer;
    }
  }
  .assetCapitalAnalysis-container {
    width: 100%;
    height: 100%;
    background: #f6f8f9 !important;
    padding: 0px !important;

    .outfinancing {
      margin-top: 24px;
    }
    .jijin {
      margin-top: 24px;
      .title {
        width:265px;
        height:56px;
        line-height: 52px;
        text-align:center;
        vertical-align:middle;
        border-radius:4px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        background:rgb(253,246,246);
        box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
        border:1px solid #CC1214;
        display:inline-block;
        margin:-4px auto 20px;
        .yearMonth {
          font-size: 24px;
          font-weight: 600;
          color:#CC1214;
          padding:0 4px;
        }
        span{
          vertical-align:middle;
        }
      }
    }

    .stockCompanyInfo {
      margin-top: 24px;
    }
  }
</style>
