<!--添加新分组弹窗-->
<template>
  <DialogCard :dialogTableVisible="dialogVisible" title="房屋" :close="handleCancel" :flag="true" width="1700px" height="700px" top="5vh">
    <div slot="content" class="add-group-content">

      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="单位名称:">
              <el-input v-model="searchForm.name" placeholder="请输入企业名称" class="inputW" />
            </el-form-item>
            <el-form-item label="统一社会信用代码:">
              <el-input v-model="searchForm.tyxydm" placeholder="请输入统一社会信用代码" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
          </el-form-item>
        </div>
      </el-form>

      <el-table :data="tableData" style="width: 100%;margin-bottom: 50px;" height="500px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="ZCHI_YEAR" label="年份" width="100" show-overflow-tooltip align="center" label-class-name="zchiYear" />
        <el-table-column prop="ZCHI_MJ" label="密级" width="100" show-overflow-tooltip align="center" label-class-name="zchiMj" />
        <el-table-column prop="COMPANY_TNAME" label="二级单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyTname" />
        <el-table-column prop="COMPANY_NAME" label="本单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyName" />
        <el-table-column prop="ZCHI_CREDIT_CODE" label="本单位统一社会信用代码" width="180" show-overflow-tooltip align="center" label-class-name="zchiCreditCode" />
        <el-table-column prop="ZCHI_IF_EXIST" label="是否取得房屋所有权证" width="180" show-overflow-tooltip align="center" label-class-name="zchiIfExist" />
        <el-table-column prop="ZCHI_ASSETS_NO" label="资产编号" width="150" show-overflow-tooltip align="center" label-class-name="zchiAssetsNo" />
        <el-table-column prop="ZCHI_ASSETS_NAME" label="资产名称" width="150" show-overflow-tooltip align="center" label-class-name="zchiAssetsName" />
        <el-table-column prop="ZCHI_ASSETSM_TYPE" label="资产类型" width="150" show-overflow-tooltip align="center" label-class-name="zchiAsssetsmType" />
        <el-table-column prop="ZCHI_CERTIFICATE_CODE" label="房屋所有权证号" width="150" show-overflow-tooltip align="center" label-class-name="zchiCertificateCode" />
        <el-table-column prop="ZCHI_NOCERTIFICATE_REASON" label="未取得房屋权属证明原因" width="180" show-overflow-tooltip align="center" label-class-name="zchiNocertificateReason" />
        <el-table-column prop="ZCHI_USE_DESCRIBE" label="现状用途描述" width="150" show-overflow-tooltip align="center" label-class-name="zchiUseDescribe" />
        <el-table-column prop="ZCHI_STRATEGY_DESCRIBE" label="是否有特别战略安排" width="150" show-overflow-tooltip align="center" label-class-name="zchiStrategyDescribe" />
        <el-table-column prop="ZCHI_RANGE" label="境内/境外" width="150" show-overflow-tooltip align="center" label-class-name="zchiRange" />
        <el-table-column prop="ZCHI_COUNTRY" label="国家或地区" width="150" show-overflow-tooltip align="center" label-class-name="zchiCountry" />
        <el-table-column prop="ZCHI_PROVINCE" label="坐落位置（省）" width="150" show-overflow-tooltip align="center" label-class-name="zchiProvince" />
        <el-table-column prop="ZCHI_CITY" label="坐落位置（市）" width="150" show-overflow-tooltip align="center" label-class-name="zchiCity" />
        <el-table-column prop="ZCHI_DISTRICT" label="坐落位置（区）" width="150" show-overflow-tooltip align="center" label-class-name="zchiDistrict" />
        <el-table-column prop="ZCHI_ADDRESS" label="具体地理位置" width="150" show-overflow-tooltip align="center" label-class-name="zchiAddress" />
        <el-table-column prop="ZCLI_CERTIFICATE_CODE" label="对应土地权属证明编号" width="180" show-overflow-tooltip align="center" label-class-name="zcliCertificateCode" />
        <el-table-column prop="ZCHI_HOUSE_SOURCE" label="取得方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiHouseSource" />
        <el-table-column prop="ZCHI_DATE" label="取得时间" width="150" show-overflow-tooltip align="center" label-class-name="zchiDate" />
        <el-table-column prop="ZCHI_SERVICE_LIFE" label="使用年限（年）" width="150" show-overflow-tooltip align="center" label-class-name="zchiServiceLife" />
        <el-table-column prop="ZCHI_ORIGINAL_VALUE" label="原值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zchiOriginalValue" />
        <el-table-column prop="ZCHI_NET_VALUE" label="净值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zchiNetValue" />
        <el-table-column prop="ZCHI_IF_ASSETS" label="是否两非资产" width="150" align="center" label-class-name="zchiIfAssets" />
        <el-table-column prop="ZCHI_DEPRECIABLE_YEAR" label="折旧年限" width="150" show-overflow-tooltip align="center" label-class-name="zchiDepreciableYear" />
        <el-table-column prop="ZCHI_TOTAL_DEPRECIATION" label="本年计提折旧总额（万元）" width="200" align="center" label-class-name="zchiTotalDepreciation" />
        <el-table-column prop="ZCHI_EVALUATE_DATE" label="最近评估日期" width="150" show-overflow-tooltip align="center" label-class-name="zchiEvaluateDate" />
        <el-table-column prop="ZCHI_EVALUATE_VALUE" label="最近评估价值(万元）" width="180" show-overflow-tooltip align="center" label-class-name="zchiEvaluateValue" />
        <el-table-column prop="ZCHI_AREA" label="房屋总面积(平方米)" width="150" align="center" label-class-name="zchiArea" />
        <el-table-column prop="ZCHI_OFFICE_AREA" label="其中：科研办公面积（平方米）" width="220" align="center" label-class-name="zchiOfficeArea" />
        <el-table-column prop="ZCHI_INDUSTRIAL_AREA" label="其中：工业面积（平方米）" width="220" align="center" label-class-name="zchiIndustrialArea" />
        <el-table-column prop="ZCHI_COMMERCIAL_AREA" label="其中：商业面积（平方米）" width="220" align="center" label-class-name="zchiCommercialArea" />
        <el-table-column prop="ZCHI_RESIDENTIAL_AREA" label="其中：住宅面积（平方米）" width="220" align="center" label-class-name="zchiResidentialArea" />
        <el-table-column prop="ZCHI_OTHER_AREA" label="其中：其他面积（平方米）" width="220" align="center" label-class-name="zchiOtherArea" />
        <el-table-column prop="ZCHI_UNDERGROUND_AREA" label="地下总建筑面积（平方米）" width="220" align="center" label-class-name="zchiUndergroundArea" />
        <el-table-column prop="ZCHI_ONROUND_AREA_SCIENTIFIC" label="科研、办公、联试等面积（平方米）" width="250" align="center" label-class-name="zchiOnroundAreaScientific" />
        <el-table-column prop="ZCHI_ONROUND_AREA_HOTEL" label="住宅酒店等" width="150" show-overflow-tooltip align="center" label-class-name="zchiOnroundAreaHotel" />
        <el-table-column prop="ZCHI_ONROUND_AREA_FACTORY" label="生产厂房面积（平方米）" width="220" align="center" label-class-name="zchiOnroundAreaFactory" />
        <el-table-column prop="ZCHI_ONROUND_AREA_OUTFIELD" label="试验外场面积（平方米）" width="220" align="center" label-class-name="zchiOnroundAreaOutfield" />
        <el-table-column prop="ZCHI_BUSINESS_DIRECTION" label="房屋主要经营方向" width="150" show-overflow-tooltip align="center" label-class-name="zchiBusinessDirection" />
        <el-table-column prop="ZCHI_AREA_ZY" label="其中：自用面积（平方米）" width="220" align="center" label-class-name="zchiAreaZy" />
        <el-table-column prop="ZCHI_AREA_CZ" label="其中：出租面积（平方米）" width="220" align="center" label-class-name="zchiAreaCz" />
        <el-table-column prop="ZCHI_AREA_XZ" label="其中：闲置面积（平方米）" width="220" align="center" label-class-name="zchiAreaXz" />
        <el-table-column prop="ZCHI_IDLE_START_TIME" label="闲置起始时间" width="150" show-overflow-tooltip align="center" label-class-name="zchiIdleStartTime" />
        <el-table-column prop="ZCHI_IDLE_TIME" label="空置时间" width="150" show-overflow-tooltip align="center" label-class-name="zchiIdleTime" />
        <el-table-column prop="ZCHI_VACANCY_REASONS" label="空置原因" width="150" show-overflow-tooltip align="center" label-class-name="zchiVacancyReasons" />
        <el-table-column prop="ZCHI_METHODS" label="建议盘活/处置方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiMethods" />
        <el-table-column prop="ZCHI_RENTAL_PRICE_LASTYEAR" label="上年租赁单价（元/平米/天）" width="220" align="center" label-class-name="zchiRentalPriceLastyear" />
        <el-table-column prop="ZCHI_RENTAL_INCOME_LASTYEAR" label="上年租金收入（万元）" width="180" align="center" label-class-name="zchiRentalIncomeLastyear" />
        <el-table-column prop="ZCHI_RENTAL_PRICE" label="本年租赁单价（元/平米/天）" width="220" align="center" label-class-name="zchiRentalPrice" />
        <el-table-column prop="ZCHI_RENTAL_INCOME_THISYEAR" label="预计本年租金收入（万元）" width="220" align="center" label-class-name="zchiRentalIncomeThisyear" />
        <el-table-column prop="ZCHI_SURROUNDING_SALE_PRICE" label="上年周边可比房产出售单价（元/平方米/月）" width="300" align="center" label-class-name="zchiSurroundingSalePrice" />
        <el-table-column prop="ZCHI_SURROUNDING_RENT_PRICE" label="上年周边可比房产出租单价（元/平方米/月）" width="300" align="center" label-class-name="zchiSurroundingRentPrice" />
        <el-table-column prop="ZCHI_BUSINESS_DZZB" label="电子装备(平方米)" width="150" align="center" label-class-name="zchiBusinessDzzb" />
        <el-table-column prop="ZCHI_BUSINESS_WXTX" label="网信体系(平方米)" width="150" align="center" label-class-name="zchiBusinessWxtx" />
        <el-table-column prop="ZCHI_BUSINESS_CYJC" label="产业基础(平方米)" width="150" align="center" label-class-name="zchiBusinessCyjc" />
        <el-table-column prop="ZCHI_BUSINESS_WLAQ" label="网络安全(平方米)" width="150" align="center" label-class-name="zchiBusinessWlaq" />
        <el-table-column prop="ZCHI_BUSINESS_OTHER" label="其他(平方米)" width="150" align="center" label-class-name="zchiBusinessOther" />
        <el-table-column prop="ZCHI_IF_DISPUTE" label="是否存在纠纷" width="150" show-overflow-tooltip align="center" label-class-name="zchiIfDispute" />
        <el-table-column prop="ZCHI_IF_MORTGAGE" label="是否存在抵押" width="150" show-overflow-tooltip align="center" label-class-name="zchiIfMortgage" />
        <el-table-column prop="ZCHI_MORTGAGE" label="其中:已抵押面积（平方米）" width="220" align="center" label-class-name="zchiMortgage" />
        <el-table-column prop="ZCHI_IF_DISPOSE" label="是否可处置" width="150" align="center" label-class-name="zchiIfDispose" />
        <el-table-column prop="ZCHI_REASON_DISPOSAL" label="不可处置原因" width="150" show-overflow-tooltip align="center" label-class-name="zchiReasonDisposal" />
        <el-table-column prop="ZCHI_DEPT_NAME" label="业务主管部门名称" width="150" show-overflow-tooltip align="center" label-class-name="zchiDeptName" />
        <el-table-column prop="ZCHI_OPERATOR" label="经办人" width="150" show-overflow-tooltip align="center" label-class-name="zchiOperator" />
        <el-table-column prop="ZCHI_OPERATOR_TEL" label="经办人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiOperatorTel" />
        <el-table-column prop="ZCHI_DEPARTMENT_LEADER" label="部门负责人" width="150" show-overflow-tooltip align="center" label-class-name="zchiDepartmentLeader" />
        <el-table-column prop="ZCHI_DEPARTMENT_TEL" label="部门负责人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiDepartmentTel" />
        <el-table-column prop="ZCHI_COMPANY_LEADER" label="分管所(公司)领导" width="150" show-overflow-tooltip align="center" label-class-name="zchiCompanyLeader" />
        <el-table-column prop="ZCHI_COMPANY_TEL" label="分管所(公司)领导联系方式" width="220" show-overflow-tooltip align="center" label-class-name="zchiCompanyTel" />
        <el-table-column prop="ZCHI_REMARK" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zchiRemark" />
        <el-table-column prop="ZCHI_LONGITUDE" label="经度" width="150" show-overflow-tooltip align="center" label-class-name="zchiLongitude" />
        <el-table-column prop="ZCHI_LATITUDE" label="纬度" width="150" show-overflow-tooltip align="center" label-class-name="zchiLatitude" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />

      <BuildingDetailDialog ref="buildingDetail" />
    </div>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard.vue'
import { getIndicatorValueWithParamsPage } from 'api/digitalAssetSystem/assetOverview'
import BuildingDetailDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/buildings/components/BuildingDetailDialog.vue'

export default {
  components: { DialogCard, BuildingDetailDialog },

  data () {
    return {
      dialogVisible: false,
      tableData: [],
      indicatorCode: '',
      zcbiId: '',
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        ZCLI_ASSETS_NO: '',
        ZCLI_CERTIFICATE_CODE: '',
      },
      total: 0
    }
  },

  methods: {

    handleDetail (row) {
      row.zchiId = row.ZCHI_ID
      this.$refs.buildingDetail.showDialog(row)
    },

    handleShow (indicatorCode, zcbiId) {
      this.dialogVisible = true
      this.indicatorCode = indicatorCode
      this.zcbiId = zcbiId
      this.getList()
    },

    getList () {
      getIndicatorValueWithParamsPage({
        indicatorCode: "HOUSE_DYNAMIC_QUERY",
        pageNo: this.searchForm.pageNo,
        pageSize: this.searchForm.pageSize,
        params: {
          year: '2025',
          zcbiId: this.zcbiId,
          companyName: this.searchForm.name,
          zcliCreditCode: this.searchForm.tyxydm,
          ...this.indicatorCode,
        },
        // additionalConditions: {
        //   ...this.indicatorCode,
        //   companyName: this.searchForm.name ? `AND COMPANY_NAME LIKE '%${this.searchForm.name}%'` : '',
        //   zcliCreditCode: this.searchForm.tyxydm ? `AND ZCHI_CREDIT_CODE LIKE '%${this.searchForm.tyxydm}%'` : ''
        // }
      }).then(res => {
        console.log("🚀🚀 ~ 房屋-二级页面 ~ 🚀🚀", res.data.list)
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.getList()
    },

    handleCancel () {
      this.dialogVisible = false
      this.tableData = []
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.getList()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 100%;
}

.leftRight {
  display: flex;
  // justify-content: space-between;
}
</style>
