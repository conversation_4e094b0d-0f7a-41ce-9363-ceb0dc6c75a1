<template>
  <div class="assetMapDetailRight-container">
    <div class="headerCont">
      <el-input
        v-model="searchVal"
        @keydown.enter.native="handleSearchSubmit"
        class="searchIpt"
        placeholder="请输入单位名称"
        style="width: 240px"
      />
      <el-button @click="handleSearchClick">搜索</el-button>
      <el-button @click="handleResetClick">重置</el-button>
    </div>

    <div class="bodyCont">
      <CardBox :title="'单位列表'">
        <div class="exportBtn" slot="rightTitle" @click="handelExportClick">
          <img src="@/assets/external-link-line.png" alt="" >
          导出数据
        </div>
        <el-table v-if="title != '持股比例情况'" border :data="tableData" height="430" style="width: 100%" id="AssetCqAssayDetailRight">
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column align="left" label="单位名称" prop="zcciName" />
          <el-table-column
            align="center"
            label="统一社会信用代码"
            prop="zcciOrganizationCode"
            width="200"
          />
          <el-table-column
            align="center"
            label="成立时间"
            prop="zcciFormatDate"
            width="120"
          />
          <el-table-column
            align="center"
            label="注册资本/开办资金(元) "
            prop="zcciRegisteredCapital"
            width="180"
          />
          <el-table-column
            align="center"
            label="法定代表人"
            prop="zcciArtificialPerson"
            width="120"
          />
          <el-table-column
            align="center"
            label="企业类型"
            prop="zcciEnterpriseType"
            width="120"
          />
          <el-table-column fixed="right" label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="text"
                @click="handleClick(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-table v-else border :data="tableData" height="430" style="width: 100%" id="AssetCqAssayDetailRightTwo">
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column align="left" label="单位名称" prop="companyName" />
          <el-table-column
            align="center"
            label="统一社会信用代码"
            prop="companyCreditCode"
            width="200"
          />
          <el-table-column
            align="center"
            label="成立时间"
            prop="createdTime"
            width="120"
          />
          <el-table-column
            align="center"
            label="企业对外投资企业状态"
            prop="companyCorpStatus"
            width="180"
          />
          <el-table-column
            align="center"
            label="企业对外投资穿透总持股比例"
            prop="companyTotalStockPercent"
            width="120"
          />
          <el-table-column
            align="center"
            label="对外投资穿透总持股比例计算过程"
            prop="companyCalculation"
            width="120"
          />
          <el-table-column fixed="right" label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="text"
                @click="handleClick(scope.row)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          layout="total, sizes, prev, pager, next, jumper"
          :page-size="pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </CardBox>

      <!-- 产权分布侧边栏单位详细信息 -->
      <el-drawer
        class="cqfbDialog_wrapper"
        custom-class="cqfbDialog"
        :modal="false"
        :modal-append-to-body="true"
        size="100%"
        :visible.sync="cqfbDrawerShow"
        :with-header="false"
        :wrapper-closable="false"
      >
        <!-- <CompanyAllInfo
          @closeCqDialog="closeCqDialog"
          :currentCompanyInfo="currentCompanyInfo"
          :year="year"
        /> -->
      </el-drawer>
    </div>
  </div>
</template>

<script>
  import CardBox from '@/views/common/CardBox'
  // import CompanyAllInfo from '@/views/cockpit/assetMap/components/CompanyAllInfo'
  // import { getPropertyRightAnalysisLevel,getPropertyRightAnalysisLevell,getQueryEnterpriseDetails } from '@/api/sam/property'
  // import { exportXLSX } from '@/api/exportExcel'
  export default {
    name: 'AssetCqAssayDetailRight',
    components: {
      CardBox,
      // CompanyAllInfo
    },
    props: ['title','currentName','companyShareholding','year','name'],
    data() {
      return {
        searchVal: '',
        tableData: [],
        pageNo: 1,
        pageSize: 20,
        total: 0,
        cqfbDrawerShow: false,
        queryObj: {},
        left: '',
        right: '',
        currentCompanyInfo: {},
        number: ''
      }
    },
    created() {
      if(this.title == '持股比例情况') {
        this.number = this.name.slice(1, -1).split(",")
        if(this.name == '无') {
          this.left = 0
          this.right = 0
        }else {
          this.left = this.number[0]
          this.right = this.number[1]
        }
      }
      this.getCompanyList()
    },
    methods: {
      // 搜索按钮
      handleSearchClick() {
        this.getCompanyList()
      },
      handleSearchSubmit() {
        if(this.searchVal != '') {
          this.getCompanyList()
        }
      },
      handleSizeChange(val) {
        this.pageSize = val
        this.getCompanyList()
      },
      handleCurrentChange(val) {
        this.pageNo = val
        this.getCompanyList()
      },
      // 重置按钮点击事件
      handleResetClick() {
        this.searchVal = ''
        this.getCompanyList()
      },
      // 获取单位列表
      async getCompanyList() {
       if(this.title == '持股比例情况') {
        let params = {
          zcciName: this.searchVal,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          left: this.left,
          right: this.right
        }
        this.queryObj = Object.assign({},params)
        // let { data } = await getPropertyRightAnalysisLevell(params)
        // this.total = data.total
        // this.tableData = data.list
       }else {
        let params = {
          zcciName: this.searchVal,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          type: this.title,
          start: this.currentName
        }
        this.queryObj = Object.assign({},params)
        // let { data } = await getPropertyRightAnalysisLevel(params)
        // this.total = data.total
        // this.tableData = data.list
       }
      },
      // 关闭产权分布企业详情的侧边栏
      closeCqDialog() {
        this.cqfbDrawerShow = false
      },
      async handleClick(row) {
        if(this.title == '持股比例情况') {
        //  let {data} = await getQueryEnterpriseDetails({companyCreditCode: row.companyCreditCode})
        //  this.currentCompanyInfo  = data
        //   this.cqfbDrawerShow = true
        }else {
          this.currentCompanyInfo = row
          this.cqfbDrawerShow = true
        }

      },
      async handelExportClick() {
       if(this.queryObj.pageSize < this.total) {
        this.queryObj.pageSize = this.total
        // let { data } = await getPropertyRightAnalysisLevel(this.queryObj)
        // this.total = data.total
        // this.tableData = data.list
       }
       this.$nextTick(() => {
        if(this.title == '持股比例情况') {
          // exportXLSX('#AssetCqAssayDetailRightTwo','产权分析单位列表')
      }else {
        // exportXLSX('#AssetCqAssayDetailRight','产权分析单位列表')
      }
       })
       this.getCompanyList()
      }
    },
    watch: {
      currentName() {
        this.searchVal = ''
        this.getCompanyList()
      },
      title() {
        this.searchVal = ''
        this.getCompanyList()
      },
      name(val) {
        if(this.title == '持股比例情况') {
          if(this.name == '无') {
            this.left = 0
            this.right = 0
          }else {
            this.left = val.slice(1, -1).split(",")[0]
            this.right = val.slice(1, -1).split(",")[1]
          }
        }
        this.searchVal = ''
        this.getCompanyList()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .assetMapDetailRight-container {
    width: 100%;
    height: 100%;
    .headerCont {
      width: 100%;
      height: 56px;
      background: #ffffff;
      box-shadow: 0px 8px 20px 0px rgba(177, 197, 197, 0.08);
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      padding: 0 24px;
      line-height: 56px;
      display: flex;
      align-items: center;
      .el-button {
        background: #cc1214 !important;
        border-radius: 0px 4px 4px 0px;
        border: 1px solid #e4e7ed;
        color: #ffffff !important;
      }
      p {
        margin: 0px;
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #303133;
        margin-right: 32px;
      }
    }
    .bodyCont {
      width: 100%;
      height: calc(100% - 24px - 56px);
      margin-top: 24px;
    }
    .cqfbDialog_wrapper {
      width: 50%;
      overflow: hidden !important;
      position: absolute;
      left: 50%;
    }
    .exportBtn {
      width: 122px;
      height: 32px;
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #E4E7ED;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #303133;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>
