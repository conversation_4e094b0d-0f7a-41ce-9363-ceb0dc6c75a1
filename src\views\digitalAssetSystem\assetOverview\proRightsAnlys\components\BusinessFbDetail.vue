<template>
  <div class="businessFbDetail-container">
    <div id="chinaMap" style="width: 100%; height: 495px"></div>
  </div>
</template>

<script>
  // import {searchzcciAddress} from '@api/sam/property.js'
  import chinaJson from '@/assets/digitalAssetSystem/china.json'
  import * as echarts from 'echarts'
  export default {
    name: "BusinessFbDetail",
    components: {},
    data() {
      return {
        tableDataProvince: []
      }
    },
    created() {
      this.initCharts()
      this.getsearchzcciAddress()
    },
    methods: {
      initCharts() {
        echarts.registerMap('china', chinaJson)
      },
      async getsearchzcciAddress() {
        let params = {
          year: new Date().getFullYear(),
          start: 0
        }
        // let { data } = await searchzcciAddress(params)
        //   this.tableDataProvince = data
        //   const mapData = []
        //   for (let i = 0; i < data.length; i++) {
        //     const params = {
        //       value: data[i].zcciAddressCount,
        //       name: data[i].zcciAddress,
        //     }
        //     mapData.push(params)
        //   }
        //   this.chinaMap(mapData)
      },
      chinaMap(data) {
        const chinaMap = echarts.init(document.getElementById('chinaMap'))
        this.map(chinaMap, data)
      },
      map(mychart, data) {
        const option = {
          tooltip: {
            trigger: 'item',
          },
          visualMap: {
            min: 0,
            max: 200,
            left: 'left',
            top: 'bottom',
            text: ['高', '低'], // 文本，默认为数值文本
            calculable: true,
            inRange: {
              color: ['#5BA7F8', '#FFDF80', '#FFB200', '#FF6500', '#B22C00'],
            },
            padding: [50, 50, 50, 20],
          },
          toolbox: {
            show: false,
            orient: 'vertical',
            left: 'right',
            top: 'center',
            feature: {
              dataView: { readOnly: false },
              restore: {},
              saveAsImage: {},
            },
          },
          /*           geo: [
              {
                map: 'china',
                regions: [
                  {
                    name: '南海诸岛',
                    itemStyle: {
                      // 隐藏地图
                      normal: {
                        opacity: 0, // 为 0 时不绘制该图形
                      },
                    },
                    label: {
                      show: false, // 隐藏文字
                    },
                  },
                ],
              },
            ], */
          series: [
            {
              name: '户数',
              type: 'map',
              map: 'china',
              zoom: 1.2,
              roam: false,
              label: {
                normal: {
                  show: true,
                },
                emphasis: {
                  show: true,
                },
              },
              data: data,
            },
          ],
        }
        mychart.setOption(option)
        mychart.on('click', param => {
          this.$router.push({
            path: '/views/cockpit/assetOverview/components/AssetMapDetail',
            query: {
              year: this.year,
              city: param.name,
              index:param.dataIndex,
              text: '产权分布',
              type: '0',
              name: '产权分析',
              data: JSON.stringify(data)
            }
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>