<template>
  <div class="capability-analysis-container">
    <!-- 顶部标题 + 右侧单位选择 -->
    <div class="page-header">
      <div class="capability-levels-info">
        <div class="levels-grid">
          <div
            v-for="(level, index) in capabilityLevels"
            :key="level.level"
            :class="['level-item', getLevelColorClass(index)]"
            :title="level.description"
          >
            <div class="level-title">{{ level.level }}({{ level.name }})</div>
            <div class="level-count"><div>{{ level.count }}</div> <div style="color: black;font-size: 15px;margin: 0 0 3px 5px">个</div> </div>
            <!--              <div v-if="level.description" class="level-description">{{ level.description }}</div>-->
          </div>
        </div>
      </div>
      <div class="header-actions">
        <!-- 二级成员单位下拉列表（替换原 SelectTree） -->
        <el-select
          v-model="zcbiId"
          placeholder="请选择单位"
          style="width: 100%"
          size="small"
          :disabled="unitDisabled"
          clearable
          filterable
          @change="handleZcbiChange"
        >
          <el-option
            v-for="(item, idx) in ejOptions"
            :key="item.zcbiId || idx"
            :label="item.zcbiName"
            :value="item.zcbiId"
          />
        </el-select>
      </div>
    </div>

    <div class="capability-stats">
      <div class="stats-row">
        <CardBox title="能力及资产情况" class="stats-card-box">
          <div class="stats-grid">
            <div class="stat-item cyan">
              <div class="stat-icon">
                <i class="el-icon-office-building" />
              </div>
              <div class="stat-content" @click="openCapabilityDetail(topData[0].root_category)">
                <div class="stat-title">{{topData[0].root_category}}</div>
                <div class="stat-number">工具<span class="highlight-red">{{topData[0].t4_tjz || 0}}</span>套</div>
                <div class="stat-detail">涉及资产数量<span class="highlight-red">{{topData[0].asset_tjz || 0}}</span></div>
              </div>
            </div>

            <div class="stat-item green">
              <div class="stat-icon">
                <i class="el-icon-cpu" />
              </div>
              <div class="stat-content" @click="openCapabilityDetail(topData[1].root_category)">
                <div class="stat-title">{{ topData[1].root_category }}</div>
                <div class="stat-number">工具<span class="highlight-red">{{topData[1].t4_tjz || 0}}</span>套</div>
                <div class="stat-detail">涉及资产数量<span class="highlight-red">{{topData[1].asset_tjz || 0}}</span></div>
              </div>
            </div>

            <div class="stat-item blue">
              <div class="stat-icon">
                <i class="el-icon-goods" />
              </div>
              <div class="stat-content" @click="openCapabilityDetail(topData[2].root_category)">
                <div class="stat-title">{{ topData[2].root_category }}</div>
                <div class="stat-number">产线<span class="highlight-red">{{topData[2].t4_tjz || 0}}</span>条</div>
                <div class="stat-detail">涉及资产数量<span class="highlight-red">{{topData[2].asset_tjz || 0}}</span></div>
              </div>
            </div>

            <div class="stat-item orange">
              <div class="stat-icon">
                <i class="el-icon-data-line" />
              </div>
              <div class="stat-content" @click="openCapabilityDetail(topData[3].root_category)">
                <div class="stat-title">{{ topData[3].root_category }}</div>
                <div class="stat-number">环境<span class="highlight-red">{{topData[3].t4_tjz || 0}}</span>个</div>
                <div class="stat-detail">涉及资产数量<span class="highlight-red">{{topData[3].asset_tjz || 0}}</span></div>
              </div>
            </div>

            <div class="stat-item blue-secondary">
              <div class="stat-icon">
                <i class="el-icon-connection" />
              </div>
              <div class="stat-content" @click="openCapabilityDetail(topData[4].root_category)">
                <div class="stat-title">{{ topData[4].root_category }}</div>
                <div class="stat-number">工具<span class="highlight-red">{{topData[4].t4_tjz || 0}}</span>套</div>
                <div class="stat-detail">涉及资产数量<span class="highlight-red">{{topData[4].asset_tjz || 0}}</span></div>
              </div>
            </div>

            <div class="stat-item green-secondary">
              <div class="stat-icon">
                <i class="el-icon-data-analysis" />
              </div>
              <div class="stat-content" @click="openCapabilityDetail(topData[5].root_category)">
                <div class="stat-title">{{ topData[5].root_category }}</div>
                <div class="stat-number">工具<span class="highlight-red">{{topData[5].t4_tjz || 0}}</span>套</div>
                <div class="stat-detail">涉及资产数量<span class="highlight-red">{{topData[5].asset_tjz || 0}}</span></div>
              </div>
            </div>
          </div>
        </CardBox>

        <CardBox title="能力批复及形成情况" class="stats-card-box">
          <div class="province-stats">
            <div class="province-item purple">
              <div class="province-icon">
                <i class="el-icon-data-board" />
              </div>
              <div class="province-content">
                <div class="province-title">已批复能力<span class="highlight-red">0</span>项</div>
              </div>
            </div>

            <div class="province-item blue-light">
              <div class="province-icon">
                <i class="el-icon-monitor" />
              </div>
              <div class="province-content">
                <div class="province-title">已形成能力<span class="highlight-red">0</span>项</div>
              </div>
            </div>

            <div class="province-item teal">
              <div class="province-icon">
                <i class="el-icon-connection" />
              </div>
              <div class="province-content">
                <div class="province-title">已验收能力<span class="highlight-red">0</span>项</div>
              </div>
            </div>
          </div>
        </CardBox>
      </div>
    </div>

    <!-- 中间内容区域 -->
    <div class="content-area">
      <!-- 左侧饼图 -->
      <div class="left-section">
        <CardBox title="六大类下属能力单元数量统计" class="chart-card-box">
          <div class="pie-chart">
            <CapabilityPieChart ref="capabilityPieChart" :zcbiId="zcbiId" />
          </div>
        </CardBox>
      </div>

      <!-- 右侧地图和排名 -->
      <div class="right-section">
        <CardBox title="能力单元（T4）分布情况" class="right-section">
          <!-- 能力检索级联选择器 -->
          <div class="capability-search-container" slot="rightTitle">
            <span style="margin-right: 50px;">涉及能力单元{{nldyNumber}}个，单位{{unitNumber}}个</span>
            <div class="search-label">能力检索</div>
            <div class="search-select">
              <el-cascader
                v-model="selectedCapabilityPath"
                :options="cascaderOptions"
                :props="cascaderProps"
                placeholder="请选择"
                size="small"
                style="width: 300px;"
                filterable
                clearable
                :loading="treeSelectLoading"
                @change="handleCascaderChange"
                :show-all-levels="false"
              />
            </div>
          </div>
          <div class="botWrap">
            <div class="map-container">
              <MapChart ref="mapChart" @updateNumber="getNumber"/>
            </div>

            <!-- 排名表格 -->
            <div class="ranking-container">
              <div class="chart-title">
                <div class="subtxt">
                  <img style="width: 20px;height: 12px" src="@/assets/common_images/icon_zs.png" alt="">
                  能力建设排名前十单位
                </div>
                <span class="more-link" @click="showMore">更多 ></span>
              </div>
              <div class="ranking-table">
                <el-table :data="rankingData" :show-header="true" size="small">
                  <el-table-column prop="rank" label="排名" width="50" align="center" />
                  <el-table-column prop="name" label="单位名称" show-overflow-tooltip />
                  <el-table-column prop="count" label="能力单元" width="80" align="center" />
                </el-table>
              </div>
            </div>
          </div>
        </CardBox>
      </div>
    </div>

    <!-- 底部对比表格 -->
    <div class="comparison-table-container">
      <CardBox title="待建设能力情况">
        <span slot="rightTitle">{{ nlzlNumber }}</span>
        <el-table :data="comparisonData" border :span-method="spanMethod">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="t1" label="大类(T1)" show-overflow-tooltip align="center" />
          <el-table-column prop="t2" label="领域(T2)" show-overflow-tooltip align="center" />
          <el-table-column prop="t3" label="子类(T3)" show-overflow-tooltip align="center" />
        </el-table>
      </CardBox>
    </div>

    <MoreDialog ref="moreDialog" :tciCategory="currentTciCategory" :zcbiId="zcbiId" />
    <CapabilityDetailDialog ref="capabilityDetailDialog" />

  </div>
</template>

<script>
import CardBox from '@/views/common/CardBox'
import CapabilityPieChart from '@/views/digitalAssetSystem/capabilityMap/capabilityAnalysis/components/CapabilityPieChart.vue'
import MapChart from '@/views/digitalAssetSystem/capabilityMap/capabilityAnalysis/components/MapChart.vue'
import MoreDialog from '@/views/digitalAssetSystem/capabilityMap/capabilityAnalysis/components/MoreDialog.vue'
import {
  getCapabilityT4New,
  getCapabilityIndexTreeLimited,
  getCompanyT4,
  getNlzlNumber,
  getNljgNumber, getCapabilityIndexTreeDjsnlqk
} from '@/api/digitalAssetSystem/capabilityIndex'
import CapabilityDetailDialog from '@/views/digitalAssetSystem/capabilityMap/capabilityAnalysis/components/CapabilityDetailDialog.vue'

import { searchSecoundOrgInfo } from '@/api/common'
import { mapGetters } from 'vuex'


export default {
  components: {
    CardBox,
    CapabilityPieChart,
    MapChart,
    MoreDialog,
    CapabilityDetailDialog
  },
  data () {
    return {
      titleInfo: "T1是大类(需求论证能力、研发设计能力、生产制造能力、试验论证能力、运维保障能力、信息基础和设施支撑能力)，T2是领域(0个)，T3是子类(0个)，T4是能力单元(0个)",
      // 能力层级统计信息
      capabilityLevels: [
        { level: '大类', name: 'T1', description: '需求论证能力、研发设计能力、生产制造能力、试验论证能力、运维保障能力、信息基础和设施支撑能力', count: 6 },
        { level: '领域', name: 'T2', description: '', count: 0 },
        { level: '子类', name: 'T3', description: '', count: 0 },
        { level: '能力单元', name: 'T4', description: '', count: 0 }
      ],
      // 能力检索级联选择器
      selectedCapabilityPath: [], // 级联选择器选中的路径数组
      currentTciCategory: '需求论证能力', // 供 MapChart/MoreDialog 联动
      // 级联选择器相关数据
      treeSelectLoading: false,
      originalTreeData: [], // 原始树形数据
      cascaderOptions: [], // 级联选择器选项数据
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        expandTrigger: 'click',
        emitPath: false, // 只返回最后一级的值
        checkStrictly: true // 允许选择任意层级的节点，不仅限于叶子节点
      },

      // 二级成员单位下拉
      ejOptions: [], // 接口返回的二级成员单位列表（res.data.children）
      zcbiId: '', // 选中的单位ID
      zcbiName: '', // 选中的单位名称
      unitDisabled: false,

      topData: [],
      // 排名数据
      rankingData: [
      ],

      // 对比表格数据
      comparisonData: [],
      nldyNumber: 0,
      unitNumber: 0,
      nlzlNumber: ""
    }
  },
  computed: {
    ...mapGetters({
      loginUser: 'user/loginUser'
    }),
  },

  mounted () {
    this.init()
  },
  methods: {
    // 获取能力层级的颜色样式类
    getLevelColorClass(index) {
      const colorClasses = ['cyan', 'green', 'blue', 'orange']
      return colorClasses[index % colorClasses.length]
    },

    getNumber(res) {
      this.nldyNumber = res.nldyNumber
      this.unitNumber = res.unitNumber
    },

    init() {
      console.log(111);
      // 加载单位树并根据角色设置默认与禁用状态
      this.fetchEjOptions()
      this.setupUnitByRole()
      this.loadMapChartData()
    },

    loadMapChartData() {
      // 地图默认加载一个能力类别（可根据需要调整或移除）
      if (this.$refs.mapChart && typeof this.$refs.mapChart.fetchMapData === 'function') {
        this.$refs.mapChart.fetchMapData(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ tciCategory: this.currentTciCategory, zcbiId: this.zcbiId }:{ tciCategory: this.currentTciCategory })
        // this.$refs.mapChart.fetchMapData({ tciCategory: this.currentTciCategory })
      } else {
        setTimeout(() => {
          this.loadMapChartData()
        },100)
      }
    },

    // 打开“能力详情”弹窗，展示与该能力类别相关的表格信息
    openCapabilityDetail (name) {
      if (this.$refs.capabilityDetailDialog && typeof this.$refs.capabilityDetailDialog.show === 'function') {
        this.$refs.capabilityDetailDialog.show(name, this.zcbiId)
        // this.$refs.capabilityDetailDialog.show(name)
      }
    },

    async loadTopData () {
      // 为六大类能力统计数据接口添加 zcbiId 参数
      getCapabilityT4New(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ zcbiId: this.zcbiId }:{}).then(res => {
      // getCapabilityT4New().then(res => {
        // console.log("🚀🚀 ~ 数据1 ~ 🚀🚀", res)
        this.topData = res.data
      })
    },

    /**
     * 加载能力指标树数据
     */
    async loadCapabilityTreeData () {
      try {
        this.treeSelectLoading = true
        // 使用与 capSysManage 相同的接口，添加 zcbiId 参数
        const response = await getCapabilityIndexTreeLimited(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ zcbiId: this.zcbiId }:{})
        // const response = await getCapabilityIndexTreeLimited()

        if (response && response.code == 200 && response.data) {
          this.originalTreeData = response.data
          this.cascaderOptions = this.transformTreeToCascaderOptions(response.data)
          // console.log('成功加载能力指标树数据:', response)

          // 设置默认选中"需求论证能力"
          this.setDefaultSelection()
        } else {
          this.$message.warning('暂无能力指标数据')
          // 设置默认选中
          this.setDefaultSelection()
        }
      } catch (error) {
        console.error('加载能力指标数据失败:', error)
        this.$message.error('加载能力指标数据失败，请稍后重试')
      } finally {
        this.treeSelectLoading = false
      }
    },

    // 加载二级成员单位（Ej）下拉选项
    async fetchEjOptions () {
      try {
        const { data } = await searchSecoundOrgInfo()
        this.ejOptions = [data,...data.children]
      } catch (e) {
        console.error('加载二级成员单位失败:', e)
        this.ejOptions = []
      }
    },

    // 处理二级成员单位选择变化
    handleZcbiChange (val) {
      const found = this.ejOptions.find(i => i.zcbiId === val)
      this.zcbiId = val || ''
      this.zcbiName = found ? found.zcbiName : ''
      this.refreshAllData()
    },

    // 根据用户角色设置默认选择与禁用状态
    setupUnitByRole () {
      const roles = (this.loginUser && Array.isArray(this.loginUser.roles.split(","))) ? this.loginUser.roles.split(",") : []
      // 根据当前登录用户默认单位尝试匹配下拉选项
      const userOdId = this.loginUser.odId || ''
      const userOdName = this.loginUser.odName || ''
      const matchById = this.ejOptions.find(i => i.zcbiId === userOdId)
      const matchByName = this.ejOptions.find(i => i.zcbiName === userOdName)
      const match = matchById || matchByName || this.ejOptions[0]    //匹配不到默认选中第一个
      this.zcbiId = match ? match.zcbiId : ''
      this.zcbiName = match ? match.zcbiName : ''
      const isMemberUnit = roles.includes('ZcMemberUnit')

       // 加载能力指标树数据
       this.loadCapabilityTreeData()
       this.loadTopData()
       this.loadNljspmqbUnit(this.currentTciCategory)
       this.loadNlNumber()
       this.getTotalNumberCount()
       this.loadDjsnl()

      // 级联选择器的默认选中将在数据加载完成后设置
      if (isMemberUnit) {
        this.unitDisabled = true
      } else {
        this.unitDisabled = false
      }
    },

    loadNlNumber() {
      getNlzlNumber(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{zcbiId: this.zcbiId}:{}).then(res => {
      // getNlzlNumber().then(res => {
        // this.nlzlNumber = "目前共有子类"+ res.data.t3Count || 0 +"个，待建设能力子类"+ res.data.totalCount || 0 + "个";
        this.nlzlNumber = "目前共有"+ (res.data.t2WithoutT3ButWithT4Count || 0) + "个领域（T2）、"+ (res.data.t3WithoutT4Count || 0) +"个子类（T3）能力待建设";
      })
    },

    loadDjsnl() {
      getCapabilityIndexTreeDjsnlqk(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ zcbiId: this.zcbiId }:{}).then(response => {
      // getCapabilityIndexTreeDjsnlqk().then(response => {
        // 构造comparisonData模拟数据
        this.comparisonData = this.generateComparisonDataFromTree(response.data)
      })
    },

    loadNljspmqbUnit (name) {
      // 为排名数据接口添加 zcbiId 参数
      getCompanyT4(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ tciCategory: name, pageSize: 10, pageNo: 1, zcbiId: this.zcbiId }:{tciCategory: name, pageSize: 10, pageNo: 1}).then(res => {
      // getCompanyT4({ tciCategory: name, pageSize: 10, pageNo: 1}).then(res => {
        this.rankingData = res.data.records.map((item,i) => ({
          rank: i + 1,
          name: item.zcbi_name,
          count: item.tjz,
        }))
      })
    },

    getTotalNumberCount() {
      getNljgNumber(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{zcbiId: this.zcbiId}:{}).then(res => {
      // getNljgNumber().then(res => {
        this.titleInfo = "T1是大类(需求论证能力、研发设计能力、生产制造能力、试验论证能力、运维保障能力、信息基础和设施支撑能力)，T2是领域("+res.data[1].tjz+"个)，T3是子类("+res.data[2].tjz+"个)，T4是能力单元("+res.data[3].tjz+"个)"

        // 更新能力层级统计信息
        this.capabilityLevels[1].count = res.data[1].tjz || 0
        this.capabilityLevels[2].count = res.data[2].tjz || 0
        this.capabilityLevels[3].count = res.data[3].tjz || 0
      })
    },

    showMore () {
      this.$refs.moreDialog.show()
    },

    /**
     * 将树形数据转换为级联选择器所需的格式
     */
    transformTreeToCascaderOptions (treeData) {
      if (!treeData || !Array.isArray(treeData) || treeData.length === 0) {
        return []
      }

      // 递归处理树形数据
      const processNode = (node, parentCategory = '') => {
        const nodeLabel = node.tciCategory || node.label || '未命名指标'
        const nodeValue = node.tciId || node.id || `node-${Math.random()}`
        // 优先使用节点自身的分类，如果没有则使用父级分类，最后使用节点标签
        const nodeCategory = node.tciCategory || parentCategory || nodeLabel

        const option = {
          value: nodeValue,
          label: nodeLabel,
          category: nodeCategory, // 保存分类信息用于后续查询
          rawData: node
        }

        // 递归处理子节点，传递当前节点的分类作为父级分类
        if (node.children && Array.isArray(node.children) && node.children.length > 0) {
          option.children = node.children.map(child => processNode(child, nodeCategory))
        }

        return option
      }

      // 处理所有根节点
      return treeData.map(rootNode => processNode(rootNode))
    },

    /**
     * 从树状数据构造comparisonData
     * 提取每层数据的tciCategory字段，构造成对比表格数据
     */
    generateComparisonDataFromTree (treeData) {
      if (!treeData || !Array.isArray(treeData) || treeData.length === 0) {
        this.comparisonData = []
        return this.comparisonData // 返回默认数据
      }

      const comparisonList = []

      // 遍历树状数据，按层级提取tciCategory
      treeData.forEach(t1Node => {
        const t1Category = t1Node.tciCategory || t1Node.tciIndicatorName || '一级能力'
        let isFirstT1Row = true // 标记是否是T1分组的第一行

        if (t1Node.children && t1Node.children.length > 0) {
          // 处理T2级别
          t1Node.children.forEach(t2Node => {
            const t2Category = t2Node.tciCategory || t2Node.tciIndicatorName || '二级能力'
            let isFirstT2Row = true // 标记是否是T2分组的第一行

            if (t2Node.children && t2Node.children.length > 0) {
              // 处理T3级别
              t2Node.children.forEach(t3Node => {
                const t3Category = t3Node.tciCategory || t3Node.tciIndicatorName || '三级能力'

                comparisonList.push({
                  t1: isFirstT1Row ? t1Category : '', // 只有第一行显示T1，其他为空用于合并
                  t2: isFirstT2Row ? t2Category : '', // 只有第一行显示T2，其他为空用于合并
                  t3: t3Category
                })

                isFirstT1Row = false
                isFirstT2Row = false
              })
            } else {
              // T2没有子节点，直接添加T2数据
              comparisonList.push({
                t1: isFirstT1Row ? t1Category : '',
                t2: t2Category,
                t3: '-'
              })

              isFirstT1Row = false
            }
          })
        } else {
          // T1没有子节点，直接添加T1数据
          comparisonList.push({
            t1: t1Category,
            t2: '-',
            t3: '-'
          })
        }
      })

      // 如果没有生成数据，返回默认数据
      return comparisonList.length > 0 ? comparisonList : this.comparisonData
    },

    /**
     * 处理级联选择器变化
     */
    handleCascaderChange (value) {
      console.log('级联选择器选中值:', value)

      if (!value) {
        // 清空选择时重置为默认状态
        this.currentTciCategory = '需求论证能力'
        this.updateRelatedComponents({ label: '需求论证能力', category: '需求论证能力' })
        return
      }

      // 根据选中的值查找对应的节点数据
      const selectedNode = this.findNodeByValue(value, this.cascaderOptions)

      if (selectedNode) {
        console.log('选中的能力指标详情:', selectedNode)
        console.log('选中层级:', this.getNodeLevel(selectedNode))

        // 更新当前能力分类
        this.currentTciCategory = selectedNode.category || selectedNode.label || '需求论证能力'

        // 更新相关组件
        this.updateRelatedComponents(selectedNode)
      }
    },

    /**
     * 获取节点层级（用于调试和日志）
     */
    getNodeLevel (node) {
      if (!node) return 0

      // 根据节点的value或其他特征判断层级
      if (node.value && node.value.includes('-capability')) {
        return 1 // 一级节点
      } else if (node.children && node.children.length > 0) {
        return 2 // 二级节点
      } else {
        return 3 // 三级节点（叶子节点）
      }
    },

    /**
     * 根据值在级联选择器选项中查找节点
     */
    findNodeByValue (value, options) {
      for (const option of options) {
        if (option.value === value) {
          return option
        }
        if (option.children && option.children.length > 0) {
          const found = this.findNodeByValue(value, option.children)
          if (found) {
            return found
          }
        }
      }
      return null
    },

    /**
     * 根据分类名称查找节点
     */
    findNodeByCategory (category, options) {
      for (const option of options) {
        if (option.category === category || option.label === category) {
          return option
        }
        if (option.children && option.children.length > 0) {
          const found = this.findNodeByCategory(category, option.children)
          if (found) {
            return found
          }
        }
      }
      return null
    },

    /**
     * 设置默认选中状态
     */
    setDefaultSelection () {
      // 查找"需求论证能力"对应的节点
      const defaultNode = this.findNodeByCategory(this.currentTciCategory, this.cascaderOptions)

      if (defaultNode) {
        // 设置级联选择器的选中值
        this.selectedCapabilityPath = defaultNode.value
        console.log('设置默认选中:', defaultNode)

        // 更新当前分类
        this.currentTciCategory = defaultNode.category || defaultNode.label || '需求论证能力'

        // 不需要调用 updateRelatedComponents，因为初始化时其他组件会自动加载默认数据
      } else {
        console.warn('未找到"需求论证能力"对应的节点，使用空值')
        this.selectedCapabilityPath = []
      }
    },

    /**
     * 根据选中的能力指标更新相关组件
     */
    updateRelatedComponents (selectedNode) {
      // 这里可以实现根据选中的能力指标更新页面其他部分的逻辑
      // 例如：
      // 1. 更新地图显示
      // 2. 更新排名数据
      // 3. 更新统计数据等

      console.log('更新相关组件数据:', selectedNode)
      // 当前能力分类用于联动地图与“更多”弹窗
      this.currentTciCategory = selectedNode.category || selectedNode.label || ''

      // 地图更新，传入 zcbiId 参数
      if (this.$refs.mapChart && typeof this.$refs.mapChart.fetchMapData === 'function') {
        this.$refs.mapChart.fetchMapData(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ tciCategory: this.currentTciCategory, zcbiId: this.zcbiId }:{ tciCategory: this.currentTciCategory })
        // this.$refs.mapChart.fetchMapData({ tciCategory: this.currentTciCategory })
      }

      // 更新排名数据
      this.loadNljspmqbUnit(this.currentTciCategory)
    },

    /**
     * 刷新所有数据（单位切换时调用）
     */
    refreshAllData () {
      // 重新加载顶部统计数据
      this.loadTopData()

      // 重新加载能力指标树数据
      this.loadCapabilityTreeData()

      this.loadDjsnl()

      // 重新加载排名数据
      this.loadNljspmqbUnit(this.currentTciCategory)

      this.loadNlNumber()

      this.getTotalNumberCount()

      // 通知饼图组件更新数据（通过 props 变化自动触发）
      this.$nextTick(() => {
        if (this.$refs.capabilityPieChart && typeof this.$refs.capabilityPieChart.loadData === 'function') {
          this.$refs.capabilityPieChart.loadData()
        }
      })

      // 更新地图数据
      if (this.$refs.mapChart && typeof this.$refs.mapChart.fetchMapData === 'function') {
        this.$refs.mapChart.fetchMapData(this.zcbiId != "62B9AC49C9564F2AB9C1464A164DDBEE"?{ tciCategory: this.currentTciCategory, zcbiId: this.zcbiId }:{tciCategory: this.currentTciCategory})
        // this.$refs.mapChart.fetchMapData({ tciCategory: this.currentTciCategory })
      }
    },
    // 单元格合并方法
    spanMethod ({ row, rowIndex, columnIndex }) {
      // 只对T1列（第2列，索引为1）进行合并
      if (columnIndex === 1) {
        // 获取当前行的T1值
        const currentValue = row.t1

        // 如果当前行的T1值为空，则不显示该单元格
        if (!currentValue) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }

        // 计算需要合并的行数
        let rowspan = 1

        // 向下查找连续的空值（属于同一个T1分组）
        for (let i = rowIndex + 1; i < this.comparisonData.length; i++) {
          const nextRow = this.comparisonData[i]
          // 如果下一行的T1为空，说明属于当前T1分组，继续合并
          if (!nextRow.t1) {
            rowspan++
          } else {
            // 如果下一行有T1值，停止合并
            break
          }
        }

        return {
          rowspan: rowspan,
          colspan: 1
        }
      }

      // 其他列不进行合并
      return {
        rowspan: 1,
        colspan: 1
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.capability-analysis-container {
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px 16px;
  font-weight: bolder;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 25px;
}

/* 能力层级信息样式 */
.capability-levels-info {
  flex: 1 1 auto;
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.level-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;

  &.cyan {
    background-color: rgba(23, 162, 184, 0.1);
  }

  &.green {
    background-color: rgba(40, 167, 69, 0.1);
  }

  &.blue {
    background-color: rgba(0, 123, 255, 0.1);
  }

  &.orange {
    background-color: rgba(253, 126, 20, 0.1);
  }
}

.level-content {
  flex: 1;
}

.level-title {
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 600;
}

.level-count {
  font-size: 16px;
  color: #CC1214;
  font-weight: bold;
  margin: 0 5px 0 5px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.level-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  word-break: break-all;
}
.header-actions {
  flex: 0 0 360px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 能力数据统计卡片样式 */
.capability-stats {
  margin-bottom: 20px;
}

.stats-row {
  display: grid;
  grid-template-columns: 75% 25%;
  gap: 20px;
}

.stats-card-box,
.chart-card-box {
  flex: 1;
  display: flex;
  flex-direction: column;

  ::v-deep .card-body {
    width: 100%;
    flex: 1;
  }
}

/* 能力数据产品情况样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px;
  height: 100%;
}

.stat-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background: #f8f9fa;

  &.cyan {
    background-color: rgba(23, 162, 184, 0.1);
    .stat-icon {
      background: rgba(23, 162, 184, 0.1);
      color: #17a2b8;
    }
  }

  &.green {
    background-color: rgba(40, 167, 69, 0.1);
    .stat-icon {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
    }
  }

  &.blue {
    background-color: rgba(0, 123, 255, 0.1);
    .stat-icon {
      background: rgba(0, 123, 255, 0.1);
      color: #CC1214;
    }
  }

  &.orange {
    background-color: rgba(253, 126, 20, 0.1);
    .stat-icon {
      background: rgba(253, 126, 20, 0.1);
      color: #fd7e14;
    }
  }

  &.blue-secondary {
    background-color: rgba(111, 66, 193, 0.1);
    .stat-icon {
      background: rgba(111, 66, 193, 0.1);
      color: #6f42c1;
    }
  }

  &.green-secondary {
    background-color: rgba(32, 201, 151, 0.1);
    .stat-icon {
      background: rgba(32, 201, 151, 0.1);
      color: #20c997;
    }
  }
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  i {
    font-size: 20px;
  }
}

.stat-content {
  flex: 1;
  cursor: pointer;
}

.stat-title {
  font-size: 15px;
  color: #000;
  margin-bottom: 8px;
  font-weight: bolder;
}

.stat-number {
  font-size: 15px;
  color: #000;
  margin-bottom: 10px;
  font-weight: bolder;
}

.stat-detail {
  font-size: 15px;
  color: #000;
  font-weight: bolder;
}

.highlight-red {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
}

/* 能力数据各省市情况样式 */
.province-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.province-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background: #f8f9fa;

  &.purple {
    background-color: rgba(111, 66, 193, 0.1);
    .province-icon {
      background: rgba(111, 66, 193, 0.1);
      color: #6f42c1;
    }
  }

  &.blue-light {
    background-color: rgba(23, 162, 184, 0.1);
    .province-icon {
      background: rgba(23, 162, 184, 0.1);
      color: #17a2b8;
    }
  }

  &.teal {
    background-color: rgba(32, 201, 151, 0.1);
    .province-icon {
      background: rgba(32, 201, 151, 0.1);
      color: #20c997;
    }
  }
}

.province-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  i {
    font-size: 20px;
  }
}

.province-content {
  flex: 1;
}

.province-title {
  font-size: 15px;
  color: #000;
  margin-bottom: 4px;
  font-weight: bolder;
}

/* 中间内容区域 */
.content-area {
  display: grid;
  grid-template-columns: 32% 68%;
  gap: 20px;
  margin-bottom: 20px;
}

.left-section {
  width: 100%;
}

.pie-chart {
  width: 100%;
  height: 100%;
  overflow: visible;
}

.right-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  box-shadow: 0px 8px 20px 0px rgba(177, 197, 197, 0.08);
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  box-sizing: border-box;
  //padding: 16px;

  .botWrap {
    width: 100%;
    height: 100%;
    background-color: #f8f8f8;
    display: grid;
    grid-template-columns: 75% 25%;
  }
}

/* 能力检索选择框样式 */
.capability-search-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #000;
  font-weight: 500;
}

.search-select {
  ::v-deep .el-cascader {
    .el-input__inner {
      border-color: #dcdfe6;
      border-radius: 4px;

      &:focus {
        border-color: #409eff;
      }
    }

    .el-cascader-panel {
      .el-cascader-menu {
        .el-cascader-node {
          padding: 8px 12px;

          &:hover {
            background-color: #f5f7fa;
          }

          &.is-active {
            color: #409eff;
            font-weight: 600;
            background-color: #e6f7ff;
          }

          &.is-selectable {
            cursor: pointer;

            &:hover {
              background-color: #f0f9ff;
            }
          }

          .el-cascader-node__label {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .el-cascader-node__postfix {
            .el-icon-check {
              color: #409eff;
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  // 兼容原有的 el-select 样式（如果还有其他地方使用）
  ::v-deep .el-select {
    .el-input__inner {
      border-color: #dcdfe6;
      border-radius: 4px;

      &:focus {
        border-color: #409eff;
      }
    }

    .el-select-dropdown {
      .el-select-group__title {
        color: #409eff;
        font-weight: 600;
        font-size: 13px;
        background-color: #f5f7fa;
        padding: 8px 12px;
        border-bottom: 1px solid #e4e7ed;
      }

      .el-option {
        padding: 8px 12px;

        &.is-disabled {
          color: #c0c4cc;
          cursor: not-allowed;
        }

        &:hover:not(.is-disabled) {
          background-color: #f5f7fa;
        }

        &.selected {
          color: #409eff;
          font-weight: 600;
          background-color: #e6f7ff;
        }

        span {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.map-container {
  width: 100%;
  height: 100%;
}

.ranking-container {
  width: 140%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 12px 8px 12px 0;
  position: relative;
  right: 110px;
}

.chart-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #cc1214;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
  font-weight: 600;
  .subtxt {
    display: flex;
    align-items: center;
    font-size: 16px;
  }

  img {
    margin-right: 8px;
  }
}

.more-link {
  color: #a3a3a3;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    text-decoration: underline;
  }
}

/* 排名表格 */
.ranking-table {
  flex: 1;

  ::v-deep .el-table {
    font-size: 12px;
    height: 100%;

    .el-table__header-wrapper {
      th {
        background-color: #eee;
        color: #000;
        padding: 8px 0;
        font-size: 12px;
      }
    }

    .el-table__body-wrapper {
      td {
        padding: 6px 0;
        font-size: 12px;
      }

      .cell {
        padding: 0 8px;
        line-height: 1.4;
      }
    }

    .el-table__row {
      height: auto;
    }
  }
}

/* 底部对比表格 */
.comparison-table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  ::v-deep .el-table {
    .el-table__header-wrapper {
      th {
        background-color: #f8f9fa;
        color: #666;
        font-weight: 600;
        text-align: center;
      }
    }

    .el-table__body-wrapper {
      td {
        padding: 12px 0;

        &:first-child {
          text-align: center;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }
}
</style>
