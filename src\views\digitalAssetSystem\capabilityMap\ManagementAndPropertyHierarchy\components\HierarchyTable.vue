<!--层级表格组件-->
<template>
  <div class="hierarchy-table">
    <!-- 数据源切换控制栏 -->
    <div class="table-controls" v-if="showDataSourceSwitch">
      <el-radio-group v-model="currentDataSource" @change="handleDataSourceChange" size="small">
        <el-radio-button label="property">产权层级</el-radio-button>
        <el-radio-button label="management">管理层级</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 表格内容 -->
    <div v-if="loading" class="loading-container" v-loading="loading" :element-loading-text="loadingText">
    </div>

    <div v-else-if="error" class="error-container">
      <el-alert
        title="数据加载失败"
        :description="error"
        type="error"
        show-icon
        :closable="false">
      </el-alert>
      <el-button type="primary" @click="handleRetry" style="margin-top: 16px;">
        重新加载
      </el-button>
    </div>

    <div v-else-if="!tableData || tableData.length === 0" class="empty-container">
      <el-empty :description="emptyText">
        <el-button type="primary" @click="handleRetry">刷新数据</el-button>
      </el-empty>
    </div>

    <div v-else class="table-container" :style="{ height: tableHeight + 'px' }">
      <el-table
        ref="hierarchyTable"
        :data="tableData"
        row-key="zcbiId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :lazy="lazy"
        :load="handleLoadChildren"
        border
        stripe
        style="width: 100%"
        :height="tableHeight">

        <el-table-column
          prop="zcbiName"
          label="企业名称"
          min-width="200"
          show-overflow-tooltip>
        </el-table-column>

        <el-table-column
          prop="zcbiShortName"
          label="企业简称"
          width="150"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.zcbiShortName || '暂无' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="zcbiZcd"
          label="注册地址"
          min-width="200"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.zcbiZcd || '暂无' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="zcbiFddbr"
          label="法定代表人"
          width="120"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.zcbiFddbr || '暂无' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="zcbiJyzt"
          label="经营状态"
          width="100"
          align="center">
          <template slot-scope="scope">
<!--            <el-tag-->
<!--              :type="getBusinessStatusType(scope.row.zcbiJyzt)"-->
<!--              size="small">-->
<!--              {{ scope.row.zcbiJyzt || '暂无' }}-->
<!--            </el-tag>-->
            {{ scope.row.zcbiJyzt || '暂无' }}
          </template>
        </el-table-column>

        <el-table-column
          label="主要行业"
          min-width="180"
          show-overflow-tooltip>
          <template slot-scope="scope">
            {{ getMainIndustry(scope.row) }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="80"
          align="center"
          fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      :title="detailDialog.title"
      :visible.sync="detailDialog.visible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="true">

      <div class="detail-content" v-if="detailDialog.data">
        <div class="detail-row">
          <span class="detail-label">企业名称：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiName || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">企业简称：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiShortName || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">企业ID：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiId || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">注册地址：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiZcd || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">法定代表人：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiFddbr || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">经营状态：</span>
          <span class="detail-value">
            <el-tag :type="getBusinessStatusType(detailDialog.data.zcbiJyzt)" size="small">
              {{ detailDialog.data.zcbiJyzt || '暂无' }}
            </el-tag>
          </span>
        </div>
        <div class="detail-row">
          <span class="detail-label">主要行业1：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiZyhy1 || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">主要行业2：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiZyhy2 || '暂无' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">主要行业3：</span>
          <span class="detail-value">{{ detailDialog.data.zcbiZyhy3 || '暂无' }}</span>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialog.visible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'HierarchyTable',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 错误信息
    error: {
      type: String,
      default: null
    },
    // 加载文本
    loadingText: {
      type: String,
      default: '正在加载表格数据...'
    },
    // 空数据文本
    emptyText: {
      type: String,
      default: '暂无表格数据'
    },
    // 表格高度
    tableHeight: {
      type: [Number, String],
      default: 400
    },
    // 是否启用懒加载
    lazy: {
      type: Boolean,
      default: true
    },
    // 是否显示数据源切换
    showDataSourceSwitch: {
      type: Boolean,
      default: true
    },
    // 当前数据源
    dataSource: {
      type: String,
      default: 'property'
    }
  },
  data() {
    return {
      currentDataSource: this.dataSource,

      // 详情弹窗
      detailDialog: {
        visible: false,
        title: '',
        data: null
      }
    }
  },
  watch: {
    dataSource(newVal) {
      this.currentDataSource = newVal
    },
    tableData(newVal) {
      if (Array.isArray(newVal) && newVal.length) {
        this.$nextTick(() => this.expandFirstLevel())
      }
    }
  },
  mounted() {
    if (Array.isArray(this.tableData) && this.tableData.length) {
      this.$nextTick(() => this.expandFirstLevel())
    }
  },

  methods: {
    /**
     * 处理数据源切换
     */
    handleDataSourceChange() {
      this.$emit('data-source-change', this.currentDataSource)
    },

    /**
     * 处理懒加载子节点
     */
    async handleLoadChildren(tree, treeNode, resolve) {
      console.log(tree, treeNode, resolve);
      this.$emit('load-children', { tree, treeNode, resolve })
    },

    /**
     * 显示详情
     */
    showDetail(row) {
      this.detailDialog = {
        visible: true,
        title: `企业详情 - ${row.zcbiName || '未知企业'}`,
        data: row
      }

      this.$emit('row-detail', row)
    },

    /**
     * 处理重试
     */
    handleRetry() {
      this.$emit('retry')
    },

    /**
     * 获取经营状态标签类型
     */
    getBusinessStatusType(status) {
      const statusMap = {
        '存续': 'success',
        '在业': 'success',
        '正常': 'success',
        '注销': 'danger',
        '吊销': 'danger',
        '停业': 'warning',
        '清算': 'warning'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取主要行业信息
     */
    getMainIndustry(row) {
      const industries = [row.zcbiZyhy1, row.zcbiZyhy2, row.zcbiZyhy3]
        .filter(industry => industry && industry.trim())
        .join('、')
      return industries || '暂无'
    },

    // 默认展开第一级
    expandFirstLevel() {
      const table = this.$refs.hierarchyTable
      if (!table || !Array.isArray(this.tableData)) return
      // 仅展开顶层节点
      this.tableData.forEach(row => {
        table.toggleRowExpansion(row, true)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hierarchy-table {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 表格控制栏样式
  .table-controls {
    padding: 16px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 16px;
    flex-shrink: 0;
  }

  // 表格容器样式
  .table-container {
    flex: 1;
    overflow: hidden; // 确保容器不会超出父容器
    min-height: 0; // 允许flex子项缩小

    ::v-deep .el-table {
      height: 100% !important; // 确保表格占满容器高度

      .el-table__body-wrapper {
        overflow-y: auto; // 确保表格内容可以滚动
        max-height: calc(100% - 40px); // 减去表头高度
      }

      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-table__expand-icon {
        color: #CC1214;
        font-size: 14px;
      }

      .el-table__placeholder {
        color: #909399;
        font-size: 12px;
      }

      // 确保表格滚动条样式
      .el-table__body-wrapper::-webkit-scrollbar {
        width: 8px;
      }

      .el-table__body-wrapper::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }

  .loading-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;

    ::v-deep .el-loading-text {
      color: #606266;
      font-size: 14px;
      margin-top: 12px;
    }
  }

  .error-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background: #fff;

    .el-alert {
      margin-bottom: 20px;
      max-width: 400px;
    }

    .el-button {
      min-width: 120px;
    }
  }

  .empty-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;

    ::v-deep .el-empty {
      .el-empty__image {
        width: 120px;
        height: 120px;
      }

      .el-empty__description {
        color: #909399;
        font-size: 14px;
        margin-top: 16px;
      }

      .el-button {
        margin-top: 16px;
        min-width: 100px;
      }
    }
  }
}

// 表格详情弹窗样式
.detail-content {
  .detail-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    line-height: 1.6;

    .detail-label {
      min-width: 100px;
      color: #606266;
      font-weight: 500;
      flex-shrink: 0;
    }

    .detail-value {
      flex: 1;
      color: #303133;
      word-break: break-all;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hierarchy-table {
    .table-controls {
      padding: 12px 0;
    }

    .detail-content {
      .detail-row {
        .detail-label {
          min-width: 80px;
          font-size: 13px;
        }

        .detail-value {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
