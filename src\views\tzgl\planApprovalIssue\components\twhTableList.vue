<template>
  <div class="content-wrapper">
    <div class="main-content">
      <div style="display: flex; justify-content: space-between;align-items: center;">
        <VabForm 
        ref="VabForm"
        :formModel="searchForm" 
        :formItem="formItem" 
        :formConfig="{ inline: true,clearable:false }" 
        width="100%" 
      />
        <el-button @click="savatotalMontyPf" v-if="activeName == 4" style="margin-bottom: 10px;">保存</el-button>
      </div>
      <!-- 列表 -->
      <VabTable 
        :idName="`vabId${activeName}`" 
        :tableHeader="parentTableHeader" 
        :tableData="tableData" 
        :tableHeight="tableHeight" 
        :pageNo="tablePage.pageNo" 
        :pageSize="tablePage.pageSize"
        :total="tablePage.total" 
        :rowClick="handleRowClick" 
        @current-change="handleCurrentChange" 
      />
    </div>
  </div>
</template>

<script>
  import {  mapGetters } from 'vuex'
import {getgqlTablist, postPfxd,savatotalMontyPf} from 'api/tzgl/investPlan/planDesign/twh'
import VabTable from 'components/VabTable'
import VabForm from 'components/VabForm'
import { getsecondaryunitsList } from 'api/tzgl/investPlan/searchList'

export default {
  name: 'twhTableList',
  props: {
    activeName: {
      type: String,
      default: ''
    },
    parentTableHeader: {
      type: Array,
      default: () => []
    },
    parentsearchForm :{
      type:Object,
    }
  },
  components: {
    VabTable,
    VabForm,
  },
  data () {
    return {
      searchForm:{
       tprYear:"2025"
      },
      formItem:  [
        {
          name: 'dateTime',
          type: 'year',
          prop: 'tprYear',
          format: 'yyyy',
          valueFormat: 'yyyy',
          label: '年份',
          placeholder: '请选择年份',
        },
        {
          name: 'selectTree', 
          props: { value: 'zcbiId', label: 'zcbiName', children: 'children' }, 
          options: [], 
          label: '单位名称',
          isChangeParent:true,
          getSelectTreeValue:this.getSelectTreeValue
        },
        {
          name: 'select',
          options: [
            { label: '已决策', value: '已决策' },
            { label: '未决策', value: '未决策' },
            { label: '批复下达', value: '批复下达' },
          ],
          prop: 'tprState',
          label: '决策状态',
          placeholder: '请选择决策状态',
        },
        {
          name: 'button',
          prop: 'button',
          label: '查询',
          type: 'primary',
          click: this.getTableData,
        },
        {
          name: 'button',
          prop: 'button',
          label: '重置',
          click: this.handleResetFun,
        },
        {
          name: 'button',
          prop: 'button',
          label: '批复下达',
          click: this.handleFfficial,
        },
        //  {
        //   name: 'button',
        //   prop: 'button',
        //   label: '投委会会议纪要',
        //   click: this.meetingMinutes,
        // },
      ],
      tableHeader: [],
      tableData: [],
      tablePage: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      
      tableHeight: '486',
      // 附件组件相关
      type:1,
      selectArr:[],
      selectedRow: {},
      currentState: 0,
      nameObj: {
        1: '股权类投资计划',
        2: '军工固投计划',
        3: '自筹固投计划',
        4: '日常固投计划',
        5: '资产盘活计划',
        6: '证券投资计划',
      }
    }
  },
  computed: {
    ...mapGetters({
      loginUser: 'user/loginUser',
    }),
  },
  created () {
    // 获取项目单位
    this.getTableData()
    this.getsecondaryunitsList()
  },
  watch: {
    
    activeName () {
      this.tableData = []
      this.$emit('tprYearChange','2025')
      this.getTableData()
    }
  },
  methods: {
         //获取二级单位
    async getsecondaryunitsList() {
      const { data, code, msg } = await getsecondaryunitsList()
      if (code == 200) {
        this.formItem[1].options = [data];
      } else {
        this.$message.error(msg);
      }
    },
    getSelectTreeValue(id, name) {
      this.searchForm.tprCompanyId = id
    },
    //获取表格数据
    async getTableData(){
        this.type = this.activeName
       
       
        const params = {
            ...this.searchForm,
            type:this.type,
            state:'决策会'
        }
         let res =  await getgqlTablist(params)
        if (res.code == 200) {
        this.$nextTick(()=>{
           this.tableData = res.data
        })
        } else {
          this.$message.error(res.msg)
        }
      
        
    },
    async savatotalMontyPf(){
      console.log(this.tableData,'9999');
      let res = await savatotalMontyPf([...this.tableData])
      if(res.code == '200'){
        this.$message.success(res.msg)
      }else{
        this.$message.error(res.msg)
      }
    },

    handleCurrentChange (val) {
      this.tablePage.pageNo = val
      this.getTableData()
    },

    // 表格行点击事件
    handleRowClick (row) {
      this.selectedRow = row
      // 可以根据需要设置当前状态
      this.currentState = row.state || 0
    },
    //搜索

    handleResetFun(){
       //this.$emit('handleResetsearch')
          this.$refs.VabForm.$refs.SelectTree[0].clearHandle()
      this.searchForm = {};
      this.$set(this.searchForm, 'tprYear', new Date().getFullYear() + '');
          this.getTableData()
        
    },
    async handleFfficial() {
      console.log(this.activeName,'activeName');
      
      const params = {
        tpoPlanType: this.nameObj[this.activeName],
            tprYear:this.searchForm.tprYear,
            tpoChecker: this.loginUser.luId,
        }
         let res =  await postPfxd(params)
        if (res.code == 200) {
        this.$nextTick(()=>{
          this.getTableData()
           this.$message.success(res.msg)
        })
        } else {
          this.$message.error(res.msg)
        }
    },
    meetingMinutes(){
      
    }

  }
}
</script>

<style lang="scss" scoped>
</style>