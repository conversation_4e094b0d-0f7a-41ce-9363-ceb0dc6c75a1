<template>
  <div class="cq-component">
    <div class="section-title"><span class="bar"></span>仪器设备总体情况</div>

    <div class="stats-container mb">
      <div class="stats-grid">
        <div class="stat-card-s">
          <div class="stat-card">
            <div class="stat-label">仪器设备</div>
            <div class="stat-value">{{ allData.count }}<span class="unit">万台</span></div>
          </div>
        </div>
        <div class="stat-card-s">
          <div class="stat-card">
            <div class="stat-label">仪器设备原值</div>
            <div class="stat-value">{{ allData.bookValue }}<span class="unit">万元</span></div>
          </div>
        </div>
        <div class="stat-card-s">
          <div class="stat-card">
            <div class="stat-label">仪器设备净值</div>
            <div class="stat-value">{{ allData.netValue }}<span class="unit">万元</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TdBarChart from './TdBarChart.vue'

export default {
  props: {
    allData: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    TdBarChart
  },
  data () {
    return {
      // 这里可以放置其他数据
    }
  }
}
</script>

<style lang="scss" scoped>
.mb {
  margin-bottom: 8px;
}
.cq-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  .chart-container {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
  }

  .stats-container {
    flex: 1;
    width: 100%;
    height: 100%;
    .stats-grid {
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: 1fr 1fr;
      gap: 8px;

      .stat-card-s {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
        display: flex;
        justify-content: center;
        align-items: center;
        // 第一个卡片（合并后的卡片）
        &:nth-child(1) {
          grid-column: 1 / -1; // 跨越所有列
          background: #f9fdfd;
        }

        // 第二个卡片
        &:nth-child(2) {
          background: #fdfdf9;
        }

        // 第三个卡片
        &:nth-child(3) {
          background: #fdf9fd;
        }
      }

      .stat-card {
        width: 100%;
        display: flex;
        flex-direction: column;
        // align-items: baseline;
        // justify-content: center;

        .stat-label {
          font-size: 14px;
          color: #909399;
          font-weight: 400;
          display: flex;
          justify-content: center;
          margin-bottom: 12px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          display: flex;
          justify-content: center;
          align-items: baseline;

          .unit {
            font-size: 14px;
            font-weight: normal;
            color: #909399;
            margin-left: 2px;
          }
        }
      }
    }
  }
}

.section-title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
  .bar {
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #cc1214;
    border-radius: 2px;
    margin-right: 6px;
  }
}
</style>