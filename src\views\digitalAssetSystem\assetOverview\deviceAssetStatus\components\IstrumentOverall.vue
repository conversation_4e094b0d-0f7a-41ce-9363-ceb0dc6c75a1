<template>
  <div class="istrumentOverall-container" @click="openPage()">
    <div v-for="(item, index) in overallData" :key="index" class="overall-item">
      <p class="topCont">
        {{ item.title }}
        <span class="total">{{ item.total }}</span>
        {{ item.util }}
      </p>
      <p v-if="item.second" class="bottomCont">
        国产 {{ item.causeNum }} {{ item.util }}，进口 {{ item.businessNum }}{{ item.util }}
      </p>
      <img alt="" :src="item.imgUrl" />
    </div>
  </div>
</template>

<script>
  // import { overallSituationInstrument } from '@/api/sam/instrument'
  export default {
    name: 'IstrumentOverall',
    data() {
      return {
        overallData: [
          {
            title: '仪器设备',
            total: 0,
            causeNum: 0,
            businessNum: 0,
            util: '台',
            imgUrl: require('@/assets/gateway/bg_house.png'),
            second: true,
          },
          {
            title: '账面原值',
            total: 0,
            causeNum: null,
            businessNum: null,
            util: '万元',
            imgUrl: require('@/assets/gateway/bg_houseyz.png'),
            second: false,
          },
          {
            title: '账面净值',
            total: 0,
            causeNum: null,
            businessNum: null,
            util: '万元',
            imgUrl: require('@/assets/gateway/bg_housejz.png'),
            second: false,
          },
        ],
      }
    },
    created() {
      this.getData()
    },
    methods: {
      openPage() {
        // this.$router.push({
        //   path: '/views/cockpit/assetInstrument/components/InstrumentAll',
        //   query: {
        //     title: '仪器设备',
        //   },
        // })
      },
      async getData() {
        // let { data } = await overallSituationInstrument()
        // this.overallData[0].total = (Number(data.total) + Number(data.total2))
        // this.overallData[0].causeNum = Number(data.total)
        // this.overallData[0].businessNum = Number(data.total2)
        // this.overallData[1].total = Number(data.zcfaBookValue)
        // this.overallData[2].total = Number(data.zcfaNetbookValue)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .overall-item,
  .overall-item1 {
    cursor: pointer;
  }
  .istrumentOverall-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .overall-item {
      width: calc((100% - 50px) / 3);
      height: 92px;
      background: rgba(255, 132, 135, 0.04);
      box-shadow: 0px 8px 20px 0px rgba(177, 197, 197, 0.08);
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      text-align: center;
      display: flex;
      justify-content: center;
      flex-direction: column;
      position: relative;
      p {
        margin: 0px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
      .topCont {
        color: #000000;
        margin-bottom: 9px;
        .total {
          font-size: 24px;
          font-weight: 500;
        }
      }
      .bottomCont {
        color: #303133;
      }
      img {
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
  }
</style>
