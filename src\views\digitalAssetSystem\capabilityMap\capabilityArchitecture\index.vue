<template>
  <div class="capability-architecture">
    <CardBox title="能力架构" height="65vh">
      <!-- 顶部统计信息卡片 -->
      <div class="stats-header" slot="rightTitle">
        <div class="capability-levels-info">
          <div class="levels-grid">
            <div
              v-for="(level, index) in capabilityLevels"
              :key="level.level"
              :class="['level-item', getLevelColorClass(index)]"
            >
              <div class="level-title">{{ level.name }}({{ level.level }})</div>
              <div class="level-count">
                <div>{{ level.count }}</div>
                <div style="color: black;font-size: 15px;margin: 0 0 1px 5px">个</div>
              </div>
            </div>
          </div>
        </div>
        <div class="header-actions">
        <span class="depth-select">
          展开层级：
          <el-select v-model="treeDepthSelection" size="mini" @change="onTreeDepthChange" style="width: 90px;">
            <el-option v-for="d in [1,2,3,4]" :key="d" :label="'T' + d" :value="d" />
          </el-select>
        </span>
        </div>
      </div>
      <!-- 直接在本文件内实现径向树形图，不再依赖原有图表组件 -->
      <div ref="radialTreeEl" class="radial-tree-container"></div>
    </CardBox>
  </div>
</template>

<script>
// 统一使用中文命名与注释
import CardBox from '@/views/common/CardBox.vue'
import * as echarts from 'echarts'
import {getCapabilityIndexTree, getNljgNumber} from '@/api/digitalAssetSystem/capabilityIndex'

export default {
  name: 'CapabilityArchitecture',
  components: { CardBox },
  data () {
    return {
      loading: false,        // 加载状态
      errorMessage: null,     // 错误提示文案
      radialTreeData: [],     // 径向树使用的数据
      chartInstance: null,    // echarts 实例
      colorPalette: [         // 为一级分支定义一组清爽配色，便于与示意图风格接近
        '#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16',
        '#E8684A', '#6DC8EC', '#9270CA', '#FF99C3'
      ],
      NumberArr: {},
      treeDepthSelection: 3,
      // 能力层级统计信息
      capabilityLevels: [
        { level: 'T1', name: '大类', count: 0 },
        { level: 'T2', name: '领域', count: 0 },
        { level: 'T3', name: '子类', count: 0 },
        { level: 'T4', name: '能力单元', count: 0 }
      ]
    }
  },
  mounted () {
    this.getNumber()
    // 首次进入加载数据并渲染图表
    this.$nextTick(() => {
      this.loadCapabilityData()
    })
    // 监听窗口尺寸变化，保持图表自适应
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy () {
    // 释放资源与事件
    window.removeEventListener('resize', this.handleResize)
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
  },
  methods: {
    // 获取能力层级的颜色样式类
    getLevelColorClass(index) {
      const colorClasses = ['cyan', 'green', 'blue', 'orange']
      return colorClasses[index % colorClasses.length]
    },

    /**
     * 加载能力指标树，成功后转换为径向树可用结构
     */
    async loadCapabilityData () {
      try {
        this.loading = true
        this.errorMessage = null
        const response = await getCapabilityIndexTree({})
        if (response && response.code == 200 && Array.isArray(response.data)) {
          // 包一层根节点，名称【六大能力】
          const root = [{ tciId: '1', tciCategory: '', children: response.data }]
          this.radialTreeData = this.convertToRadialTreeData(root)
          this.$nextTick(this.renderChart)
        } else {
          this.radialTreeData = []
          this.$message.warning('暂无数据')
        }
      } catch (e) {
        console.error('加载能力指标数据失败:', e)
        this.errorMessage = '加载数据失败，请稍后重试'
        this.$message.error(this.errorMessage)
      } finally {
        this.loading = false
      }
    },

    /**
     * 将后端返回的节点转换为 echarts 树形图可识别的数据结构
     * - 使用 name/children 基本字段
     * - 将原始信息放到 value.raw 便于 tooltip 展示
     * - 为每个一级分支分配固定颜色，并向下继承，形成同色系分支
     */
    convertToRadialTreeData (apiData) {
      if (!apiData || !Array.isArray(apiData)) return []

      // Recursive method: attach color and remap fields
      const transformRecursively = (node, color, depth = 2) => {
        const nameText = node.tciCategory || node.tciIndicatorName || node.name || '未命名'
        const newNode = {
          name: nameText,
          value: { raw: node },
          symbolSize: depth === 1 ? 8 : 4,
          itemStyle: color ? { color } : undefined,
          lineStyle: color ? { color, width: 1.2, curveness: 0.5 } : { width: 1, curveness: 0.5 }
        }
        if (Array.isArray(node.children) && node.children.length) {
          newNode.children = node.children.map(child => transformRecursively(child, color, depth + 1))
        }
        return newNode
      }

      // Assign colors to level-1 branches
      const root = apiData[0]
      if (!root) return []
      const rootName = root.tciCategory || root.name || ''
      const rootNode = { name: rootName, value: { raw: root }, symbolSize: 10 }
      if (Array.isArray(root.children)) {
        rootNode.children = root.children.map((firstLevel, idx) => {
          const color = this.colorPalette[idx % this.colorPalette.length]
          return transformRecursively(firstLevel, color, 1)
        })
      }
      return [rootNode]
    },

    getNumber() {
      getNljgNumber({}).then(res => {
        this.NumberArr = res.data
        // 更新能力层级统计信息
        if (res.data && Array.isArray(res.data)) {
          this.capabilityLevels[0].count = res.data[0]?.tjz || 0
          this.capabilityLevels[1].count = res.data[1]?.tjz || 0
          this.capabilityLevels[2].count = res.data[2]?.tjz || 0
          this.capabilityLevels[3].count = res.data[3]?.tjz || 0
        }
      })
    },

    /**
     * 初始化或更新图表
     */
    renderChart () {
      const containerEl = this.$refs.radialTreeEl
      if (!containerEl) return
      if (!this.chartInstance) {
        this.chartInstance = echarts.init(containerEl)
      }

      const option = {
        // 全局色彩与动画
        animationDuration: 600,
        animationDurationUpdate: 800,
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          confine: true,
          backgroundColor: 'rgba(17,24,39,0.9)',
          borderColor: 'rgba(255,255,255,0.12)',
          textStyle: { color: '#fff', fontSize: 12 },
          formatter: (param) => {
            const raw = (param.data && param.data.value && param.data.value.raw) || {}
            const nameText = (param.data && param.data.name) ? param.data.name : '未命名'

            const companyName = raw.tciCompanyName || ''
            const companyAddress = raw.tciAddress || ''
            return [
              `<div style="font-weight:600;margin-bottom:6px;">${nameText}</div>`,
              companyName ? `<div><span style="color:#9CA3AF;">企业名称：</span>${companyName}</div>` : '',
              companyAddress ? `<div><span style="color:#9CA3AF;">企业地点：</span>${companyAddress}</div>` : ''
            ].join('')
          }
        },

        // series: [{
        //   type: 'tree',
        //   data: this.radialTreeData,
        //   layout: 'radial',          // 径向布局
        //   radius: ['20%', '85%'],    // 设置内外半径，为内圈节点预留更多空间
        //   center: ['0%', '0%'],    // 居中显示，避免偏移导致的空间不均
        //   symbol: 'circle',
        //   // 默认节点尺寸；根节点与一级分支已在数据中单独指定 symbolSize
        //   symbolSize: 4,
        //   initialTreeDepth: this.treeDepthSelection,       // 初始展开层级（可动态切换）
        //   expandAndCollapse: true,   // 点击展开/收起
        //   animationEasing: 'quarticOut',
        //   lineStyle: { width: 1.2, curveness: 0.5 },
        //   itemStyle: { borderWidth: 0 },
        //   // 在支持的版本中尽量避免标签重叠（不支持的系列会自动忽略）
        //   labelLayout: { hideOverlap: true },
        //   // 分级样式，按层级优化标签显示与间距
        //   levels: [
        //     // 根节点
        //     { label: { position: 'inside', rotate: 0, fontSize: 13, color: '#111827' } },
        //     // 一级：增大距离，避免与根节点重叠
        //     { label: { position: 'radial', rotate: 'tangential', distance: 15, fontSize: 12, color: '#374151' } },
        //     // 二级：进一步增大距离，避免与一级重叠
        //     { label: { position: 'radial', rotate: 'tangential', distance: 25, fontSize: 11, color: '#374151' } },
        //     // 三级及以下：外圈保持合理距离
        //     { label: { position: 'radial', rotate: 'tangential', distance: 35, fontSize: 10, color: '#1F2937' } }
        //   ],
        //   // 常规与叶子节点的缺省配置（作为兜底）
        //   label: {
        //     position: 'radial',      // 文本位置按径向布局自动分配
        //     rotate: 'tangential',    // 标签沿圆周切线方向摆放，更贴合外圈示例样式
        //     color: '#374151',
        //     fontSize: 11,
        //     distance: 20
        //   },
        //   leaves: {
        //     label: {
        //       position: 'radial',
        //       rotate: 'tangential',
        //       fontSize: 10,
        //       color: '#1F2937',
        //       distance: 35
        //     }
        //   }
        // }],
        series: [
          {
            type: 'tree',
            data: this.radialTreeData,
            left: '10%',
            top: '-10%',
            bottom: '0%',
            layout: 'radial',
            symbol: 'emptyCircle',
            symbolSize: 7,
            initialTreeDepth: 3,
            animationDurationUpdate: 750,
            zoom: 1,
            roam: true, // 启用缩放和拖拽以适应横向布局
            scaleLimit: {
              min: 0.5,
              max: 5
            },
            emphasis: {
              focus: 'descendant'
            }
          }
        ]
      }

      this.chartInstance.setOption(option, true)
      this.handleResize()
    },


    /**
     * 下拉选择改变：动态更新树的默认展开层级
     */
    onTreeDepthChange (val) {
      if (!this.chartInstance) return
      const depth = Number(val)
      if (!Number.isFinite(depth)) return
      this.chartInstance.setOption({ series: [{ initialTreeDepth: depth }] }, false)
    },

    /**
     * 根据容器尺寸自适应
     */
    handleResize () {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.capability-architecture {
  height: 100%;
}

/* 顶部统计信息区域样式 */
.stats-header {
  background-color: transparent;
  font-weight: bolder;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

/* 能力层级信息样式 */
.capability-levels-info {
  flex: 1 1 auto;
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.level-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7px 12px 7px 12px;
  border-radius: 8px;
  background: #f8f9fa;

  &.cyan {
    background-color: rgba(23, 162, 184, 0.1);
  }

  &.green {
    background-color: rgba(40, 167, 69, 0.1);
  }

  &.blue {
    background-color: rgba(0, 123, 255, 0.1);
  }

  &.orange {
    background-color: rgba(253, 126, 20, 0.1);
  }
}

.level-content {
  flex: 1;
}

.level-title {
  font-size: 15px;
  color: #333;
  font-weight: 600;
}

.level-count {
  font-size: 16px;
  color: #CC1214;
  font-weight: bold;
  margin: 0 5px 0 5px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.header-actions {
  flex: 0 0 200px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 展开层级选择器样式 */
.depth-select {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

/* 径向树容器样式：填满卡片内容区域，背景纯白，圆角与页面风格一致 */
.radial-tree-container {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
}
</style>
