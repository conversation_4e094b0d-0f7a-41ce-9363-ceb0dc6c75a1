<!--实物资产档案 - 土地-->
<template>
  <div class="land-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards" v-if="flag">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-map-location" />
        </div>
        <div class="stat-info">
          <div class="stat-title">土地总数量</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>个</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">土地总价值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.disposalCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.leaseCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产编号:">
              <el-input v-model="searchForm.zcliAssetsNo" placeholder="请输入资产编号" class="inputW" />
            </el-form-item>
            <el-form-item label="土地权属证号:">
              <el-input v-model="searchForm.zcliCertificateCode" placeholder="请输入土地权属证明编号" class="inputW" />
            </el-form-item>
            <el-form-item label="二级成员单位:">
              <el-select v-model="searchForm.companyTname" placeholder="请选择二级成员单位" class="inputW" clearable filterable>
                <el-option v-for="(item, idx) in ejOptions" :key="idx" :label="item.zcbiName" :value="item.zcbiName" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">综合查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
            <ExportButton :export-api="exportLands" table-selector="#landTable" :query-form="searchForm" file-name="实物资产档案-土地.xls" excel-title="实物资产档案-土地" :date-fields="exportDateFields" :show-dropdown="true" :all-data-page-size="10000" button-type="primary" :auto-exclude-operations="true" :exclude-columns="[]" :table-columns="tableColumns" @export-success="handleExportSuccess" @export-error="handleExportError" @export-all-success="handleExportAllSuccess" @export-all-error="handleExportAllError" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="landTable" :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zcliId">
        <!-- 列配置严格按《各模块列表展示字段(1).md》土地模块字段顺序与数量配置 -->
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zcliYear" label="年份" width="150" show-overflow-tooltip align="center" label-class-name="zcliYear" />
        <el-table-column prop="zcliMj" label="密级" width="150" show-overflow-tooltip align="center" label-class-name="zcliMj" />
        <el-table-column prop="companyTname" label="二级单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyTname" />
        <el-table-column prop="companyName" label="本单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyName" />
<!--        <el-table-column prop="zcliCreditCode" label="本单位统一社会信用代码" width="180" show-overflow-tooltip align="center" label-class-name="zcliCreditCode" />-->
        <el-table-column prop="zcliAssetsNo" label="资产编号" width="150" show-overflow-tooltip align="center" label-class-name="zcliAssetsNo" />
        <el-table-column prop="zcliAsssetsmType" label="资产类型" width="150" show-overflow-tooltip align="center" label-class-name="zcliAsssetsmType" />
        <el-table-column prop="zcliCertificateCode" label="土地权属证明编号" width="150" show-overflow-tooltip align="center" label-class-name="zcliCertificateCode" />
        <el-table-column prop="zcliIfExist" label="是否取得土地权属证明" width="180" show-overflow-tooltip align="center" label-class-name="zcliIfExist" />
        <el-table-column prop="zcliNocertificateReason" label="未取得土地权属证明原因" width="180" show-overflow-tooltip align="center" label-class-name="zcliNocertificateReason" />
        <el-table-column prop="zcliUseDescribe" label="现状用途描述" width="150" show-overflow-tooltip align="center" label-class-name="zcliUseDescribe" />
        <el-table-column prop="zcliRequirements" label="地上建筑是否满足批复或者备案功能要求" width="300" show-overflow-tooltip align="center" label-class-name="zcliRequirements" />
        <el-table-column prop="zcliStrategyDescribe" label="是否有战略安排" width="150" show-overflow-tooltip align="center" label-class-name="zcliStrategyDescribe" />
        <el-table-column prop="zcliRange" label="境内境外" width="150" show-overflow-tooltip align="center" label-class-name="zcliRange" />
        <el-table-column prop="zcliCountry" label="国家或地区" width="150" show-overflow-tooltip align="center" label-class-name="zcliCountry" />
        <el-table-column prop="zcliProvince" label="坐落位置（省）" width="150" show-overflow-tooltip align="center" label-class-name="zcliProvince" />
        <el-table-column prop="zcliCity" label="坐落位置（市）" width="150" show-overflow-tooltip align="center" label-class-name="zcliCity" />
        <el-table-column prop="zcliCounty" label="坐落位置（区）" width="150" show-overflow-tooltip align="center" label-class-name="zcliCounty" />
        <el-table-column prop="zcliAddress" label="具体土地位置" width="150" show-overflow-tooltip align="center" label-class-name="zcliAddress" />
        <el-table-column prop="zcliIfConstruction" label="是否在建" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfConstruction" />
        <el-table-column prop="zcliIfStagnation" label="是否延期或停滞" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfStagnation" />
        <el-table-column prop="zcliReasonStagnation" label="延期/停滞原因" width="150" show-overflow-tooltip align="center" label-class-name="zcliReasonStagnation" />
        <el-table-column prop="zcliUseful" label="土地性质" width="150" show-overflow-tooltip align="center" label-class-name="zcliUseful" />
        <el-table-column prop="zcliType" label="取得方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliType" />
        <el-table-column prop="zcliObtainDate" label="取得时间" width="150" show-overflow-tooltip align="center" label-class-name="zcliObtainDate" />
        <el-table-column prop="zcliServiceLife" label="土地使用年限（年）" width="150" show-overflow-tooltip align="center" label-class-name="zcliServiceLife" />
        <el-table-column prop="zcliPlotRatio" label="当前容积率" width="150" show-overflow-tooltip align="center" label-class-name="zcliPlotRatio" />
        <el-table-column prop="zcliApprovalRatio" label="批复容积率" width="150" show-overflow-tooltip align="center" label-class-name="zcliApprovalRatio" />
        <el-table-column prop="zcliBookValue" label="原值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zcliBookValue" />
        <el-table-column prop="zcliNetbookValue" label="净值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zcliNetbookValue" />
        <el-table-column prop="zcliIfAssets" label="是否两非资产" width="150" align="center" label-class-name="zcliIfAssets" />
        <el-table-column prop="zcliDepreciableYear" label="折旧年限" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepreciableYear" />
        <el-table-column prop="zcliTotalDepreciation" label="本年计提折旧总额(万元)" width="180" align="center" label-class-name="zcliTotalDepreciation" />
        <el-table-column prop="zcliEvaluateDate" label="最近评估日期" width="150" show-overflow-tooltip align="center" label-class-name="zcliEvaluateDate" />
        <el-table-column prop="zcliEvaluateValue" label="最近评估价值(万元)" width="150" align="center" label-class-name="zcliEvaluateValue" />
        <el-table-column prop="zcliArea" label="土地总面积（亩）" width="150" align="center" label-class-name="zcliArea" />
        <el-table-column prop="zcliStateZy" label="其中：自用面积(亩)" width="150" align="center" label-class-name="zcliStateZy" />
        <el-table-column prop="zcliStateCz" label="其中：出租面积(亩)" width="150" align="center" label-class-name="zcliStateCz" />
        <el-table-column prop="zcliStateXz" label="其中：闲置面积(亩)" width="150" align="center" label-class-name="zcliStateXz" />
        <el-table-column prop="zcliIdleStartTime" label="闲置起始时间" width="150" show-overflow-tooltip align="center" label-class-name="zcliIdleStartTime" />
        <el-table-column prop="zcliIdleTime" label="空置时间" width="150" show-overflow-tooltip align="center" label-class-name="zcliIdleTime" />
        <el-table-column prop="zcliReasonIdleness" label="闲置原因" width="150" show-overflow-tooltip align="center" label-class-name="zcliReasonIdleness" />
        <el-table-column prop="zcliMethods" label="建议盘活/处置方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliMethods" />
        <el-table-column prop="zcliRentalIncomeLastyear" label="上年租金收入（万元）" width="180" align="center" label-class-name="zcliRentalIncomeLastyear" />
        <el-table-column prop="zcliRentalIncomeThisyear" label="预计本年租金收入（万元）" width="220" align="center" label-class-name="zcliRentalIncomeThisyear" />
        <el-table-column prop="zcliCorrespondingIncome" label="土地面积对应本年营业收入（万元）" width="250" align="center" label-class-name="zcliCorrespondingIncome" />
        <el-table-column prop="zcliSurroundingSalePrice" label="上年周边可比土地出售单价（元/平方米/月）" width="300" align="center" label-class-name="zcliSurroundingSalePrice" />
        <el-table-column prop="zcliSurroundingRentPrice" label="上年周边可比土地出租单价（元/平方米/月）" width="300" align="center" label-class-name="zcliSurroundingRentPrice" />
        <el-table-column prop="zcliBusinessDzzb" label="电子装备（亩）" width="150" align="center" label-class-name="zcliBusinessDzzb" />
        <el-table-column prop="zcliBusinessWxtx" label="网信体系（亩）" width="150" align="center" label-class-name="zcliBusinessWxtx" />
        <el-table-column prop="zcliBusinessCyjc" label="产业基础（亩）" width="150" align="center" label-class-name="zcliBusinessCyjc" />
        <el-table-column prop="zcliBusinessWlaq" label="网络安全（亩）" width="150" align="center" label-class-name="zcliBusinessWlaq" />
        <el-table-column prop="zcliBusinessOther" label="其他（亩）" width="150" align="center" label-class-name="zcliBusinessOther" />
        <el-table-column prop="zcliIfDispute" label="是否存在纠纷" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfDispute" />
        <el-table-column prop="zcliIfMortgage" label="土地是否已抵押" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfMortgage" />
        <el-table-column prop="zcliMortgage" label="其中:已抵押面积（亩）" width="180" align="center" label-class-name="zcliMortgage" />
        <el-table-column prop="zcliIfDispose" label="是否可处置" width="150" align="center" label-class-name="zcliIfDispose" />
        <el-table-column prop="zcliReasonDisposal" label="不可处置原因" width="150" show-overflow-tooltip align="center" label-class-name="zcliReasonDisposal" />
        <el-table-column prop="zcliDeptName" label="业务主管部门名称" width="150" show-overflow-tooltip align="center" label-class-name="zcliDeptName" />
        <el-table-column prop="zcliOperator" label="经办人" width="150" show-overflow-tooltip align="center" label-class-name="zcliOperator" />
        <el-table-column prop="zcliOperatorTel" label="经办人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliOperatorTel" />
        <el-table-column prop="zcliDepartmentLeader" label="部门负责人" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepartmentLeader" />
        <el-table-column prop="zcliDepartmentTel" label="部门负责人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepartmentTel" />
        <el-table-column prop="zcliCompanyLeader" label="分管所(公司)领导" width="150" show-overflow-tooltip align="center" label-class-name="zcliCompanyLeader" />
        <el-table-column prop="zcliCompanyTel" label="分管所(公司)领导联系方式" width="220" show-overflow-tooltip align="center" label-class-name="zcliCompanyTel" />
        <el-table-column prop="zcliRemark" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zcliRemark" />
        <el-table-column prop="zcliLongitude" label="经度" width="150" show-overflow-tooltip align="center" label-class-name="zcliLongitude" />
        <el-table-column prop="zcliLatitude" label="纬度" width="150" show-overflow-tooltip align="center" label-class-name="zcliLatitude" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <land-detail-dialog ref="landDetail" />

    <!-- 高级查询弹窗组件 -->
    <land-advanced-search-dialog ref="landAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getLandsList, exportLands, getLandsStats } from '@/api/digitalAssetSystem/land'
import LandDetailDialog from './components/LandDetailDialog.vue'
import LandAdvancedSearchDialog from './components/LandAdvancedSearchDialog.vue'
import ExportButton from '@/components/ExportButton/index.vue'
import { searchSecoundOrgInfo } from '@/api/common'

export default {
  name: "LandIndex",
  components: {
    LandDetailDialog,
    LandAdvancedSearchDialog,
    ExportButton
  },
  props: {
    flag: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      height: this.$baseTableHeight(1, 3.5),
      searchForm: {
        // 严格参照接口文档字段，移除系统与非文档字段
        zcliAssetsNo: '',
        zcliCertificateCode: '',
        companyName: '',
        companyTname: '',

        zcliCity: '',
        zcliProvince: '',
        zcliDistrict: '',
        zcliAddress: '',
        zcliUseDescribe: '',
        zcliIfDispose: '',
        zcliIfDispute: '',
        zcliIfMortgage: '',
        // 高级查询字段
        zcliType: '',
        zcliDate: '',
        zcliRange: '',
        zcliIfAssets: '',
        zcliIfExist: '',
        zcliArea: '',
        zcliBookValue: '',
        zcliNetbookValue: '',
        zcliTotalDepreciation: '',
        zcliEvaluateValue: '',
        zcliEvaluateDate: '',
        zcliServiceLife: '',
        zcliDepreciableYear: '',
        zcliUseful: '',
        zcliYear: '',
        zcliInventoryStatus: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      ejOptions: [],
      statistics: {
        totalCount: 0,
        totalValue: 0,
        totalArea: 0,
        disposalCount: 0,
        leaseCount: 0
      },

      // 表格列配置，用于导出 - 严格 70 字段，按接口文档顺序
      tableColumns: [
        { prop: 'companyName', label: '本单位名称' },
        { prop: 'companyTname', label: '二级单位名称' },
        { prop: 'zcliAddress', label: '具体土地位置' },
        { prop: 'zcliArea', label: '土地总面积' },
        { prop: 'zcliAssetsNo', label: '资产编号' },

        { prop: 'zcliCity', label: '坐落位置（市）' },
        { prop: 'zcliDistrict', label: '坐落位置（区）' },
        { prop: 'zcliProvince', label: '坐落位置（省）' },
        { prop: 'zcliType', label: '取得方式' },
        { prop: 'zcliIfAssets', label: '是否两非资产' },
        { prop: 'zcliIfDispose', label: '是否可处置' },
        { prop: 'zcliBookValue', label: '账面价值（万元）' },
        { prop: 'zcliNetbookValue', label: '账面净值（万元）' },
        { prop: 'zcliAsssetsmType', label: '资产类型' },
        { prop: 'zcliBusinessCyjc', label: '产业基础面积' },
        { prop: 'zcliBusinessDzzb', label: '电子装备面积' },
        { prop: 'zcliBusinessOther', label: '其它面积' },
        { prop: 'zcliBusinessWlaq', label: '网络安全面积' },
        { prop: 'zcliBusinessWxtx', label: '网信体系面积' },
        { prop: 'zcliCertificateCode', label: '土地权属证明编号' },
        { prop: 'zcliCompanyLeader', label: '分管所(公司)领导' },
        { prop: 'zcliCompanyTel', label: '分管所(公司)领导联系方式' },
        { prop: 'zcliCorrespondingIncome', label: '土地面积对应本年营业收入（万元）' },
        { prop: 'zcliCountry', label: '国家或地区' },
        { prop: 'zcliCreditCode', label: '统一社会信用代码' },
        { prop: 'zcliDate', label: '登记时间' },
        { prop: 'zcliDepartmentLeader', label: '部门负责人' },
        { prop: 'zcliDepartmentTel', label: '部门负责人联系方式' },
        { prop: 'zcliDepreciableYear', label: '折旧年限' },
        { prop: 'zcliDeptName', label: '业务主管部门名称' },
        { prop: 'zcliEvaluateDate', label: '最近评估日期' },
        { prop: 'zcliEvaluateValue', label: '最近评估价值（万元）' },
        { prop: 'zcliIdleStartTime', label: '闲置起始时间' },
        { prop: 'zcliIdleTime', label: '空置时间' },
        { prop: 'zcliIfConstruction', label: '是否在建' },
        { prop: 'zcliIfDispute', label: '是否存在纠纷' },
        { prop: 'zcliIfExist', label: '是否取得土地权属证明编号' },
        { prop: 'zcliIfMortgage', label: '土地是否已抵押' },
        { prop: 'zcliIfStagnation', label: '是否延期或停滞' },
        { prop: 'zcliLatitude', label: '纬度' },
        { prop: 'zcliLongitude', label: '经度' },
        { prop: 'zcliMethods', label: '建议盘活/处置方式' },
        { prop: 'zcliMj', label: '密级' },
        { prop: 'zcliMortgage', label: '已抵押面积' },
        { prop: 'zcliParkName', label: '本地块所属园区' },
        { prop: 'zcliPlotRatio', label: '容积率' },
        { prop: 'zcliRange', label: '境内境外' },
        { prop: 'zcliReasonDisposal', label: '不可处置原因' },
        { prop: 'zcliReasonIdleness', label: '闲置原因' },
        { prop: 'zcliReasonStagnation', label: '延期/停滞原因' },
        { prop: 'zcliRentalIncomeLastyear', label: '上年租金收入（万元）' },
        { prop: 'zcliRentalIncomeThisyear', label: '预计本年租金收入（万元）' },
        { prop: 'zcliRequirements', label: '地上建筑是否满足批复或备案功能要求' },
        { prop: 'zcliServiceLife', label: '土地使用年限(年)' },
        { prop: 'zcliStateCz', label: '出租面积' },
        { prop: 'zcliStateXz', label: '闲置面积' },
        { prop: 'zcliStateZy', label: '自用面积' },
        { prop: 'zcliStrategyDescribe', label: '是否有战略安排' },
        { prop: 'zcliSurroundingRentPrice', label: '上年周边可比土地出租单价（元/平方米/月）' },
        { prop: 'zcliSurroundingSalePrice', label: '上年周边可比土地出售单价（元/平方米/月）' },
        { prop: 'zcliTotalDepreciation', label: '本年计提折旧总额（万元）' },
        { prop: 'zcliUseDescribe', label: '现状用途描述' },
        { prop: 'zcliUseful', label: '土地性质' },
        { prop: 'zcliYear', label: '年份' },
        { prop: 'zcpiName', label: '批复项目名称' },
        { prop: 'zcliNotobtainedCertificateReason', label: '未取得土地权属证明原因' },
        { prop: 'zcliInventoryStatus', label: '盘点状态' },
        { prop: 'zcliApprovalRatio', label: '批复容积率' }
      ],

      // 导出日期字段配置
      exportDateFields: {
        zcliDate: { celltype: "text" },
        zcliEvaluateDate: { celltype: "text" },
        zcliIdleStartTime: { celltype: "text" }
      },

      // 导出API函数
      exportLands
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
    this.fetchEjOptions()
  },
  methods: {
    // 
    fetchEjOptions () {
      searchSecoundOrgInfo().then(({ data }) => {
        this.ejOptions = (data && data.children) || []
      }).catch(() => {
        this.ejOptions = []
      })
    },
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm
      }

      getLandsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getLandsStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.total_land || 0,
            totalValue: response.data.total_value || 0,
            totalArea: response.data.totalArea || 0,
            disposalCount: response.data.disposalCount || 0,
            leaseCount: response.data.leaseCount || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
        // 使用 Mock 统计数据
        this.statistics = {
          totalCount: 156,
          totalValue: 23.5,
          totalArea: 1250000,
          disposalCount: 8,
          leaseCount: 12
        }
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.fetchData()
    },

    // 导出成功回调
    handleExportSuccess (response) {
      console.log('当前数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出失败回调
    handleExportError (error) {
      console.error('当前数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 全部数据导出成功回调
    handleExportAllSuccess (response) {
      console.log('全部数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 全部数据导出失败回调
    handleExportAllError (error) {
      console.error('全部数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    handleAdvancedSearch () {
      this.$refs.landAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleDetail (row) {
      this.$refs.landDetail.showDialog(row)
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  // justify-content: space-between;
}
.inputW {
  width: 250px;
}

.land-container {
  padding: 16px;
  background-color: #FFFFFF;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
