<template>
  <div ref="chartRef" class="pie-chart-container"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    data: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: 'cq'
    }
  },

  data () {
    return {
      chart: null,
      mapData: [],
      maxNum: 10
    }
  },

  watch: {
    data: {
      deep: true,
      handler (val) {
        // console.log("🚀🚀 ~ 变化 ~ 🚀🚀", val)
        this.initChart() // 数据变化时重新渲染图表
      },
    },
  },

  mounted () {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    initChart () {
      this.$nextTick(() => {
        this.mapData = this.data.map(item => {
          const obj = {
            name: item.address,
            value: item.total,
            value1: item.addressPer,
          }
          return obj
        })

        // 找到total最大的项

        if (this.data[0]) {
          let maxNumObj = this.data.reduce((max, current) => {
            return (current.total > max.total) ? current : max
          }, this.data[0] || {})
          // console.log("🚀🚀 ~ initChart ~ maxNumObj ~ 🚀🚀", maxNumObj)
          this.maxNum = maxNumObj.total
        }


        this.chart = echarts.init(this.$refs.chartRef)
        this.setChartOption()
      })

    },

    setChartOption () {
      const chinaJson = require('@/assets/digitalAssetSystem/china.json')
      echarts.registerMap('china', chinaJson)
      //数据纯属虚构
      var data = this.mapData

      var option = {
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: '#fff',
          borderColor: 'rgba(24,144,255,0.35)',
          borderWidth: 1,
          padding: [8, 12],
          textStyle: {
            color: '#333'
          },
          extraCssText: 'border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1);',
          formatter: (params) => {
            var name = params.name || ''
            let valueTitle = '', unit = ''

            if (this.type === 'cq') {
              valueTitle = '单位：'
              unit = '个'
            } else if (this.type === 'td') {
              valueTitle = '土地面积：'
              unit = '亩'
            } else if (this.type === 'fc') {
              valueTitle = '房屋面积：'
              unit = '平方米'
            } else if (this.type === 'sb') {
              valueTitle = '设备数量：'
              unit = '台'
            }
            var val = (params.data && (params.data.value != null ? params.data.value : params.data['value'])) != null
              ? (params.data.value != null ? params.data.value : params.data['value'])
              : (params.value != null ? params.value : '-')

            var val1 = (params.data && (params.data.value1 != null ? params.data.value1 : params.data['value1'])) != null
              ? (params.data.value1 != null ? params.data.value1 : params.data['value1'])
              : (params.value1 != null ? params.value1 : '-')
            return '<div style="font-size:12px;line-height:1.6;color:#333;">'
              + '<div style="font-weight:600;margin-bottom:6px;">' + name + '：</div>'
              + '<div><span style="color:#8c8c8c;">' + valueTitle + '</span>'
              + '<span style="font-weight:700;font-size:16px;">' + val + unit + '</span></div>'
              + '<div><span style="color:#8c8c8c;">占比：</span>'
              + '<span style="font-weight:700;font-size:16px;">' + val1 + '%</span></div>'
              + '</div>'
          },
        },
        visualMap: {
          show: true,
          type: 'continuous',
          // text: ['占比高', '占比低'],
          showLabel: true,
          calculable: true,
          seriesIndex: [0],
          min: 0,
          max: this.maxNum,
          inRange: {
            color: ['#a3d4ff', '#83b9f1', '#5b99d5', '#2a67a1', '#024484']
          },
          itemWidth: 10,
          textStyle: {
            color: '#000'
          },
          bottom: 0,
          right: '0',
        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        geo: {
          roam: true,
          map: 'china',
          top: '50',
          left: '0',
          right: '0',
          layoutSize: '100%',
          label: {
            emphasis: {
              show: false
            }
          },
          itemStyle: {
            emphasis: {
              areaColor: '#fff464'
            }
          },
          regions: [{
            name: '南海诸岛',
            value: 0,
            itemStyle: {
              normal: {
                opacity: 0,
                label: {
                  show: false
                }
              }
            }
          }],
        },
        series: [{
          name: 'mapSer',
          type: 'map',
          roam: false,
          geoIndex: 0,
          label: {
            show: false,
          },
          data: data
        }]
      }
      this.chart.setOption(option)
    },
    handleResize () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-container {
  width: 100%;
  height: 100%;
}
</style>
