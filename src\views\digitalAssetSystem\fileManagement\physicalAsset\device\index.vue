<!--设备资产管理页面-->
<template>
  <div class="custom-table-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards" v-if="flag">
      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-s-data" />
        </div>
        <div class="stat-info">
          <div class="stat-title">仪器设备总数</div>
          <div class="stat-value blue-text">{{ statsData.totalCount || 0 }}<span>个</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper green-bg">
          <i class="el-icon-s-check" />
        </div>
        <div class="stat-info">
          <div class="stat-title">设备总原值</div>
          <div class="stat-value green-text">{{ statsData.normalCount || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-warning" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value orange-text">{{ statsData.idleCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value red-text">{{ statsData.totalValue || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form" v-if="flag">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.zcfaAssetsName" placeholder="请输入资产名称" class="inputW" />
            </el-form-item>
            <el-form-item label="本单位名称:">
              <el-input v-model="searchForm.companyName" placeholder="请输入本单位名称" class="inputW" />
            </el-form-item>
            <el-form-item label="二级成员单位:">
              <el-select v-model="searchForm.companyTname" placeholder="请选择二级成员单位" class="inputW" clearable filterable>
                <el-option v-for="(item, idx) in ejOptions" :key="idx" :label="item.zcbiName" :value="item.zcbiName" />
              </el-select>
            </el-form-item>
            <el-form-item label="资产状态:">
              <el-select v-model="searchForm.zcfaAssetsState" placeholder="请选择资产状态" class="inputW" clearable>
                <el-option
                  :label="item.lpvVal"
                  :value="item.lpvVal"
                  v-for="(item,i) in zcztOptions"
                  :key="item.lpvVal"
                />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search" v-if="flag">综合查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
            <ExportButton v-if="flag" :export-api="exportDevicesProgress" table-selector="#deviceTable" :query-form="searchForm" file-name="设备资产管理.xls" excel-title="设备资产管理" :date-fields="exportDateFields" :show-dropdown="true" :all-data-page-size="10000000" button-type="primary" :auto-exclude-operations="true" :exclude-columns="[]" :table-columns="tableColumns" @export-success="handleExportSuccess" @export-error="handleExportError" @export-all-success="handleExportAllSuccess" @export-all-error="handleExportAllError" @export-all-with-progress="handleExportAllWithProgress" @export-all-progress-error="handleExportAllProgressError" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="deviceTable" :data="list" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="listLoading" row-key="zcfaId">
        <!-- 列配置严格按《各模块列表展示字段(1).md》仪器设备模块字段顺序与数量配置 -->
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zcfaYear" label="年份" width="150" show-overflow-tooltip align="center" label-class-name="zcfaYear" />
        <el-table-column prop="zcfaMj" label="密级" width="150" show-overflow-tooltip align="center" label-class-name="zcfaMj" />
        <el-table-column prop="companyTname" label="二级单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyTname" />
        <el-table-column prop="companyName" label="本单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyName" />
<!--        <el-table-column prop="zcfaCreditCode" label="本单位统一社会信用代码" width="180" show-overflow-tooltip align="center" label-class-name="zcfaCreditCode" />-->
        <el-table-column prop="zcfaSerialNum" label="单位内部资产编号" width="150" show-overflow-tooltip align="center" label-class-name="zcfaSerialNum" />
        <el-table-column prop="zcfaCodeRule" label="资产编码规则" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCodeRule" />
        <el-table-column prop="zcfaAssetsName" label="资产名称" width="150" show-overflow-tooltip align="center" label-class-name="zcfaAssetsName" />
        <el-table-column prop="zcfaType" label="资产类别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaType" />
        <el-table-column prop="zcfaUse" label="资产用途" width="150" show-overflow-tooltip align="center" label-class-name="zcfaUse" />
        <el-table-column prop="zcfaModel" label="型号" width="150" show-overflow-tooltip align="center" label-class-name="zcfaModel" />
        <el-table-column prop="zcfaKeyFigures" label="关键指标" width="150" show-overflow-tooltip align="center" label-class-name="zcfaKeyFigures" />
        <el-table-column prop="zcfaProLine" label="资产所属产线" width="150" show-overflow-tooltip align="center" label-class-name="zcfaProLine" />
        <el-table-column prop="zcfaIfEpect" label="当前产线生产的产品规格是否低于投资预期" width="300" show-overflow-tooltip align="center" label-class-name="zcfaIfEpect" />
        <el-table-column prop="zcfaIfStoppage" label="产线是否停产" width="150" align="center" label-class-name="zcfaIfStoppage" />
        <el-table-column prop="zcfaEquipment" label="仪器设备先进性" width="150" show-overflow-tooltip align="center" label-class-name="zcfaEquipment" />
        <el-table-column prop="zcfaCountry" label="国别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCountry" />
        <el-table-column prop="zcfaCountryName" label="所属国家名称" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCountryName" />
        <el-table-column prop="zcfaManufactor" label="生产厂家" width="150" show-overflow-tooltip align="center" label-class-name="zcfaManufactor" />
        <el-table-column prop="zcfaSharedCategory" label="共享类别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaSharedCategory" />
        <el-table-column prop="zcfaSpecialCategory" label="专用类别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaSpecialCategory" />
        <el-table-column prop="zcfaProductDate" label="购置日期" width="150" show-overflow-tooltip align="center" label-class-name="zcfaProductDate" />
        <el-table-column prop="zcfaRecordDate" label="入账日期" width="150" show-overflow-tooltip align="center" label-class-name="zcfaRecordDate" />
        <el-table-column prop="zcfaUsefulLife" label="折旧年限（年）" width="150" align="center" label-class-name="zcfaUsefulLife" />
        <el-table-column prop="zcfaFundsSource" label="经费来源" width="150" show-overflow-tooltip align="center" label-class-name="zcfaFundsSource" />
        <el-table-column prop="zcfaIfMilitary" label="是否军工技改关键设施设备" width="220" align="center" label-class-name="zcfaIfMilitary" />
        <el-table-column prop="zcfaBookValue" label="账面原值（元）" width="150" show-overflow-tooltip align="center" label-class-name="zcfaBookValue" />
        <el-table-column prop="zcfaNetbookValue" label="账面净值（元）" width="150" show-overflow-tooltip align="center" label-class-name="zcfaNetbookValue" />
        <el-table-column prop="zcfaIfTwonon" label="是否两非资产" width="150" align="center" label-class-name="zcfaIfTwonon" />
        <el-table-column prop="zcfaProvince" label="坐落位置-省" width="150" show-overflow-tooltip align="center" label-class-name="zcfaProvince" />
        <el-table-column prop="zcfaCity" label="坐落位置-市" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCity" />
        <el-table-column prop="zcfaDistrict" label="坐落位置-区/县" width="150" show-overflow-tooltip align="center" label-class-name="zcfaDistrict" />
        <el-table-column prop="zcfaStoragePlace" label="存放地点" width="150" show-overflow-tooltip align="center" label-class-name="zcfaStoragePlace" />
        <el-table-column prop="zcfaAssetsState" label="资产状态" width="150" align="center" label-class-name="zcfaAssetsState" />
        <el-table-column prop="zcfaIdleStartTime" label="闲置起始时间" width="150" show-overflow-tooltip align="center" label-class-name="zcfaIdleStartTime" />
        <el-table-column prop="zcfaReasonStatus" label="状态原因" width="150" show-overflow-tooltip align="center" label-class-name="zcfaReasonStatus" />
        <el-table-column prop="zcfaDisposition" label="建议盘活/处置方式" width="150" show-overflow-tooltip align="center" label-class-name="zcfaDisposition" />
        <el-table-column prop="zcfaBill" label="是否在账" width="150" show-overflow-tooltip align="center" label-class-name="zcfaBill" />
        <el-table-column prop="zcfaInventoryStatus" label="盘点状态" width="150" show-overflow-tooltip align="center" label-class-name="zcfaInventoryStatus" />
        <el-table-column prop="zcfaContacts" label="联系人" width="150" show-overflow-tooltip align="center" label-class-name="zcfaContacts" />
        <el-table-column prop="zcfaContactsPhone" label="联系电话" width="150" show-overflow-tooltip align="center" label-class-name="zcfaContactsPhone" />
        <el-table-column prop="zcfaRemark" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zcfaRemark" />
        <el-table-column label="操作" width="80" fixed="right" align="center" v-if="flag">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <device-detail-dialog ref="deviceDetail" />

    <!-- 高级查询弹窗组件 -->
    <device-advanced-search-dialog ref="deviceAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />

    <!-- 导出进度弹窗组件 -->
    <export-progress-dialog ref="exportProgressDialog" :uid="exportUid" @export-success="handleExportProgressSuccess" @export-error="handleExportProgressError" @dialog-close="handleExportProgressClose" />
  </div>
</template>

<script>
import { getDevicesList, exportDevicesProgress, getDevicesStats } from '@/api/digitalAssetSystem/device'
import DeviceDetailDialog from './components/DeviceDetailDialog.vue'
import DeviceAdvancedSearchDialog from './components/DeviceAdvancedSearchDialog.vue'
import ExportButton from '@/components/ExportButton/index.vue'
import ExportProgressDialog from './components/ExportProgressDialog.vue'
import { uuid } from '@/utils'
import { searchSecoundOrgInfo } from '@/api/common'
import {getParamvalsById} from "@/api/tzgl/project/common";

export default {
  name: "index",
  components: {
    DeviceDetailDialog,
    DeviceAdvancedSearchDialog,
    ExportButton,
    ExportProgressDialog
  },
  props: {
    flag: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      height: this.$baseTableHeight(1, 3.5),
      list: [],
      total: 0,
      listLoading: true,
      ejOptions: [],
      zcztOptions: [],
      searchForm: {
        companyName: '',
        companyTname: '',
        zcfaAssetsName: '',
        zcfaAssetsState: '',

        zcfaType: '',
        zcfaUse: '',
        zcfaStoragePlace: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaBookValue: '',
        zcfaNetbookValue: '',
        zcfaProductDateStr: '',
        zcfaRecordDateStr: '',
        zcfaIdleStartTimeStr: '',
        zcfaUsefulLife: '',
        zcfaFundsSource: '',
        zcfaIfAssets: '',
        zcfaRemark: '',
        zcfaAbilitySort: '',
        zcfaBill: '',
        zcfaBusinessUnit: '',
        zcfaCodeRule: '',
        zcfaContacts: '',
        zcfaContactsPhone: '',
        zcfaCountry: '',
        zcfaCountryName: '',
        zcfaCreditCode: '',
        zcfaDisposition: '',
        zcfaEquipment: '',
        zcfaIfEpect: '',
        zcfaIfMilitary: '',
        zcfaIfStoppage: '',
        zcfaIfTwonon: '',
        zcfaIncity: '',
        zcfaIncityDistinguish: '',
        zcfaIncityEconomize: '',
        zcfaIncityMarket: '',
        zcfaKeyFigures: '',
        zcfaMj: '',
        zcfaProLine: '',
        zcfaSerialNum: '',
        zcfaSharedCategory: '',
        zcfaSpecialCategory: '',
        zcfaYear: '',
        zcfaAbility: '',
        zcfaInventoryStatus: '',
        pageNo: 1,
        pageSize: 10
      },
      productDateRange: [],
      recordDateRange: [],
      statsData: {
        totalCount: 0,
        normalCount: 0,
        idleCount: 0,
        totalValue: 0
      },

      // 表格列配置，用于导出
      tableColumns: [
        { prop: 'companyName', label: '本单位名称' },
        { prop: 'companyTname', label: '二级单位名称' },
        { prop: 'zcfaAssetsName', label: '资产名称' },
        { prop: 'zcfaAssetsState', label: '资产状态' },

        { prop: 'zcfaType', label: '资产类别' },
        { prop: 'zcfaUse', label: '资产用途' },
        { prop: 'zcfaStoragePlace', label: '存放地点' },
        { prop: 'zcfaManufactor', label: '生产厂家' },
        { prop: 'zcfaModel', label: '型号' },
        { prop: 'zcfaBookValue', label: '账面原值（元）' },
        { prop: 'zcfaNetbookValue', label: '账面净值（元）' },
        { prop: 'zcfaProductDateStr', label: '购置日期' },
        { prop: 'zcfaRecordDateStr', label: '入账日期' },
        { prop: 'zcfaIdleStartTimeStr', label: '闲置起始时间' },
        { prop: 'zcfaUsefulLife', label: '折旧年限（年）' },
        { prop: 'zcfaFundsSource', label: '经费来源' },
        { prop: 'zcfaIfAssets', label: '是否两非资产' },
        { prop: 'zcfaRemark', label: '备注' },
        { prop: 'zcfaAbilitySort', label: '能力类别' },
        { prop: 'zcfaBill', label: '是否在账' },
        { prop: 'zcfaBusinessUnit', label: '所属业务板块' },
        { prop: 'zcfaCodeRule', label: '单位内部编码规则' },
        { prop: 'zcfaContacts', label: '联系人' },
        { prop: 'zcfaContactsPhone', label: '联系电话' },
        { prop: 'zcfaCountry', label: '国别' },
        { prop: 'zcfaCountryName', label: '国家名称' },
        { prop: 'zcfaCreditCode', label: '统一社会信用代码' },
        { prop: 'zcfaDisposition', label: '建议盘活/处置方式' },
        { prop: 'zcfaEquipment', label: '仪器设备先进性' },
        { prop: 'zcfaIfEpect', label: '是否低于投资预期' },
        { prop: 'zcfaIfMilitary', label: '是否军工技改关键设施设备' },
        { prop: 'zcfaIfStoppage', label: '产线是否停产' },
        { prop: 'zcfaIfTwonon', label: '是否两非资产（备用字段）' },
        { prop: 'zcfaIncity', label: '坐落位置' },
        { prop: 'zcfaIncityDistinguish', label: '坐落位置--区/县(新增)' },
        { prop: 'zcfaIncityEconomize', label: '坐落位置--省(新增)' },
        { prop: 'zcfaIncityMarket', label: '坐落位置-市(新增)' },
        { prop: 'zcfaKeyFigures', label: '关键指标' },
        { prop: 'zcfaMj', label: '密级' },
        { prop: 'zcfaProLine', label: '资产所属产线' },
        { prop: 'zcfaSerialNum', label: '单位内部资产编号' },
        { prop: 'zcfaSharedCategory', label: '共享类别' },
        { prop: 'zcfaSpecialCategory', label: '专用类别' },
        { prop: 'zcfaYear', label: '年份' },
        { prop: 'zcfaAbility', label: '资产能力' },
        { prop: 'zcfaInventoryStatus', label: '盘点状态' }
      ],

      // 导出日期字段配置
      exportDateFields: {
        zcfaProductDateStr: { celltype: "text" },
        zcfaRecordDateStr: { celltype: "text" },
        zcfaIdleStartTimeStr: { celltype: "text" }
      },

      // 导出API函数
      exportDevicesProgress,

      // 导出进度相关
      exportUid: null
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
    this.fetchEjOptions()
    this.getParamvalsData('ZCZT', 'zcztOptions')
  },
  methods: {
    // 获取字典项
    async getParamvalsData(lpdId, dataName) {
      const { data } = await getParamvalsById({lpvLpdId:lpdId, lpdIstree:'否'})
      this[dataName] = data.list || data
    },
    // 加载二级成员单位选项
    fetchEjOptions () {
      searchSecoundOrgInfo().then(({ data }) => {
        this.ejOptions = (data && data.children) || []
      }).catch(() => {
        this.ejOptions = []
      })
    },
    fetchData () {
      this.listLoading = true
      const query = {
        ...this.searchForm
      }

      getDevicesList(query).then(response => {
        if (response && response.data) {
          this.list = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getDevicesStats().then(response => {
        if (response && response.data) {
          this.statsData = {
            totalCount: response.data.total_fixed_assets_count || 0,
            normalCount: response.data.total_fixed_assets_value || 0,
            idleCount: response.data.idleCount || 0,
            totalValue: response.data.totalValue || 0
          }
        }
      }).catch(() => {
        // this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },
    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        if (key !== 'pageNo' && key !== 'pageSize') {
          this.searchForm[key] = ''
        }
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.productDateRange = []
      this.recordDateRange = []
      this.fetchData()
    },
    handleProductDateChange (val) {
      if (val && val.length === 2) {
        this.searchForm.zcfaProductDateStart = val[0]
        this.searchForm.zcfaProductDateEnd = val[1]
      } else {
        this.searchForm.zcfaProductDateStart = ''
        this.searchForm.zcfaProductDateEnd = ''
      }
    },
    handleRecordDateChange (val) {
      if (val && val.length === 2) {
        this.searchForm.zcfaRecordDateStart = val[0]
        this.searchForm.zcfaRecordDateEnd = val[1]
      } else {
        this.searchForm.zcfaRecordDateStart = ''
        this.searchForm.zcfaRecordDateEnd = ''
      }
    },
    // 导出成功回调
    handleExportSuccess (response) {
      console.log('当前数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出失败回调
    handleExportError (error) {
      console.error('当前数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 全部数据导出成功回调
    handleExportAllSuccess (response) {
      console.log('全部数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 全部数据导出失败回调
    handleExportAllError (error) {
      console.error('全部数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 带进度条的全部数据导出处理
    async handleExportAllWithProgress (exportData) {
      try {
        // 生成唯一ID用于SSE连接
        this.exportUid = uuid()
        // 显示进度弹窗
        this.$refs.exportProgressDialog.showDialog(this.exportUid)

        // 添加uid到导出数据中
        const exportDataWithUid = {
          ...exportData,
          uid: this.exportUid
        }

        // 调用导出API
        const response = await exportDevicesProgress(exportDataWithUid)

        if (response.code == 200) {
          console.log('启动导出数据操作成功')
        } else {
          throw new Error(response.msg || '启动导出失败')
        }
      } catch (error) {
        console.error('启动导出失败:', error)
        this.$message.error(error.message || '启动导出失败，请重试')
        // 关闭进度弹窗
        if (this.$refs.exportProgressDialog) {
          this.$refs.exportProgressDialog.handleClose()
        }
      }
    },

    // 带进度条导出启动失败回调
    handleExportAllProgressError (error) {
      console.error('启动带进度条导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 导出进度成功回调
    handleExportProgressSuccess (response) {
      console.log('带进度条导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出进度错误回调
    handleExportProgressError (error) {
      console.error('带进度条导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 导出进度弹窗关闭回调
    handleExportProgressClose () {
      this.exportUid = null
    },

    handleAdvancedSearch () {
      this.$refs.deviceAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleDetail (row) {
      this.$refs.deviceDetail.showDialog(row)
    },

    getStatusTagType (status) {
      const statusMap = {
        '正常': 'success',
        '闲置': 'warning',
        '报废': 'danger',
        '维修': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  // justify-content: space-between;
}
.inputW {
  width: 200px;
}

.custom-table-container {
  padding: 16px;
  background-color: #FFFFFF;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.green-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.green-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
