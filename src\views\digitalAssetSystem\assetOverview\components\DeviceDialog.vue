<!--添加新分组弹窗-->
<template>
  <DialogCard :dialogTableVisible="dialogVisible" title="仪器设备" :close="handleCancel" :flag="true" width="1700px" height="700px" top="5vh">
    <div slot="content" class="add-group-content">

      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.name" placeholder="请输入资产名称" class="inputW" />
            </el-form-item>
            <el-form-item label="单位名称:">
              <el-input v-model="searchForm.companyName" placeholder="请输入单位名称" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
          </el-form-item>
        </div>
      </el-form>

      <el-table :data="tableData" style="width: 100%;margin-bottom: 50px;" height="500px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="ZCFA_YEAR" label="年份" width="100" show-overflow-tooltip align="center" label-class-name="zcfaYear" />
        <el-table-column prop="ZCFA_MJ" label="密级" width="100" show-overflow-tooltip align="center" label-class-name="zcfaMj" />
        <el-table-column prop="COMPANY_TNAME" label="二级单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyTname" />
        <el-table-column prop="COMPANY_NAME" label="本单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyName" />
        <el-table-column prop="ZCFA_CREDIT_CODE" label="本单位统一社会信用代码" width="180" show-overflow-tooltip align="center" label-class-name="zcfaCreditCode" />
        <el-table-column prop="ZCFA_SERIAL_NUM" label="单位内部资产编号" width="150" show-overflow-tooltip align="center" label-class-name="zcfaSerialNum" />
        <el-table-column prop="ZCFA_CODE_RULE" label="资产编码规则" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCodeRule" />
        <el-table-column prop="ZCFA_ASSETS_NAME" label="资产名称" width="150" show-overflow-tooltip align="center" label-class-name="zcfaAssetsName" />
        <el-table-column prop="ZCFA_TYPE" label="资产类别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaType" />
        <el-table-column prop="ZCFA_USE" label="资产用途" width="150" show-overflow-tooltip align="center" label-class-name="zcfaUse" />
        <el-table-column prop="ZCFA_MODEL" label="型号" width="150" show-overflow-tooltip align="center" label-class-name="zcfaModel" />
        <el-table-column prop="ZCFA_KEY_FIGURES" label="关键指标" width="150" show-overflow-tooltip align="center" label-class-name="zcfaKeyFigures" />
        <el-table-column prop="ZCFA_PRO_LINE" label="资产所属产线" width="150" show-overflow-tooltip align="center" label-class-name="zcfaProLine" />
        <el-table-column prop="ZCFA_IF_EPECT" label="当前产线生产的产品规格是否低于投资预期" width="300" show-overflow-tooltip align="center" label-class-name="zcfaIfEpect" />
        <el-table-column prop="ZCFA_IF_STOPPAGE" label="产线是否停产" width="150" align="center" label-class-name="zcfaIfStoppage" />
        <el-table-column prop="ZCFA_EQUIPMENT" label="仪器设备先进性" width="150" show-overflow-tooltip align="center" label-class-name="zcfaEquipment" />
        <el-table-column prop="ZCFA_COUNTRY" label="国别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCountry" />
        <el-table-column prop="ZCFA_COUNTRY_NAME" label="所属国家名称" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCountryName" />
        <el-table-column prop="ZCFA_MANUFACTOR" label="生产厂家" width="150" show-overflow-tooltip align="center" label-class-name="zcfaManufactor" />
        <el-table-column prop="ZCFA_SHARED_CATEGORY" label="共享类别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaSharedCategory" />
        <el-table-column prop="ZCFA_SPECIAL_CATEGORY" label="专用类别" width="150" show-overflow-tooltip align="center" label-class-name="zcfaSpecialCategory" />
        <el-table-column prop="ZCFA_PRODUCT_DATE" label="购置日期" width="150" show-overflow-tooltip align="center" label-class-name="zcfaProductDate" />
        <el-table-column prop="ZCFA_RECORD_DATE" label="入账日期" width="150" show-overflow-tooltip align="center" label-class-name="zcfaRecordDate" />
        <el-table-column prop="ZCFA_USEFUL_LIFE" label="折旧年限（年）" width="150" align="center" label-class-name="zcfaUsefulLife" />
        <el-table-column prop="ZCFA_FUNDS_SOURCE" label="经费来源" width="150" show-overflow-tooltip align="center" label-class-name="zcfaFundsSource" />
        <el-table-column prop="ZCFA_IF_MILITARY" label="是否军工技改关键设施设备" width="220" align="center" label-class-name="zcfaIfMilitary" />
        <el-table-column prop="ZCFA_BOOK_VALUE" label="账面原值（元）" width="150" show-overflow-tooltip align="center" label-class-name="zcfaBookValue" />
        <el-table-column prop="ZCFA_NETBOOK_VALUE" label="账面净值（元）" width="150" show-overflow-tooltip align="center" label-class-name="zcfaNetbookValue" />
        <el-table-column prop="ZCFA_IF_TWONON" label="是否两非资产" width="150" align="center" label-class-name="zcfaIfTwonon" />
        <el-table-column prop="ZCFA_PROVINCE" label="坐落位置-省" width="150" show-overflow-tooltip align="center" label-class-name="zcfaProvince" />
        <el-table-column prop="ZCFA_CITY" label="坐落位置-市" width="150" show-overflow-tooltip align="center" label-class-name="zcfaCity" />
        <el-table-column prop="ZCFA_DISTRICT" label="坐落位置-区/县" width="150" show-overflow-tooltip align="center" label-class-name="zcfaDistrict" />
        <el-table-column prop="ZCFA_STORAGE_PLACE" label="存放地点" width="150" show-overflow-tooltip align="center" label-class-name="zcfaStoragePlace" />
        <el-table-column prop="ZCFA_ASSETS_STATE" label="资产状态" width="150" align="center" label-class-name="zcfaAssetsState" />
        <el-table-column prop="ZCFA_IDLE_START_TIME" label="闲置起始时间" width="150" show-overflow-tooltip align="center" label-class-name="zcfaIdleStartTime" />
        <el-table-column prop="ZCFA_REASON_STATUS" label="状态原因" width="150" show-overflow-tooltip align="center" label-class-name="zcfaReasonStatus" />
        <el-table-column prop="ZCFA_DISPOSITION" label="建议盘活/处置方式" width="150" show-overflow-tooltip align="center" label-class-name="zcfaDisposition" />
        <el-table-column prop="ZCFA_BILL" label="是否在账" width="150" show-overflow-tooltip align="center" label-class-name="zcfaBill" />
        <el-table-column prop="ZCFA_INVENTORY_STATUS" label="盘点状态" width="150" show-overflow-tooltip align="center" label-class-name="zcfaInventoryStatus" />
        <el-table-column prop="ZCFA_CONTACTS" label="联系人" width="150" show-overflow-tooltip align="center" label-class-name="zcfaContacts" />
        <el-table-column prop="ZCFA_CONTACTS_PHONE" label="联系电话" width="150" show-overflow-tooltip align="center" label-class-name="zcfaContactsPhone" />
        <el-table-column prop="ZCFA_REMARK" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zcfaRemark" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />

      <DeviceDetailDialog ref="deviceDetail" />
    </div>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard.vue'
import { getIndicatorValueWithParamsPage } from 'api/digitalAssetSystem/assetOverview'
import DeviceDetailDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/device/components/DeviceDetailDialog.vue'

export default {
  components: { DialogCard, DeviceDetailDialog },

  data () {
    return {
      dialogVisible: false,
      tableData: [],
      indicatorCode: '',
      zcbiId: '',
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        zcliAssetsNo: '',
        zcliCertificateCode: '',
      },
      total: 0
    }
  },

  methods: {

    handleDetail (row) {
      row.zcfaId = row.ZCFA_ID
      this.$refs.deviceDetail.showDialog(row)
    },

    handleShow (indicatorCode, zcbiId) {
      this.dialogVisible = true
      this.indicatorCode = indicatorCode
      this.zcbiId = zcbiId
      this.getList()
    },

    getList () {
      getIndicatorValueWithParamsPage({
        indicatorCode: "FIXED_ASSETS_DYNAMIC",
        pageNo: this.searchForm.pageNo,
        pageSize: this.searchForm.pageSize,
        params: {
          year: '2025',
          zcbiId: this.zcbiId,
          name: this.searchForm.name,
          companyName: this.searchForm.companyName,
          ...this.indicatorCode,
        },
        // additionalConditions: {
        //   ...this.indicatorCode,
        //   name: this.searchForm.name ? `AND ZCFA_ASSETS_NAME LIKE '%${this.searchForm.name}%'` : '',
        //   companyName: this.searchForm.companyName ? `AND COMPANY_NAME LIKE '%${this.searchForm.companyName}%'` : ''
        // }
      }).then(res => {
        console.log("🚀🚀 ~ 仪器设备-二级页面 ~ 🚀🚀", res.data.list)
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.getList()
    },

    handleCancel () {
      this.dialogVisible = false
      this.tableData = []
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.getList()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 100%;
}

.leftRight {
  display: flex;
  // justify-content: space-between;
}
</style>
