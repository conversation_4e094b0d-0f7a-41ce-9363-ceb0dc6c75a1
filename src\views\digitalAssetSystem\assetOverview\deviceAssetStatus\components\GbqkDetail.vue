<template>
  <div class="container">
    <VabChartPie v-show="radio == '数量'&&sum > 0" :option="option" title='tdsyqk' bigTitle='国别情况划分' assort="仪器设备" firstLevelTitle='2' :home="home"/>
    <div class="emptyImg" v-show="radio == '数量'&&sum == 0">
      <img src="@/assets/empty_images/data_empty.png" alt="" width="360" />
    </div>
    <VabChartPie v-show="radio == '原值'&&sum > 0" :option="option" title='tdsyqk' bigTitle='国别情况划分' assort="仪器设备" firstLevelTitle='2' :home="home"/>
    <div class="emptyImg" v-show="radio == '原值'&&sum == 0">
      <img src="@/assets/empty_images/data_empty.png" alt="" width="360" />
    </div>
    <VabChartPie v-show="radio == '净值'&&sum > 0" :option="option" title='tdsyqk' bigTitle='国别情况划分' assort="仪器设备" firstLevelTitle='2' :home="home"/>
    <div class="emptyImg" v-show="radio == '净值'&&sum == 0">
      <img src="@/assets/empty_images/data_empty.png" alt="" width="360" />
    </div>
   </div> 
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { countrySituation } from '@/api/sam/instrument'
  export default {
    name: "",
    props: ['radio','home'],
    components: {
      VabChartPie
    },
    data() {
      return {
        tableData: [],
        option: {},
        sum: 0
      }
    },
    created() {
      this.getData()
    },
    methods: {
      async getData() {
        let params = {
          companyId: this.companyId,
          flag: this.flag,
          catName: '资产状态',
          catName2: this.radio,
          start: this.radio
        }
        // let { data } = await countrySituation(params)
        // this.tableData = data
        // this.sum = data.reduce((accumulator, currentValue) => {  
        //   const number = parseFloat(currentValue.total)
        //   return accumulator + number;  
        // }, 0).toFixed(2); 
        // let echartData = []
        // data.map(item => {
        //   echartData.push({
        //     name: item.zcfaCountry,
        //     value: item.total,
        //     data: item.proportion
        //   })
        // })
        // let count = ''
        // if(this.radio != '数量') {
        //   count = '万元'
        // }else {
        //   count = '套'
        // }
        // this.option = {
        //   title: {
        //     zlevel: 0,
        //     text: [
        //         '{name|合计}',
        //         '{value|' + this.sum + '}',
        //         count
        //     ].join('\n'),
        //     top: 'center',
        //     left: '29%',
        //     textAlign: 'center',
        //     textStyle: {
        //       rich: {
        //         value: {
        //           color: '#303133',
        //           fontSize: 18,
        //           fontWeight: 'bold',
        //           lineHeight: 26,
        //         },
        //         name: {
        //           color: '#909399',
        //           fontSize: 16,
        //           lineHeight: 20
        //         },
        //       },
        //     },
        //   },
        //   tooltip: {
        //     trigger: 'item'
        //   },
        //   legend: {
        //     left: 'left',
        //     orient: 'vertical',
        //     icon:'circle',
        //     left: '70%',
        //     top: '40%'
        //   },
        //   grid: {
        //     top:'1%',
        //     containLabel: true
        //   },
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)','rgba(93,112,146,0.85)'],
        //   series: [
        //     {
        //       name: '房屋使用情况',
        //       type: 'pie',
        //       radius: ['50%', '70%'],
        //       center: ['30%', '50%'],
        //       avoidLabelOverlap: false,
        //       label: {
        //         show: false,
        //         position: 'center'
        //       },
        //       emphasis: {
        //         label: {
        //           show: true,
        //           fontSize: 20,
        //           fontWeight: 'bold'
        //         }
        //       },
        //       labelLine: {
        //         show: false
        //       },
        //       data: echartData
        //     }
        //   ]
        // }
      },
    },
    watch: {
      flag() {
        this.getData()
      },
      companyId() {
        this.getData()
      },
      radio() {
        this.getData()
      },
    }

  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
  }
  
  :deep() {
    .echarts {
      width: 100%;
      height: 204px;
    }
  }
  .emptyImg {
      height: auto;
      display: flex;
      justify-content: center;
      align-items: center;
    }
</style>