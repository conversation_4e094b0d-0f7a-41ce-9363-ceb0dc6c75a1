<template>
  <div class="container">
    <el-tabs v-model="activeName" >
      <el-tab-pane :label="year + '年仪器设备转让计划情况'" name="yqsbZR">
        <YqsbZrDetail :year="year"/>
      </el-tab-pane>
      <el-tab-pane :label="year + '年仪器设备出租计划情况'" name="yqsbCZ">
        <YqsbCzDetail :year="year"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import YqsbZrDetail from './YqsbZrDetail'
  import YqsbCzDetail from './YqsbCzDetail'
  export default {
    name: "",
    props: ['year'],
    components: {
      YqsbZrDetail,
      YqsbCzDetail
    },
    data() {
      return {
        activeName: 'yqsbZR'
      }
    },
    methods: {

    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-tabs__nav-scroll {
    display: flex;
    justify-content: right!important;
  }
</style>