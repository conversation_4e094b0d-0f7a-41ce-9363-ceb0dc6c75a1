<template>
  <div>
    <!-- 单选切换 -->
    <el-radio-group v-model="radioValue" >
      <el-radio-button v-for="(item,index) in radioList" :key="index" :label="item">{{item}}</el-radio-button>
    </el-radio-group>

    <!-- 项目进度 -->
    <SmallCardVerticalLine title="项目进度">
      <span v-if="radioValue=='股权类' || radioValue=='资产处置'" slot="rightTitle">单位：数量（个）</span>
      <HorizontalBarEcharts 
        v-if="radioValue=='股权类' || radioValue=='资产处置'"
        position="left" 
        labelPosition="insideLeft"
        height="277px" 
        barWidth="24"
        :data="activeNameData.topEchartsData || []" 
        :inverse="false" 
        :color="['rgba(91,143,249,0.55)', 'rgba(91,143,249,0.85)']" 
        :barClick="handleBarClick"
      />
      <div v-else-if="radioValue == '自筹类固投' || radioValue == '军工类固投'">
        <p class="projectStackedBarChartTitle">{{ radioValue == '自筹类固投' ? '年度立项计划' : '立项计划' }}</p>
        <StackedBarChart 
          chartHeight="51px"
          :chartData="activeNameData.topHeaderEchartsData || []" 
          :barClick="handleBarClick"
        />
        <p class="projectStackedBarChartTitle">{{ radioValue == '自筹类固投' ? '在建设项目全周期进展' : '在建设项目进展' }}</p>
        <StackedBarChart 
          chartHeight="164px"
          :chartData="activeNameData.topFooterEchartsData || []" 
          :barClick="handleBarClick"
        />
      </div>
    </SmallCardVerticalLine>

    <!-- 股权类-业务布局 -->
    <SmallCardVerticalLine title="业务布局" style="margin-top: 17px;" v-if="radioValue=='股权类' || radioValue=='资产处置'">
      <span slot="rightTitle">单位：金额 (万元)</span>
      <PieEcharts
        :data="activeNameData.bottomEchartsData || []"
        height="286px"
        :unit="' '"
        :isHandleNumberData="true"
      />
    </SmallCardVerticalLine>

    <!-- 自筹、军工、资产处置-项目资金执行情况、业务布局 -->
    <div style="margin-top: 17px;display: flex;" v-else-if="radioValue=='自筹类固投' || radioValue=='军工类固投'">
      <SmallCardVerticalLine title="项目资金执行情况" style="flex: 1;">
        <span slot="rightTitle">单位：金额 (万元)</span>
        <PieEcharts
          height="286px"
          radius="45%"
          legendTop="60%"
          :data="activeNameData.bottomLeftEchartsData || []"
          :legendLeft="'center'"
          :center="['50%','30%']" 
          :unit="' '"
          :isHandleNumberData="true"
        />
      </SmallCardVerticalLine>
      <SmallCardVerticalLine title="业务布局" style="flex: 1;margin-left: 17px;">
        <span slot="rightTitle">单位：金额 (万元)</span>
        <PieEcharts
          height="286px"
          radius="45%"
          legendTop="60%"
          labelPosition="vertical"
          :data="activeNameData.bottomRightEchartsData || []"
          :unit="' '"
          :legendLeft="'center'"
          :center="['50%','30%']" 
          :isHandleNumberData="true"
        />
      </SmallCardVerticalLine>
    </div>
  </div>
</template>

<script>
  import SmallCardVerticalLine from 'common/SmallCardVerticalLine';
  import StackedBarChart from 'common/StackedBarChart';
  import PieEcharts from "common/PieEcharts";
  import HorizontalBarEcharts from 'common/HorizontalBarEcharts';
  import { getNdtzxmList } from 'api/tzgl/investOverview/investAll';

  export default {
    name: 'projectQk',
    props:{
      params:{
        type: Object,
        default: () => {}
      }
    },
    components: {
      SmallCardVerticalLine,
      StackedBarChart,
      PieEcharts,
      HorizontalBarEcharts
    },
    data(){
      return {
        paramsDebounceTimer: null,
        radioValue:'股权类',
        radioList:[
          '股权类',
          '军工类固投',
          '自筹类固投',
          '资产处置',
        ],
        allData:{
          '股权类':{
            topEchartsData:[],
            bottomEchartsData:[],
          },
          '自筹类固投':{
            topHeaderEchartsData:{
              barWidth:'12px',
              name:'lxjh',
              labels: [],
              title:'',
              datasets: [
                {
                    label: '已完成',
                    data: [],
                    backgroundColor: ['#73DEB3','#73DEB3'],
                    stack: 'group1'
                },
                {
                    label: '已上报',
                    data: [],
                    backgroundColor: ['#5B8FF9','#5B8FF9'],
                    stack: 'group1' 
                },
                {
                    label: '待上报',
                    data: [],
                    backgroundColor: ['#F7C739','#F7C739'],
                    stack: 'group1'
                },
              ],
            },
            topFooterEchartsData:{ 
              barWidth:'12px',
              name:'xmjz',
              labels: [],
              title:'',
              datasets: [
                {
                    label: '已完成',
                    data: [],
                    backgroundColor: ['#73DEB3','#73DEB3'],
                    stack: 'group1'
                },
                {
                    label: '已上报',
                    data: [],
                    backgroundColor: ['#5B8FF9','#5B8FF9'],
                    stack: 'group1' 
                },
                {
                    label: '拖期',
                    data: [],
                    backgroundColor: ['#EB7E65','#EB7E65'],
                    stack: 'group1'
                },
                {
                    label: '待上报',
                    data: [],
                    backgroundColor: ['#F7C739','#F7C739'],
                    stack: 'group1'
                },
              ],
            },
            bottomLeftEchartsData:[],
            bottomRightEchartsData:[]
          },
          '军工类固投':{
            topHeaderEchartsData:{
              barWidth:'12px',
              name:'lxjh',
              labels: [],
              title:'',
              datasets: [
                {
                    label: '已完成',
                    data: [],
                    backgroundColor: ['#73DEB3','#73DEB3'],
                    stack: 'group1'
                },
                {
                    label: '已上报',
                    data: [],
                    backgroundColor: ['#5B8FF9','#5B8FF9'],
                    stack: 'group1' 
                },
                {
                    label: '待上报',
                    data: [],
                    backgroundColor: ['#F7C739','#F7C739'],
                    stack: 'group1'
                },
              ],
            },
            topFooterEchartsData:{ 
              barWidth:'12px',
              name:'xmjz',
              labels: [],
              title:'',
              datasets: [
                {
                    label: '已完成',
                    data: [],
                    backgroundColor: ['#73DEB3','#73DEB3'],
                    stack: 'group1'
                },
                {
                    label: '已上报',
                    data: [],
                    backgroundColor: ['#5B8FF9','#5B8FF9'],
                    stack: 'group1' 
                },
                {
                    label: '拖期',
                    data: [],
                    backgroundColor: ['#EB7E65','#EB7E65'],
                    stack: 'group1'
                },
                {
                    label: '待上报',
                    data: [],
                    backgroundColor: ['#F7C739','#F7C739'],
                    stack: 'group1'
                },
              ],
            },
            bottomLeftEchartsData:[],
            bottomRightEchartsData:[]
          },
          '资产处置':{
            topEchartsData:[],
            bottomEchartsData:[],
          }
        },
        activeNameData:{}
      }
    },
    watch:{
      radioValue(val){
        this.activeNameData = this.allData[val];
      },
      params:{
        deep: true,
        handler(newVal, oldVal){
          if (newVal.year !== oldVal.year || newVal.zcbiId !== oldVal.zcbiId) {
            clearTimeout(this.paramsDebounceTimer);
            this.paramsDebounceTimer = setTimeout(() => {
              this.getData();
            }, 500);
          }
        }
      }
    },
    beforeUnmount() {
      clearTimeout(this.paramsDebounceTimer);
    },
    methods: {
      // 获取当前页数据
      async getData(){
        const {data,code,msg} = await getNdtzxmList(this.params);
        if(code == 200){
          this.handleInitData(data);
        }else{
          this.$message.error(msg);
        }
      },
      // 初始化数据处理
      handleInitData(data){
        try {
          this.allData['股权类'].topEchartsData = data.gq || [];
          this.allData['股权类'].bottomEchartsData = data.gqywbj || [];

          let labelsTop = [],data1 = [],data2 = [],data3 = [];
          let labelsBottom = [],data4 = [],data5 = [],data6 = [],data7 = [];
          (data.zc || []).map((item,index)=>{
            if(index == 0){
              labelsTop.push(item.name);
              data1.push(item.value);
              data2.push(item.value1);
              data3.push(item.value3);
            }else{
              labelsBottom.push(item.name);
              data4.push(item.value);
              data5.push(item.value1);
              data6.push(item.value2);
              data7.push(item.value3);
            }
          })

          this.allData['自筹类固投'].topHeaderEchartsData.labels = labelsTop;
          this.allData['自筹类固投'].topHeaderEchartsData.datasets[0].data = data1;
          this.allData['自筹类固投'].topHeaderEchartsData.datasets[1].data = data2;
          this.allData['自筹类固投'].topHeaderEchartsData.datasets[2].data = data3;
          this.allData['自筹类固投'].topFooterEchartsData.labels = labelsBottom;
          this.allData['自筹类固投'].topFooterEchartsData.datasets[0].data = data4;
          this.allData['自筹类固投'].topFooterEchartsData.datasets[1].data = data5;
          this.allData['自筹类固投'].topFooterEchartsData.datasets[2].data = data6;
          this.allData['自筹类固投'].topFooterEchartsData.datasets[3].data = data7;
          this.allData['自筹类固投'].bottomLeftEchartsData = data.zcxmzj || [];
          this.allData['自筹类固投'].bottomRightEchartsData = data.zcywbj || [];

          let labelsTop1 = [],data11 = [],data12 = [],data13 = [];
          let labelsBottom1 = [],data14 = [],data15 = [],data16 = [],data17 = [];
          (data.jg || []).map((item,index)=>{
            if(index == 0){
              labelsTop1.push(item.name);
              data11.push(item.value);
              data12.push(item.value1);
              data13.push(item.value3);
            }else{
              labelsBottom1.push(item.name);
              data14.push(item.value);
              data15.push(item.value1);
              data16.push(item.value2);
              data17.push(item.value3);
            }
          })
          this.allData['军工类固投'].topHeaderEchartsData.labels = labelsTop1;
          this.allData['军工类固投'].topHeaderEchartsData.datasets[0].data = data11;
          this.allData['军工类固投'].topHeaderEchartsData.datasets[1].data = data12;
          this.allData['军工类固投'].topHeaderEchartsData.datasets[2].data = data13;
          this.allData['军工类固投'].topFooterEchartsData.labels = labelsBottom1;
          this.allData['军工类固投'].topFooterEchartsData.datasets[0].data = data14;
          this.allData['军工类固投'].topFooterEchartsData.datasets[1].data = data15;
          this.allData['军工类固投'].topFooterEchartsData.datasets[2].data = data16;
          this.allData['军工类固投'].topFooterEchartsData.datasets[3].data = data17;
          this.allData['军工类固投'].bottomLeftEchartsData = data.jgxmzj || [];
          this.allData['军工类固投'].bottomRightEchartsData = data.jgywbj || [];

          this.allData['资产处置'].topEchartsData = data.zr || [];
          this.allData['资产处置'].bottomEchartsData = data.zrywbj || [];
          this.activeNameData = this.allData[this.radioValue];
        } catch (error) {
          this.$message.error(error);
        }
      },
      // 股权、资产处置单个柱子点击事件
      handleBarClick(...args){
        this.$emit('handleSecondPage',args,this.radioValue);
      },
    }
  }
</script>

<style lang="scss" scoped>
  .el-radio-group{
    width:100%;
    display:flex;
    margin-bottom: 16px;
    label{flex:1;}
    ::v-deep .el-radio-button__inner{
      font-size: 14px;
      width: 100%;
      display: inline-block;
      border: 1px solid #DCDFE6!important;
      border-shadow: none;
    }
  }
  p{
    margin: 0;
  }
  .el-radio-group{
    ::v-deep .is-active .el-radio-button__inner{
      background:rgba(204,18,20,0.04)!important;
      color: #CC1214!important;
      border: 1px solid #DCDFE6!important;
      font-weight: bolder;
    }
  }

  .projectStackedBarChartTitle{
    height: 31px;
    font-weight: bolder;
    font-size: 14px;
    color: #303133;
    line-height: 31px;
    padding: 0 16px;
    background: linear-gradient( 270deg, rgba(101,120,155,0) 0%, rgba(101,120,155,0.08) 100%);
  }
</style>