<template>
  <div class="container">
    <VabChartPie :option="option" v-show="tableData.length > 0"  :title="'仪器设备区域'"/>
    <div class="emptyImg" v-show=" tableData.length == 0">
      <img src="@/assets/empty_images/data_empty.png" alt="" width="360">
    </div>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { instrumentAreaDivision } from '@/api/sam/instrument'
  // import { provincialLevelDistribution } from '@/api/sam/instrument'
  export default {
    name: "YqsbNumArea",
    props: ['companyId','flag','title'],
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {},
        tableData:[],
        cityDataAll: []
      }
    },
    created(){
      this.getEchartData()
      this.getDataAll()
      this.$baseEventBus.$on('areaRegionClick',(params)=>{
       if(params.title == "仪器设备区域"){
        // this.$router.push({
        //   path:'/views/cockpit/assetInstrument/components/YqsbNumAreaRegion',
        //   query:{
        //     value:JSON.stringify(this.cityDataAll),
        //     cityName:params.name,
        //     title: this.title,
        //     index: params.dataIndex
        //   }
        // })
       }
      })
    },
    methods: {
      async getDataAll() {
        // let { data } = await provincialLevelDistribution()
        // this.cityDataAll = data
      },
      async getEchartData() {
        let params = {
          companyId: this.companyId,
          flag: this.flag
        }
        // let { data } = await instrumentAreaDivision(params)
        // this.tableData = data
        // let echartData = []
        // data.map(item => {
        //   echartData.push({
        //     name: item.zcaiCityName,
        //     value: item.total,
        //   })
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'item'
        //   },
        //   legend: {
        //     orient: 'vertical',
        //     left:'50%',
        //     top: '15%'
        //   },
        //   color:['rgba(91,143,249,0.85)', 'rgba(90,216,166,0.85)', 'rgba(93,112,146,0.85)'],
         
        //   series: [
        //     {
        //       name: '仪器使用情况',
        //       type: 'pie',
        //       radius: '80%',
        //       center: ['20%','50%'],
        //       label: {
        //         show: false,
        //       },
        //       labelLine:{  //删除指示线
        //         show:false
        //       },
        //       data: echartData,
        //       emphasis: {
        //         itemStyle: {
        //           shadowBlur: 10,
        //           shadowOffsetX: 0,
        //           shadowColor: 'rgba(0, 0, 0, 0.5)'
        //         }
        //       }
        //     }
        //   ]
        // }
      }
    },
  }
</script>

<style lang="scss" scoped>

  :deep() {
    .echarts {
      width: 100%;
      height: 308px;
    }
    .emptyImg {
      height: 308px !important;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>