<template>
  <div class="container">
    <VabChartPie :option="option"/>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { assetUsage } from '@/api/sam/instrument'
  export default {
    name: "YtqkDetail",
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {}
      }
    },
    created() {
      this.getEchartData()
    },
    methods: {
      async getEchartData() {
        // let { data } = await assetUsage()
        // let xData = []
        // let yData1 = []
        // let yData2 = []
        // let yData3 = []
        // data.map(item => {
        //   xData.push(item.lpvName)
        //   yData1.push(item.total)
        //   yData2.push(item.total1)
        //   yData3.push(item.total2)
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow'
        //     }
        //   },
        //   legend: {},
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)'],
        //   grid: {
        //     left: '5%',
        //     right: '10%',
        //     bottom: '5%',
        //     top:'15%',
        //     containLabel: true
        //   },
        //   xAxis: {
        //     name: '资产用途',
        //     type: 'category',
        //     data: xData
        //   },
        //   yAxis: [{
        //     type: 'value',
        //     name: '原值、净值 (亿元)'
        //   },{
        //     type:'value',
        //     name: '数量(台套)'
        //   }],
        //   series: [
        //     {
        //       name: '账面原值',
        //       data: yData2,
        //       type: 'bar',
        //       barWidth: '20'
        //     },
        //     {
        //       name: '账面净值',
        //       data: yData3,
        //       type: 'bar',
        //       barWidth: '20'
        //     },
        //     {
        //       name: '数量',
        //       data: yData1,
        //       type: 'bar',
        //       barWidth: '20'
        //     }
        //   ]
        // }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
  }
  
  :deep() {
    .echarts {
      width: 100%;
      height: 252px;
    }
  }
</style>