<template>
  <div class="container">
    <SmallCard :title="'仪器设备分布情况 (单位：台套)'">
      <div class="smallCardCont">
        <div class="innerCard">
          <div class="topTitle">省级地域分布数量前十排名</div>
          <div class="innerCardCont">
            <YqsbTopTen />
          </div>
        </div>
        <div class="innerCard">
          <div class="topTitle">仪器设备数量按区域划分</div>
          <div class="innerCardCont">
            <YqsbNumArea :title="'资产分析'"/>
          </div>
        </div>
      </div>
    </SmallCard>
  </div>
</template>

<script>
  import SmallCard from '@/views/common/SmallCard'
  import YqsbTopTen from './YqsbTopTen'
  import YqsbNumArea from './YqsbNumArea'
  export default {
    name: "FbDetail",
    components: {
      SmallCard,
      YqsbTopTen,
      YqsbNumArea
    },
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
    .smallCardCont {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .innerCard {
        width: calc((100% - 24px) / 2);
        height: 100%;
        box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
        border-radius: 4px;
        border: 1px solid #E4E7ED;
        .topTitle {
          width: 100%;
          height: 56px;
          background: #F5F7FA;
          border-radius: 4px 4px 0px 0px;
          border: 1px solid #E4E7ED;
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #303133;
          line-height: 56px;
          text-align: center;
        }
        .innerCardCont {
          width: 100%;
          height: calc(100% - 56px);
        }
      }
    }
  }
</style>