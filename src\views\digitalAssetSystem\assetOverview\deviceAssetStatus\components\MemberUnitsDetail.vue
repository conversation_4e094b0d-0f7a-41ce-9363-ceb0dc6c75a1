<template>
  <div class="container">
    <SmallCard :title="'成员单位仪器设备数量排名前十'">
      <div slot="rightTitle">
        <el-radio-group v-model="cydwradio">
          <el-radio-button label="表格"></el-radio-button>
          <el-radio-button label="图标"></el-radio-button>
        </el-radio-group>
      </div>
      <el-table
        :data="tableData"
        ref="table"
        height="400"
        v-show="this.cydwradio == '表格'"
        border
        show-summary
        :summary-method="getSummaries"
        style="width: 100%">
        <template slot="empty">
            <img src="@/assets/empty_images/data_empty.png" alt="" />
          </template>
        <el-table-column
          type="index"
          label="序号"
          align="center"
          width="50">
        </el-table-column>
        <el-table-column
          prop="companyName"
          label="成员单位">
        </el-table-column>
        <el-table-column
          prop="total"
          label="数量 (台套)"
          align="center"
          width="180">
        </el-table-column>
        <el-table-column
          prop="proportion"
          label="数量占比"
          align="center"
          width="180">
        </el-table-column>
      </el-table>
      <VabChartPie v-show="this.cydwradio == '图标'&& tableData.length > 0" :option="option" :title="'仪器设备总数量'"/>
      <div class="emptyImg"   v-show="this.cydwradio == '图标' && tableData.length == 0">
        <img src="@/assets/empty_images/data_empty.png" alt="">
      </div>
    </SmallCard>
  </div>
</template>

<script>
  import SmallCard from '@/views/common/SmallCard'
  // import { instrumentsOfMemberUnits } from '@/api/sam/instrument'
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  export default {
    name: "MemberUnitsDetail",
    components: {
      SmallCard,
      VabChartPie
    },
    data() {
      return {
        cydwradio: '表格',
        tableData: [],
        option: {}
      }
    },
    created() {
      this.getData()
    },
    methods: {
      async getData() {
        let params = {
          number: '10'
        }
        // let { data } = await instrumentsOfMemberUnits(params)
        // this.tableData = data
        // let xData = []
        // let yData = []
        // data.map(item => {
        //   xData.unshift(item.total)
        //   yData.unshift(item.companyName)
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow'
        //     }
        //   },
        //   // legend: {},
        //   grid: {
        //     left: '3%',
        //     right: '4%',
        //     bottom: '3%',
        //     top: '2%',
        //     containLabel: true
        //   },
        //   xAxis: {
        //     type: 'value',
        //     boundaryGap: [0, 0.01]
        //   },
        //   yAxis: {
        //     type: 'category',
        //     data: yData
        //   },
        //   series: [
        //     {
        //       name: '2011',
        //       type: 'bar',
        //       data: xData,
        //       barWidth: 20
        //     }
        //   ]
        // }
      },
      
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          
          let sum = 0;
          const values = data.map(item => item[column.property])
          values.forEach(function(item){
            if(typeof(item) == 'string' && item.indexOf('%') > 0){
              if(item == null){
                item = 0
              }
              item = Number(item.split('%')[0])
              var aaa = Number(item)
              if(!isNaN(aaa)){
                sum = sum + Number(item)
              }else{
                sum = null
              }
            }else if(Number(item)) {
              sum = sum + Number(item)
            }
          })
          if (index === 1) {
            sums[index] = '合计';
            return;
          }
          if (index === 2) {
            sums[index] = sum;
            return;
          }
          if (index === 3) {
            sums[index] = sum.toFixed(2) + "%";
            return;
          }
        });
        return sums;
      }
    },
  }
</script>

<style lang="scss" scoped>
   .emptyImg {
      height: auto;
      display: flex;
      justify-content: center;
      align-items: center;
    }
</style>