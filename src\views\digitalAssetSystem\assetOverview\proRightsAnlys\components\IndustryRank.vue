<template>
  <div class="industryRank-container">
    <el-table
      border
      :data="tableDataPh"
      max-height="750px"
    >
      <el-table-column
        align="center"
        label="行业户数排名"
        min-width="40%"
        prop="name"
      />
      <el-table-column
        align="center"
        label="户数"
        min-width="30%"
        prop="total"
      />
      <el-table-column
        align="center"
        label="占比"
        min-width="30%"
        prop="proportion"
      >
      <template slot-scope="scope">
          <span>{{ scope.row.proportion + '%' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { getNumberIndustryHouseholds } from '@/api/sam/property'
  export default {
    name: "IndustryRank",
    data() {
      return {
        tableDataPh: [
        ],
      }
    },
    created() {
      this.getTableData()
    },
    methods: {
      async getTableData() {
        // let { data } = await getNumberIndustryHouseholds()
        // this.tableDataPh = data
      }
    },
  }
</script>

<style lang="scss" scoped>

</style>