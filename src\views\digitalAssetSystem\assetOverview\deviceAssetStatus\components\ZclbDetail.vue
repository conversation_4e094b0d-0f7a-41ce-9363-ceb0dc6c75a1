<template>
  <div class="container">
    <VabChartPie :option="option"/>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { assetClassification } from '@/api/sam/instrument'
  export default {
    name: "ZclbDetail",
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {}
      }
    },
    created() {
      this.getEchartData()
    },
    methods: {
      async getEchartData() {
        // let { data } = await assetClassification()
        // let xData = []
        // let yData1 = []
        // let yData2 = []
        // data.forEach(item => {
        //   xData.push(item.lpvName)
        //   yData1.push(item.zcfaBookValue)
        //   yData2.push(item.total)
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow'
        //     }
        //   },
        //   legend: {},
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)'],
        //   grid: {
        //     left: '5%',
        //     right: '10%',
        //     bottom: '5%',
        //     top:'15%',
        //     containLabel: true
        //   },
        //   xAxis: {
        //     name: '资产类别',
        //     type: 'category',
        //     data: xData
        //   },
        //   yAxis: [{
        //     type: 'value',
        //     name: '账面原值 (千万)'
        //   },{
        //     type:'value',
        //     name: '资产总数 (台套)'
        //   }],
        //   series: [
        //     {
        //       name: '账面原值',
        //       data: yData1,
        //       type: 'line',
        //       // areaStyle: {}
        //     },
        //     {
        //       name: '资产总数',
        //       data: yData2,
        //       type: 'bar',
        //       barWidth: 30
        //     },
        //   ]
        // }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
  }
  
  :deep() {
    .echarts {
      width: 100%;
      height: 252px;
    }
  }
</style>