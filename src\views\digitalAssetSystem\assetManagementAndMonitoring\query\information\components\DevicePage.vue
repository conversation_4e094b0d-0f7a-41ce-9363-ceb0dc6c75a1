<!--设备资产管理页面-->
<template>
  <div class="custom-table-container">

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.zcfaAssetsName" placeholder="请输入资产名称" class="inputW" />
            </el-form-item>
            <el-form-item label="产权单位:">
              <el-input v-model="searchForm.companyName" placeholder="请输入产权单位" class="inputW" />
            </el-form-item>
            <el-form-item label="资产状态:">
              <el-select v-model="searchForm.zcfaAssetsState" placeholder="请选择资产状态" class="inputW" clearable>
                <el-option label="正常" value="正常" />
                <el-option label="闲置" value="闲置" />
                <el-option label="报废" value="报废" />
                <el-option label="维修" value="维修" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">综合查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
<!--            <el-button type="primary" @click="handleAdd" icon="el-icon-plus">添加</el-button>-->
<!--            <el-button icon="el-icon-download" type="primary" @click="handleExportTmpl">导出模板</el-button>-->
<!--            <el-upload :show-file-list="false" action="" :accept="fileAccept" auto-upload :disabled="fileUploadBtnText == '正在导入'" :http-request="uploadFile" style="margin-left: 10px;display: inline-block;">-->
<!--              <el-button type="primary" :icon="uploadBtnIcon">导入</el-button>-->
<!--            </el-upload>-->
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="deviceTable" :data="list" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="listLoading" row-key="zcfaId">
        <el-table-column type="index" label="序号" width="60" align="center" label-class-name="number" />
        <el-table-column prop="zcfaAssetsName" label="资产名称" width="150" show-overflow-tooltip align="center" label-class-name="zcfaAssetsName" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" label-class-name="companyName" />
        <el-table-column prop="zcfaAssetsState" label="资产状态" width="100" align="center" label-class-name="zcfaAssetsState">
          <template slot-scope="scope">
            <el-tag :type="getStatusTagType(scope.row.zcfaAssetsState)" size="small">
              {{ scope.row.zcfaAssetsState }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="zcfaType" label="资产类型" width="100" align="center" label-class-name="zcfaType" />
        <el-table-column prop="zcfaManufactor" label="制造厂商" width="120" show-overflow-tooltip align="center" label-class-name="zcfaManufactor" />
        <el-table-column prop="zcfaModel" label="型号" width="120" show-overflow-tooltip align="center" label-class-name="zcfaModel" />
        <el-table-column prop="zcfaSerialNumber" label="序列号" width="100" align="center" label-class-name="zcfaSerialNumber" />
        <el-table-column prop="zcfaBookValue" label="账面原值(万元)" width="130" align="center" label-class-name="zcfaBookValue">
          <template slot-scope="scope">
            {{ scope.row.zcfaBookValue ? Number(scope.row.zcfaBookValue).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaNetbookValue" label="账面净值(万元)" width="130" align="center" label-class-name="zcfaNetbookValue">
          <template slot-scope="scope">
            {{ scope.row.zcfaNetbookValue ? Number(scope.row.zcfaNetbookValue).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaProductDate" label="生产日期" width="110" align="center" label-class-name="zcfaProductDate">
          <template slot-scope="scope">
            {{ scope.row.zcfaProductDate ? scope.row.zcfaProductDate.split(' ')[0] : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaRecordDate" label="入账日期" width="110" align="center" label-class-name="zcfaRecordDate">
          <template slot-scope="scope">
            {{ scope.row.zcfaRecordDate ? scope.row.zcfaRecordDate.split(' ')[0] : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="zcfaStoragePlace" label="存放地点" width="120" show-overflow-tooltip align="center" label-class-name="zcfaStoragePlace" />
        <el-table-column prop="zcfaSerialNum" label="资产编号" width="120" show-overflow-tooltip align="center" label-class-name="zcfaSerialNum" />

        <el-table-column prop="zcfaUse" label="资产用途" width="120" show-overflow-tooltip align="center" label-class-name="zcfaUse" />
        <el-table-column prop="zcfaFundsSource" label="资金来源" width="100" show-overflow-tooltip align="center" label-class-name="zcfaFundsSource" />
        <el-table-column prop="zcfaUsefulLife" label="折旧年限(年)" width="110" align="center" label-class-name="zcfaUsefulLife" />
        <el-table-column prop="zcfaIfAssets" label="是否固定资产" width="110" align="center" label-class-name="zcfaIfAssets" />
        <el-table-column prop="zcfaContacts" label="联系人" width="100" show-overflow-tooltip align="center" label-class-name="zcfaContacts" />
        <el-table-column prop="zcfaContactsPhone" label="联系电话" width="120" show-overflow-tooltip align="center" label-class-name="zcfaContactsPhone" />
        <el-table-column prop="zcfaRemark" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zcfaRemark" />
        <el-table-column label="操作" width="120" fixed="right" align="center" label-class-name="_lesoper">
          <template slot-scope="scope">
<!--            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>-->
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
<!--            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 添加/编辑弹窗组件 -->
    <device-add-dialog ref="deviceAddDialog" @refresh="fetchData" />

    <!-- 详情弹窗组件 -->
    <device-detail-dialog ref="deviceDetail" />

    <!-- 高级查询弹窗组件 -->
    <device-advanced-search-dialog ref="deviceAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getDevicesList, deleteDevice } from '@/api/digitalAssetSystem/device'
import DeviceAddDialog from './DeviceAddDialog.vue'
import DeviceDetailDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/device/components/DeviceDetailDialog.vue'
import DeviceAdvancedSearchDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/device/components/DeviceAdvancedSearchDialog.vue'
import { exportRearEnds } from '@/api/exportExcel'
import { exportTmpl } from '@/api/excel'
import { baseURL } from '@/config'
import axios from 'axios'
import store from '@/store'

export default {
  name: "index",
  components: {
    DeviceAddDialog,
    DeviceDetailDialog,
    DeviceAdvancedSearchDialog
  },
  data () {
    return {
      height: this.$baseTableHeight(1, 2.7),
      list: [],
      total: 0,
      listLoading: true,
      searchForm: {
        zcfaAssetsName: '',
        companyName: '',
        zcfaAssetsState: '',
        zcfaType: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaSerialNum: '',
        zcfaStoragePlace: '',
        zcfaUse: '',
        zcfaFundsSource: '',
        zcfaIfAssets: '',
        zcfaProductDateStart: '',
        zcfaProductDateEnd: '',
        zcfaRecordDateStart: '',
        zcfaRecordDateEnd: '',
        zcfaSerialNumber: '',
        zcfaBookValue: '',
        zcfaNetbookValue: '',

        zcfaUsefulLife: '',
        zcfaContacts: '',
        zcfaContactsPhone: '',
        zcfaRemark: '',
        pageNo: 1,
        pageSize: 10
      },
      productDateRange: [],
      recordDateRange: [],
      fileUploadBtnText: "导入",
      uploadBtnIcon: "el-icon-upload2",
      fileAccept: ".xls,.xlsx"
    }
  },
  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.listLoading = true
      const query = {
        ...this.searchForm
      }

      getDevicesList(query).then(response => {
        if (response && response.data) {
          this.list = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
        this.$message.error('获取数据失败')
      })
    },



    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },
    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        if (key !== 'pageNo' && key !== 'pageSize') {
          this.searchForm[key] = ''
        }
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.productDateRange = []
      this.recordDateRange = []
      this.fetchData()
    },
    handleProductDateChange (val) {
      if (val && val.length === 2) {
        this.searchForm.zcfaProductDateStart = val[0]
        this.searchForm.zcfaProductDateEnd = val[1]
      } else {
        this.searchForm.zcfaProductDateStart = ''
        this.searchForm.zcfaProductDateEnd = ''
      }
    },
    handleRecordDateChange (val) {
      if (val && val.length === 2) {
        this.searchForm.zcfaRecordDateStart = val[0]
        this.searchForm.zcfaRecordDateEnd = val[1]
      } else {
        this.searchForm.zcfaRecordDateStart = ''
        this.searchForm.zcfaRecordDateEnd = ''
      }
    },

    handleAdvancedSearch () {
      this.$refs.deviceAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleAdd () {
      this.$refs.deviceAddDialog.showDialog()
    },

    handleEdit (row) {
      this.$refs.deviceAddDialog.showDialog(row)
    },

    handleDelete (row) {
      this.$confirm(`确定要删除资产名称为"${row.zcfaAssetsName}"的设备信息吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const loading = this.$loading({
          lock: true,
          text: '删除中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          const response = await deleteDevice(row.zcfaId)
          if (response && response.code == 200) {
            this.$message.success('删除成功')
            this.fetchData() // 刷新列表
          } else {
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        } finally {
          loading.close()
        }
      }).catch(() => {
        // 用户取消删除，不做任何操作
      })
    },

    handleDetail (row) {
      this.$refs.deviceDetail.showDialog(row)
    },

    getStatusTagType (status) {
      const statusMap = {
        '正常': 'success',
        '闲置': 'warning',
        '报废': 'danger',
        '维修': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    // 导出excel模板
    async handleExportTmpl () {
      //导出模板
      let params = { fileName: "设备资产信息模板.xls", excelIstmpl: true, excelTmplListIds: "INVESTSORTS", excelTmplListCcs: "tptSort", uncols: "createdTime,createdBy,updatedTime," }
      let qf = exportRearEnds("#deviceTable", params)

      // 直接从excelExps中移除uncols指定的列
      if (params.uncols && qf.excelExps && qf.excelExps.length > 0) {
        let uncolsArray = params.uncols.split(',').filter(col => col.trim() !== '')

        // 遍历每个表头行
        qf.excelExps.forEach(headerRow => {
          // 过滤掉uncols中指定的列
          for (let i = headerRow.length - 1; i >= 0; i--) {
            if (uncolsArray.includes(headerRow[i].field)) {
              headerRow.splice(i, 1)
            }
          }
        })
      }

      const data = await exportTmpl(qf)
      if (data.code == 200) {
        window.open(baseURL + "/" + data.msg)
      } else {
        this.$message({ message: '导出模板操作失败!', type: 'warning' })
      }
    },

    // 导入文件
    async uploadFile (param) {
      let file = param.file
      let fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
      let acceptArr = this.fileAccept + ","
      if (acceptArr.indexOf(fileType + ",") == -1) {
        this.$message({
          message: `${file.name}文件类型不符，请重新选择${this.fileAccept}格式文件`, type: "warning"
        })
      } else {
        this.uploadBtnIcon = "el-icon-loading"
        this.fileUploadBtnText = "正在导入"

        // 创建FormData对象
        let formdata = new FormData()
        formdata.append('file', file)
        formdata.append('fileparam', '对应excel列的实体类名称,逗号分隔，具体此参数什么格式内容请与后端商量')
        let url = baseURL + "/zcgl-fixed-assets/upload"
        try {

          axios({
            url,
            method: 'post',
            data: formdata,
            headers: {
              'Authorization': store.getters['user/token'],
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.data.code == 200) {
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$message.success('导入成功！')
              this.fetchData()
            } else {
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$message.error(res.data.msg)
              if (res.data.data) {
                window.open(baseURL + "/" + res.data.data)
              }
            }
          }).catch(err => {
            this.uploadBtnIcon = "el-icon-upload2"
            this.fileUploadBtnText = "导入"
            this.$message.error('导入失败！')
          })

        } catch (error) {
          this.uploadBtnIcon = "el-icon-upload2"
          this.fileUploadBtnText = "导入"
          this.$message.error('导入失败！')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  // justify-content: space-between;
}
.inputW {
  width: 250px;
}

.custom-table-container {
  padding: 0px;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
