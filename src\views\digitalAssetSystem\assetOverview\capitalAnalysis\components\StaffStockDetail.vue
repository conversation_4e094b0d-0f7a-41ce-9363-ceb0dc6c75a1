<template>
  <div class="staffStockDetail-container">
    <el-row :gutter="24">
      <el-col :span="8">
        <SmallCard :title="'数据统计'">
          <DataStat />
        </SmallCard>
      </el-col>
      <el-col :span="8">
        <el-row :gutter="24">
          <el-col :span="12">
            <SmallCard :title="'员工持股形式'">
              <StaffStockType />
            </SmallCard>
          </el-col>
          <el-col :span="12">
            <SmallCard :title="'持股企业性质'">
              <StockCompanyType />
            </SmallCard>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <SmallCard :title="'持股企业法人层级'">
          <StockLegalGrade />
        </SmallCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import SmallCard from '@/views/common/SmallCard'
  import DataStat from './DataStat'
  import StaffStockType from './StaffStockType'
  import StockCompanyType from './StockCompanyType'
  import StockLegalGrade from './StockLegalGrade'
  export default {
    name: "StaffStockDetail",
    components: {
      SmallCard,
      DataStat,
      StaffStockType,
      StockCompanyType,
      StockLegalGrade
    },
  }
</script>

<style lang="scss" scoped>
  .staffStockDetail-container {
    width: 100%;
    height: 100%;
    .el-row,.el-col {
      height: 100%;
    }
  }
</style>