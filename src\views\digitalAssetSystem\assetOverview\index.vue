<template>
  <div class="asset-overview">
    <div class="filteWrap">
      <el-select v-model="departmentValue" filterable placeholder="请选择单位" @change="handleDepartmentChange">
        <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>

    <div class="grid-2">
      <!-- 左上：资产概览 -->
      <div class="w-s">
        <CardBox title="资产概览">
          <div class="kpi-cards">
            <div class="kpi-row two">
              <div class="kpi-card pr curP" @click="showQy('kg')">
                <div class="icon-box"><i class="el-icon-s-data" /></div>
                <div class="kpi-main-new">
                  <div class="unit-new">控股</div>
                  <div class="kpi-number-new">{{ leftTopData.kongu }}</div>
                  <div class="unit-new">家</div>
                </div>
              </div>
              <div class="kpi-card pr curP" @click="showQy('cg')">
                <div class="icon-box"><i class="el-icon-s-custom" /></div>
                <div class="kpi-main-new">
                  <div class="unit-new">参股</div>
                  <div class="kpi-number-new">{{ leftTopData.cangu }}</div>
                  <div class="unit-new">家</div>
                </div>
              </div>
            </div>
            <div class="kpi-row three-new ">
              <div class="kpi-card-new curP" @click="tkBtn('td', '土地资产')">
                <div class="tnt-new">土地</div>
                <div class="botP">
                  <div class="kpi-number-new">{{ leftTopData.landArea }}</div>
                  <div class="unit-new">万亩</div>
                </div>
              </div>
              <div class="kpi-card-new curP" @click="tkBtn('fw', '房屋资产')">
                <div class="tnt-new">房屋</div>
                <div class="botP">
                  <div class="kpi-number-new">{{ leftTopData.houseArea }}</div>
                  <div class="unit-new">万平方米</div>
                </div>
              </div>
              <div class="kpi-card-new curP" @click="tkBtn('yqsb', '设备资产')">
                <div class="tnt-new">仪器设备</div>
                <div class="botP">
                  <div class="kpi-number-new">{{ leftTopData.fixArea }}</div>
                  <div class="unit-new">万台</div>
                </div>
              </div>
            </div>
          </div>
          <div class="sub-panels">
            <div class="sub-card">
              <div class="section-title"><span class="bar"></span>基金</div>
              <div class="fund-row two">
                <div class="fund-item curP" @click="showFund('主导基金','主导基金')">
                  <div class="value"><span class="strong">{{ zdcyCount[0]?.count }}</span><span class="suffix">支</span></div>
                  <div class="label">主导基金数</div>
                </div>
                <div class="fund-item curP" @click="showFund('参与基金','参与基金')">
                  <div class="value"><span class="strong">{{ zdcyCount[1]?.count }}</span><span class="suffix">支</span></div>
                  <div class="label">参与基金数</div>
                </div>
              </div>
              <div class="fund-row three">
                <div class="fund-item curP" @click="showFund('','认缴金额')">
                  <div class="value"><span class="strong">{{ rjsjCount.zrjje }}</span><span class="suffix">亿元</span></div>
                  <div class="label">认缴金额</div>
                </div>
                <div class="fund-item curP" @click="showFund('','实缴金额')">
                  <div class="value"><span class="strong">{{ rjsjCount.zsjje }}</span><span class="suffix">亿元</span></div>
                  <div class="label">实缴金额</div>
                </div>
                <div class="fund-item curP" @click="showTzxms">
                  <div class="value"><span class="strong">{{tzxms}}</span><span class="suffix">项</span></div>
                  <div class="label">投资项目数</div>
                </div>
              </div>
            </div>
            <div class="sub-card flex_1">
              <div class="section-title"><span class="bar"></span>评估与估值</div>
              <div class="eval-grid">
                <div class="eval-card">
                  <div class="eval-top">
                    <div class="title">评估项目</div>
                    <div class="num"><span class="strong">{{pgxmObj.PROJECTCOUNT || 0}}</span><span class="suffix">个</span></div>
                  </div>
                  <div class="eval-list">
                    <div>累计账面值：<span class="fw">{{pgxmObj.ORIGINALVALUE || 0}} 亿元</span></div>
                    <div>累计评估值：<span class="fw">{{pgxmObj.ESTIMATEDVALUE || 0}} 亿元</span></div>
                    <div>累计增值率：<span class="fw">{{pgxmObj.PERCENTAGE || 0}} %</span></div>
                  </div>
                </div>
                <div class="eval-card">
                  <div class="eval-top">
                    <div class="title">估值项目</div>
                    <div class="num"><span class="strong">{{gzxmObj.PROJECTCOUNT || 0}}</span><span class="suffix">个</span></div>
                  </div>
                  <div class="eval-list">
                    <div>累计账面值：<span class="fw">{{gzxmObj.ORIGINALVALUE || 0}} 亿元</span></div>
                    <div>累计评估值：<span class="fw">{{gzxmObj.ESTIMATEDVALUE || 0}} 亿元</span></div>
                    <div>累计增值率：<span class="fw">{{gzxmObj.PERCENTAGE || 0}} %</span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardBox>
      </div>
      <!-- 右上：资产分布与地图 -->
      <div class="card-box-container-new">
        <div class="card-header-new">
          <div class="disCenter">
            <div class="imgWrap">
              <img style="width: 26px;height: 16px" src="@/assets/common_images/icon_zs.png">
            </div>
            <el-tabs v-model="assetTab" class="mb16Tab" @tab-click="handleMapTabClick">
              <el-tab-pane label="企业产权" name="cq" />
              <el-tab-pane label="土地" name="td" />
              <el-tab-pane label="房屋" name="fc" />
              <el-tab-pane label="仪器设备" name="sb" />
            </el-tabs>
          </div>

        </div>

        <div class="charts-row">
          <div class="innerBox">
            <div class="innerL">
              <CqComponent v-if="assetTab === 'cq'" :echartsData="rightTopEchartsData.cq || []" :allData="rightTopEchartsData.cqhz || {}" :propDeptId="departmentValue" />
              <TdComponent v-else-if="assetTab === 'td'" :echartsData="rightTopEchartsData.td || []" :allData="rightTopEchartsData.tdhz || {}" />
              <FcComponent v-else-if="assetTab === 'fc'" :echartsData="rightTopEchartsData.fc || []" :allData="rightTopEchartsData.fchz || {}" />
              <SbComponent v-else-if="assetTab === 'sb'" :echartsData="rightTopEchartsData.sb || []" :allData="rightTopEchartsData.sbhz || {}" />
            </div>
            <div class="innerR">
              <MapChart :data="rightTopMapEchartsData" :type="assetTab" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="grid-2 mt16" style="margin-top: 16px;"> -->
    <div class="mt16" style="margin-top: 16px;">
      <!-- 左下：企业规模情况 -->
      <div class="w-s a-s">
        <CardBox :title="`企业规模情况(${qygmYear}年度)`">
          <el-tabs v-model="corpTab" type="card" class="mb16" @tab-click="handleClick">
            <el-tab-pane label="资产总额" name="0" />
            <el-tab-pane label="负债总额" name="1" />
            <el-tab-pane label="利润总额" name="2" />
          </el-tabs>
          <el-table :data="tableData" size="small" border>
            <el-table-column prop="zcbiTzfx" label="一巩固三做强" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="showYggszq(scope.row)">{{scope.row.zcbiTzfx}}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="total1" label="超过50亿元" align="center" />
            <el-table-column prop="total2" label="1亿元-50亿元" align="center" />
            <el-table-column prop="total3" label="1亿元以下" align="center" />
          </el-table>
        </CardBox>
      </div>
      <!-- 右下：能力数量及金额分布 -->
      <div class="w-s right-s a-s">
        <CardBox title="资产能力">
          <AbilityBarChart ref="abilityBarChart" :echartsData="rightBottomEchartsData" />
        </CardBox>
      </div>
    </div>

    <NlSecondDialog ref="nlSecondDialog" />
    <ThreeCatDialog ref="threeCatDialog" />
    <FwDialog ref="fwDialog" />
    <DeviceDialog ref="deviceDialog" />
    <QygmqkDialog ref="qygmqkDialog" />
    <FundDialog ref="fundDialog" />
    <TzxmsDialog ref="tzxmsDialog" />
  </div>
</template>

<script>
import CardBox from 'common/CardBox'
import AbilityBarChart from '@/views/digitalAssetSystem/assetOverview/components/AbilityBarChart'
import MapChart from '@/views/digitalAssetSystem/assetOverview/components/MapChart'
import CqComponent from '@/views/digitalAssetSystem/assetOverview/components/CqComponent'
import TdComponent from '@/views/digitalAssetSystem/assetOverview/components/TdComponent'
import FcComponent from '@/views/digitalAssetSystem/assetOverview/components/FcComponent'
import SbComponent from '@/views/digitalAssetSystem/assetOverview/components/SbComponent'
import NlSecondDialog from '@/views/digitalAssetSystem/assetOverview/components/NlSecondDialog'
import ThreeCatDialog from '@/views/digitalAssetSystem/assetOverview/components/ThreeCatDialog'
import FwDialog from '@/views/digitalAssetSystem/assetOverview/components/FwDialog'
import DeviceDialog from '@/views/digitalAssetSystem/assetOverview/components/DeviceDialog'
import QygmqkDialog from '@/views/digitalAssetSystem/assetOverview/components/QygmqkDialog'
import FundDialog from '@/views/digitalAssetSystem/assetOverview/components/FundDialog'
import TzxmsDialog from '@/views/digitalAssetSystem/assetOverview/components/TzxmsDialog'
import { mapGetters } from 'vuex'
import { getCapabilityT4, getAssetT4 } from '@/api/digitalAssetSystem/capabilityIndex'
import { getLeftTopData, getLeftBottomData, getRightTopData, getIndicatorValue, getassetBasicEj, getTaskDataByIndicatorAndCompany } from 'api/digitalAssetSystem/assetOverview'

export default {

  components: {
    CardBox,
    AbilityBarChart,
    MapChart,
    CqComponent,
    TdComponent,
    FcComponent,
    SbComponent,
    NlSecondDialog,
    ThreeCatDialog,
    FwDialog,
    DeviceDialog,
    QygmqkDialog,
    FundDialog,
    TzxmsDialog
  },

  data () {
    return {
      assetTab: 'cq',
      corpTab: '0',
      tableData: [],
      leftTopData: {},
      rightTopEchartsData: {},
      rightTopMapEchartsData: [],
      rightBottomEchartsData: [],
      tableDataList: [],
      MapEchartsData: [],
      tzxms: 0,
      pgxmObj: {},
      gzxmObj: {},
      departmentOptions: [],
      departmentValue: '',
      deFaultId: '',
      qygmYear: '2023',
      zdcyCount: [],
      rjsjCount: {}
    }
  },

  computed: {
    ...mapGetters({ loginUser: 'user/loginUser' }),
  },

  created () {
    this.init()
    getassetBasicEj({}).then(res => {
      this.deFaultId = res.data.zcbiId
      if (res.data.children) {
        this.departmentOptions = res.data.children
        this.departmentOptions.map(rs => {
          rs.value = rs.zcbiId
          rs.label = rs.zcbiName
        })
        this.departmentOptions.unshift({ value: '', label: res.data.zcbiName })
        this.refreshAllData()
      }
    })
  },

  methods: {
    showTzxms () {
      this.$refs.tzxmsDialog.handleShow('投资项目')
    },

    showFund (type, title) {
      this.$refs.fundDialog.handleShow(type, title)
    },

    showYggszq (row) {
      this.$refs.qygmqkDialog.handleShow(row.zcbiTzfx, this.departmentValue ? this.departmentValue : this.deFaultId, '企业规模情况')
    },

    showQy (type) {
      if (type == 'kg') {
        this.$refs.nlSecondDialog.handleShow({ sfbb: "是", level: '', sfssgs: '', zzxs: '' }, this.departmentValue, '控股企业')
      } else {
        this.$refs.nlSecondDialog.handleShow({ sfbb: "否", level: '', sfssgs: '', zzxs: '' }, this.departmentValue, '参股企业')
      }
    },

    tkBtn (val, name) {
      if (val == 'td') {
        this.$refs.threeCatDialog.handleShow({}, this.departmentValue)
      } else if (val == 'fw') {
        this.$refs.fwDialog.handleShow({}, this.departmentValue)
      } else {
        this.$refs.deviceDialog.handleShow({}, this.departmentValue)
      }
    },

    // 构建查询参数（包含zcbiId）
    buildQueryParams (additionalParams = {}) {
      const baseParams = {
        year: new Date().getFullYear() + ''
      }

      // 添加单位ID参数
      // 只有当departmentValue不为空字符串时才添加zcbiId
      // 如果departmentValue为空字符串（选择"全部"），则不传递zcbiId参数
      if (this.departmentValue && this.departmentValue !== '') {
        baseParams.zcbiId = this.departmentValue
        // console.log('传递zcbiId参数:', this.departmentValue)
      }

      const finalParams = { ...baseParams, ...additionalParams }
      // console.log('最终请求参数:', finalParams)
      return finalParams
    },

    // 单位选择改变事件
    handleDepartmentChange (value) {
      // console.log('单位选择改变:', value)
      // 重新获取所有数据
      this.assetTab = 'cq'
      this.refreshAllData()
    },

    // 刷新所有数据
    refreshAllData () {
      const params = this.buildQueryParams()
      this.getLeftTopData(params)
      this.getLeftBottomData(params)
      this.getRightTopEchartsData(params)
      this.getRightBottomEchartsData(params)
      this.getIndicatorValue()
      this.$nextTick(() => {
        this.$refs.abilityBarChart.setUnit(this.departmentValue)
      })
    },

    // 初始化数据，避免切换tab时数据重复加载
    async init () {
      const arr = []
      this.tableDataList.push(arr)
      this.tableDataList.push(arr)
      this.tableDataList.push(arr)
      this.MapEchartsData.push(arr)
      this.MapEchartsData.push(arr)
      this.MapEchartsData.push(arr)
      this.MapEchartsData.push(arr)

      getIndicatorValue({ indicatorCode: "COMPANY_FINANCE_YEAR" }).then(res => {
        // console.log("🚀🚀 ~ 年份 ~ 🚀🚀", res)
        this.qygmYear = res.msg
      })

      getIndicatorValue({ indicatorCode: "FUNDS_COUNT" }).then(res => {
        // console.log("🚀🚀 ~ 基金 ~ 🚀🚀", res)
        this.zdcyCount = res.data.data
      })

      getIndicatorValue({ indicatorCode: "FUNDS_SUM" }).then(res => {
        // console.log("🚀🚀 ~ 基金 ~ 🚀🚀", res)
        this.rjsjCount = res.data.data[0]
      })
    },

    // 获取资产概览数据
    async getLeftTopData (params) {
      if (this.departmentValue && this.departmentValue !== '') {
        const { data, code, msg } = await getLeftTopData(params)
        if (code == 200) {
          this.leftTopData = data
        } else {
          this.$message.error(msg)
        }
      } else {
        const { data, code, msg } = await getLeftTopData({ ...params, zcbiId: '' })
        if (code == 200) {
          this.leftTopData = data
        } else {
          this.$message.error(msg)
        }
      }

    },

    getIndicatorValue () {
      const params = this.buildQueryParams({ indicatorCode: 'TZ_PROJECT_COUNT' })
      getIndicatorValue(params).then(res => {
        console.log("🚀🚀 ~ getIndicatorValue ~ res ~ 🚀🚀", res)
        this.tzxms = res.data.data[0].COUNT
      })

      const assessmentParams = this.buildQueryParams({ indicatorCode: 'ASSESSMENT_DATA' })
      getIndicatorValue(assessmentParams).then(res => {
        this.pgxmObj = res.data.data.find(item => item.DATATYPE === 'assessment')
        this.gzxmObj = res.data.data.find(item => item.DATATYPE === 'estimated')
        // console.log("🚀🚀 ~ 截个屏 s~ 🚀🚀",this.pgxmObj)
      })
    },

    // 获取企业规模情况数据
    async getLeftBottomData (params) {
      const { data, code, msg } = await getLeftBottomData(params)
      if (code == 200) {
        this.tableDataList[0] = data.zcze
        this.tableDataList[1] = data.fzze
        this.tableDataList[2] = data.lrze
        this.tableData = this.tableDataList[0]
      } else {
        this.$message.error(msg)
      }
    },

    // 获取企业产权、土地、房屋、设备数据
    async getRightTopEchartsData (params) {
      getRightTopData(params).then(res => {
        this.rightTopEchartsData = res.data
      })

      getTaskDataByIndicatorAndCompany({ indicatorCode: 'TASK_COMPANY_REGION_DISTRIBUTION', zcbiId: this.departmentValue }).then(res => {
        // console.log("🚀🚀 ~ 企业企业 ~ 🚀🚀", res)
        this.MapEchartsData[0] = res.data
        this.rightTopMapEchartsData = this.MapEchartsData[0]
      })
    },

    // 获取资产能力数量及金额分布数据
    async getRightBottomEchartsData (params) {
      // 默认数据结构，包含6个能力类别
      let arr = [
        { root_category: "需求论证能力", tjz: 0, netvalue: 0 },
        { root_category: "研发设计能力", tjz: 0, netvalue: 0 },
        { root_category: "生产制造能力", tjz: 0, netvalue: 0 },
        { root_category: "实验论证能力", tjz: 0, netvalue: 0 },
        { root_category: "运维保障能力", tjz: 0, netvalue: 0 },
        { root_category: "信息基础和设施支撑能力", tjz: 0, netvalue: 0 }
      ]

      // let newParams = { ...params, zcbiId: params.zcbiId ? params.zcbiId : this.deFaultId }
      let newParams = {}

      try {
        // 获取能力数据 (用于netvalue)
        const { data: capabilityData, code, msg } = await getCapabilityT4(newParams)
        if (code != 200) {
          this.$message.error(msg)
          return
        }

        // 获取资产数据 (用于tjz)
        const assetResponse = await getAssetT4(newParams)
        if (!assetResponse || !assetResponse.data) {
          this.$message.error('获取资产数据失败')
          return
        }

        // 合并数据：基于默认的arr数组，用两个接口的数据来补全
        const mergedData = arr.map(defaultItem => {
          // 从能力数据中找到匹配的项 (补全netvalue)
          const capabilityItem = capabilityData.find(item =>
            item.root_category === defaultItem.root_category
          )

          // 从资产数据中找到匹配的项 (补全tjz)
          const assetItem = assetResponse.data.find(item =>
            item.root_category === defaultItem.root_category
          )

          return {
            root_category: defaultItem.root_category,
            tjz: assetItem ? assetItem.tjz : 0,
            netvalue: capabilityItem ? capabilityItem.tjz : 0
          }
        })

        // 更新图表数据
        this.rightBottomEchartsData = mergedData
        // console.log('合并后的数据:', mergedData)

      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      }
    },

    // tab切换
    handleClick () {
      this.tableData = this.tableDataList[this.corpTab * 1]
    },

    // 企业产权、土地、房屋、设备tab切换
    handleMapTabClick () {
      const params = this.buildQueryParams()
      if (this.assetTab === 'cq') {
        this.rightTopMapEchartsData = this.MapEchartsData[0]
      } else if (this.assetTab === 'td') {

        getTaskDataByIndicatorAndCompany({ indicatorCode: 'TASK_LAND_PROVINCE_NO_ESCAPE', zcbiId: this.departmentValue }).then(res => {
          // console.log("🚀🚀 ~ 企业企业 ~ 🚀🚀", res)
          this.MapEchartsData[1] = res.data
          this.rightTopMapEchartsData = this.MapEchartsData[1]
        })
      } else if (this.assetTab === 'fc') {
        getTaskDataByIndicatorAndCompany({ indicatorCode: 'TASK_HOUSE_CURRENT_YEAR_DISTRIBUTION', zcbiId: this.departmentValue }).then(res => {
          // console.log("🚀🚀 ~ 企业企业 ~ 🚀🚀", res)
          this.MapEchartsData[2] = res.data
          this.rightTopMapEchartsData = this.MapEchartsData[2]
        })
      } else if (this.assetTab === 'sb') {
        getTaskDataByIndicatorAndCompany({ indicatorCode: 'TASK_ASSETS_PROVINCE_DISTRIBUTION', zcbiId: this.departmentValue }).then(res => {
          // console.log("🚀🚀 ~ 企业企业 ~ 🚀🚀", res)
          this.MapEchartsData[3] = res.data
          this.rightTopMapEchartsData = this.MapEchartsData[3]
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.asset-overview {
  .mb16 {
    margin-bottom: 16px;
  }
  .mt16 {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
  }
}
.grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}
.kpi-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}
.kpi-row.two {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}
.kpi-row.three {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}
.kpi-card {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}
.icon-box {
  color: #cc1214;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  i {
    font-size: 26px;
  }
}
.kpi-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  flex: 1;
}
.kpi-number {
  font-size: 30px;
  font-weight: 800;
  color: #222;
}
.kpi-meta {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
  color: #606266;
  font-weight: 600;
  .tnt {
    color: #000;
    font-weight: bolder;
  }
  .unit {
    margin-top: 4px;
    font-weight: 400;
    font-size: 20px;
  }
}

.pr {
  padding-right: 50px;
}

.sub-panels {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 12px;
}
.flex_1 {
  display: flex;
  flex-direction: column;
}

.section-title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
  .bar {
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #cc1214;
    border-radius: 2px;
    margin-right: 6px;
  }
}
.fund-row.two {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}
.fund-row.three {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
}
.fund-item {
  background: #fff;
  border: 1px solid #f6e7e8;
  border-radius: 10px;
  padding: 12px 10px;
  text-align: center;
}
.fund-item .value {
  color: #cc1214;
}
.fund-item .strong {
  font-size: 22px;
  font-weight: 800;
  margin-right: 4px;
}
.fund-item .suffix {
  color: #cc1214;
}
.fund-item .label {
  color: #999;
  margin-top: 4px;
  font-size: 12px;
}

.eval-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1.3fr 1fr;
  gap: 12px;
}
.eval-card {
  border: 1px solid #f6e7e8;
  border-radius: 10px;
  padding: 20px 12px 12px 12px;
  background: #fff;
  display: flex;
  flex-direction: column;
}
.eval-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.eval-top .title {
  font-weight: 700;
  color: #333;
}
.eval-top .num {
  color: #cc1214;
}
.eval-top .strong {
  font-size: 22px;
  font-weight: 800;
  margin-right: 4px;
}
.eval-top .suffix {
  margin-left: 2px;
}

.fw {
  font-weight: bolder;
  color: #000;
}
.disCenter {
  display: flex;
}
.eval-list {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #666;
  line-height: 22px;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: space-evenly;
}
.charts-row {
  flex: 1;
  padding: 24px;
  box-sizing: border-box;
}
.innerBox {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: grid;
  // grid-template-columns: 50% 50%;
  display: flex;

  .innerL {
    // width: 100%;
    width: 50%;
    // flex: 1;
    height: 100%;
  }
  .innerR {
    // width: 444px;
    // height: 359px;
    // min-width:370px;
    // height: 100%;
    // width: 370px;
    width: 50%;
    height: 359px;
  }
}

.card-box-container-new {
  // width: 100%;
  min-width: 790px;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 8px 20px 0px rgba(177, 197, 197, 0.08);
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}
.card-header-new {
  padding: 8px 24px 0 24px;
  box-sizing: border-box;
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  align-items: center;
  justify-content: space-between;

  .imgWrap {
    padding: 12px 0 0 0;
    margin-right: 8px;
  }

  ::v-deep .el-tabs__header {
    margin: 0;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    height: 0px;
  }

  ::v-deep .el-tabs__item {
    font-size: 20px;
    font-weight: 600;
    height: 49px;
  }
  ::v-deep .el-tabs__item.is-active {
    font-size: 20px;
    font-weight: 600;
    color: #cc1214;
  }
}

.kpi-main-new {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
}

.kpi-number-new {
  font-size: 30px;
  font-weight: 800;
  line-height: 30px;
  color: #222;
}

.tnt-new {
  color: #000;
  font-weight: bolder;
}

.unit-new {
  font-weight: 400;
  font-size: 20px;
}

.kpi-card-new {
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.botP {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
}

.kpi-row.three-new {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 12px;
}
.filteWrap {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  background-color: #fff;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 10px;
}

.w-s {
  // width: 922px;
}
.right-s {
  // min-width: 790px;
  // width: 790px;
}

/* 自定义弹框样式 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.custom-dialog-container {
  width: 80%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@media (max-width: 768px) {
  .custom-dialog-container {
    width: 95%;
    margin: 20px;
  }
}

.curP {
  cursor: pointer;
}

.a-s {
  width: calc(50% - 8px);
}
</style>