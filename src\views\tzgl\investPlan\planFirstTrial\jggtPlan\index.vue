<template>
  <div class="container">
    <CardBox title="军工固投计划">
      <VabForm
        ref="VabForm"
        :formModel="searchForm"
        :formItem="formItem"
        :formConfig="{ inline: true }"
        width="100%"
        style="margin-top: 3vh"
      />
      <el-table :data="tableData" border :height="height" style="width: 100%">
        <el-table-column
          align="center"
          label="序号"
          show-overflow-tooltip
          width="55"
          type="index"
          label-class-name="number"
        />
        <el-table-column
          prop="tprYear"
          label="年份"
          show-overflow-tooltip
          align="center"
          label-class-name="tprYear"
        />
        <el-table-column
          prop="zcbiName"
          label="单位名称"
          show-overflow-tooltip
          align="center"
          label-class-name="zcbiName"
        />
        <el-table-column
          prop="ampTotalinvest"
          label="本年申报投资总额(万元)"
          show-overflow-tooltip
          align="center"
          label-class-name="ampTotalinvest"
        />
        <el-table-column
          v-if="type == 'investDetail'"
          prop="count"
          label="投资金额项数"
          show-overflow-tooltip
          align="center"
          label-class-name="count"
        />
        <el-table-column
          v-if="type != 'investDetail'"
          prop="tprState"
          label="审核状态"
          show-overflow-tooltip
          align="center"
          label-class-name="tprState"
        />
        <el-table-column label="操作" align="center" width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDetailSee(scope.row, '审核')"
              v-if="scope.row.tprState == '已上报，未审核'"
            >
              审核
            </el-button>
            <el-button
              type="text"
              size="small"
              v-if="
                scope.row.tprState == '审核通过' ||
                scope.row.tprState == '决策已下达'
              "
              @click="handleAuditingReset(scope.row)"
            >
              审核重置
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDetailSee(scope.row, '详情')"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="totalBox">
        {{ '合计本年申报投资总额：' + totalNum + '万元' }}
      </div>
      <DialogCard
        ref="dialogCard"
        :dialogTableVisible="dialogVisible"
        destroy-on-close
        v-if="dialogVisible"
        :close="closeDialigCard"
        :title="dialogCardTitle"
        :flag="true"
        top="1vh"
        height="90vh"
        width="99%"
       
      >
        <jggtProjectDetail
          slot="content"
          ref="dialogContDetail"
          :currentRow="currentRow"
          :tprId="tprId"
          :dialogCardTitle="dialogCardTitle"
           @closeDialigCard="closeDialigCard"
        ></jggtProjectDetail>
      </DialogCard>
    </CardBox>
  </div>
</template>

<script>
import VabForm from 'components/VabForm'
import CardBox from '@/views/common/CardBox'
import Search from './components/jggtProjectSearch'
import DialogCard from '@/views/common/DialogCard'
import jggtProjectDetail from './components/jggtProjectDetail'
import {
  getEquityReview,
  MilitaryAuditReset,
} from '@/api/tzgl/investPlan/jggtPlan.js'
import { getsecondaryunitsList } from 'api/tzgl/investPlan/searchList'
import { mapGetters } from 'vuex'

export default {
  name: 'JggtPlan',
  props: {
    type: String
  },
  components: {
    VabForm,
    CardBox,
    Search,
    DialogCard,
    jggtProjectDetail,
  },
  data() {
    return {
      searchForm: {
        type: '2', // 2代表初报
      },
      formItem: [
        {
          name: 'dateTime',
          type: 'year',
          prop: 'tprYear',
          format: 'yyyy',
          valueFormat: 'yyyy',
          label: '年份',
          placeholder: '请选择年份',
        },
        {
          name: 'selectTree', 
          props: { value: 'zcbiId', label: 'zcbiName', children: 'children' }, 
          options: [], 
          label: '单位名称',
          isChangeParent:true,
          getSelectTreeValue:this.getSelectTreeValue
        },
        this.type != 'investDetail' ? {
          name: 'select',
          options: [
            { label: '未上报', value: '未上报' },
            { label: '已上报，未审核', value: '已上报，未审核' },
            { label: '审核通过', value: '审核通过' },
            { label: '已退回', value: '已退回' },
             { label: '批复下达', value: '批复下达' },
          ],
          prop: 'tprState',
          label: '决策状态',
          placeholder: '请选择决策状态',
        } : {},
        {
          name: 'button',
          prop: 'button',
          label: '查询',
          type: 'primary',
          click: this.getTableData,
        },
        {
          name: 'button',
          prop: 'button',
          label: '重置',
          click: this.handleResetFun,
        },
      ],
      tableData: [],
      height: this.$baseTableHeight(1) - 112,
      totalNum: 0,
      dialogVisible: false,
      queryForm: [],
      tprId: '',
    }
  },
  computed: {
    ...mapGetters({
      loginUser: 'user/loginUser'
    }),
  },
  mounted() {
     this.getsecondaryunitsList()
    this.$set(this.searchForm, 'tprYear', new Date().getFullYear().toString())
    this.getTableData()
  },
  methods: {
     //获取二级单位
    async getsecondaryunitsList() {
      const { data, code, msg } = await getsecondaryunitsList()
      if (code == 200) {
        this.formItem[1].options = [data];
      } else {
        this.$message.error(msg);
      }
    },
    getSelectTreeValue(id, name) {
      this.searchForm.tprCompanyId = id
    },
      handleResetFun(){
         this.$refs.VabForm.$refs.SelectTree[0].clearHandle()
        this.searchForm = {
          type:"2",
        };
        this.$set(this.searchForm, 'tprYear', new Date().getFullYear()+'');
        this.getTableData();
      },
    // 获取表格数据
    async getTableData() {
      Object.assign(this.searchForm, {
        tprCompanyId: this.loginUser.zcbiId
      })
      const { data } = await getEquityReview(this.searchForm)
      this.tableData = data
      this.totalNum = this.calculateTotal(data)
    },
    calculateTotal(data) {
      // 检查 data 是否为数组
      if (!Array.isArray(data)) {
        return 0
      }
      return data.reduce((sum, num) => {
        // 检查 num.TOTAL_DECLARED_AMOUNT 是否为有效的数字
        const amount = parseFloat(num.ampTotalinvest)
        return isNaN(amount) ? sum : sum + amount
      }, 0)
    },
    // 详情按钮点击事件
    async handleDetailSee(row, title) {
      this.tprId = row.tprId || ''
      this.currentRow = row
      this.dialogCardTitle = title
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    async handleAuditingReset(row) {
      const res = await MilitaryAuditReset({
        tprId: row.tprId,
        state: 2,
      })
      if (res.code == 200) {
        this.$message.success(res.msg)

        this.$nextTick(() => {
          this.getTableData()
        })
      } else {
        this.$message.error(res.msg || '请求失败')
      }
    },
    closeDialigCard() {
      this.dialogVisible = false
      this.getTableData()
    },
    //搜索
    handleSearchClick() {},
  },
}
</script>

<style lang="scss" scoped>
.totalBox {
  margin-bottom: 24px;
  width: 100%;
  height: 56px;
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  font-weight: 600;
  font-size: 16px;
  color: #cc1214;
  line-height: 56px;
  text-align: center;
}
::v-deep .card-box-container .card-body {
  padding-bottom: 0;
  padding-top: 0;
}
</style>