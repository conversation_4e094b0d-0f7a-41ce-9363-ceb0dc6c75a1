<template>
  <div class="stockCompanyInfo-container">
    <div class="companyList">
      <div class="companyItem" v-for="(item,index) in companyData" :key="index">
        <img :src="item.url" alt="">
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "StockCompanyInfo",
    components: {},
    data() {
      return {
        activeName: '上市公司',
        companyData: [
          {url: require('@/assets/assetTzfx/img_hk.jpg')},
          {url: require('@/assets/assetTzfx/img_gb.jpg')},
          {url: require('@/assets/assetTzfx/img_tj.jpg')},
          {url: require('@/assets/assetTzfx/img_ys.jpg')},
          {url: require('@/assets/assetTzfx/img_zc.jpg')},
          {url: require('@/assets/assetTzfx/img_wst.jpg')},
          {url: require('@/assets/assetTzfx/img_gr.jpg')},
          {url: require('@/assets/assetTzfx/img_pt.jpg')},
          {url: require('@/assets/assetTzfx/img_sz.jpg')},
          {url: require('@/assets/assetTzfx/img_xp.jpg')},
          {url: require('@/assets/assetTzfx/img_dx.jpg')},
          {url: require('@/assets/assetTzfx/img_ta.jpg')},
          {url: require('@/assets/assetTzfx/img_dxhp.jpg')},
          {url: require('@/assets/assetTzfx/img_sc.jpg')},
          {url: require('@/assets/assetTzfx/img_fh.jpg')},
          {url: require('@/assets/assetTzfx/img_les.jpg')},
          {url: require('@/assets/assetTzfx/img_pt.jpg')},
          {url: require('@/assets/assetTzfx/img_sw.jpg')},
        ]
      }
    },
    methods: {

    },
  }
</script>

<style lang="scss" scoped>
  .stockCompanyInfo-container {
    width: 100%;
    height: 100%;
  }
  .el-tabs {
    height: 100%;
  }
  ::v-deep .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }
  .companyList {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    
  }
  
  .companyItem {
    width: 265px;
    height: 164px;
    background: #FFFFFF;
    box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
    border-radius: 4px;
    border: 1px solid #E4E7ED;
    text-align: center;
    padding: 24px;
    margin-right: 24px;
    margin-bottom: 24px;
    img {
      width: 198px;
      margin: 0 auto;
    }
    p {
      margin: 0px;
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #303133;
    }
  }
  .companyItem:nth-child(6n) {
    margin-left: 0px;
  }
</style>