<template>
  <div class="manageLevel-container">
    <VabChartPie v-show="radio == '图表'" :option="option" :title="'管理级次分布'"/>
    <el-table
      border
      v-show="radio == '表格'"
      :data="tableDataManager"
      max-height="294px"
    >
      <el-table-column
        align="center"
        label="管理级次"
        min-width="35%"
        prop="zcciLevel"
      />
      <el-table-column
        align="center"
        label="户数"
        min-width="35%"
        prop="zcciLevelCount"
      />
      <el-table-column
        align="center"
        label="占比"
        min-width="30%"
        prop="zcciLevelPer"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.zcciAddressPer + '%' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { searchzcciLevel } from '@api/sam/property.js'
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  export default {
    name: "ManageLevel",
    props: ['radio'],
    components: {
      VabChartPie
    },
    data() {
      return {
        tableDataManager: [],
        option: {}
      }
    },
    created() {
      this.getsearchzcciLevel()
      this.$baseEventBus.$on('echartClick',(params,title) => {
        if(title == '管理级次分布') {
          this.$router.push({
            name: 'AssetCaAssayDetail',
            query: {
              data: JSON.stringify(this.tableDataManager),
              title,
              currentName: params.name,
              tableIndex: params.dataIndex
            }
          })
        }
      })
    },
    methods: {
      async getsearchzcciLevel() {
        // let { data } = await searchzcciLevel()
        // this.tableDataManager = data
        // const data1 = []
        // const data2 = []
        // const xData = []
        // data.forEach(item => {
        //   data1.push(item.zcciLevelCount)
        //   data2.push(item.zcciLevelPer)
        //   xData.push(item.zcciLevel)
        // });
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //       textStyle: {
        //         color: '#fff',
        //       },
        //     },
        //   },
        //   grid: {
        //     left: '5%',
        //     right: '5%',
        //     bottom: '15%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       axisLine: {
        //         lineStyle: {
        //           color: '#90979c',
        //         },
        //       },
        //       splitLine: {
        //         show: false,
        //       },
        //       axisTick: {
        //         show: false,
        //       },
        //       splitArea: {
        //         show: false,
        //       },
        //       axisLabel: {
        //         interval: 0,
        //       },
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       name: '户数',
        //       type: 'value',
        //       splitLine: {
        //         show: false,
        //       },
        //       axisLine: {
        //         show: false,
        //       },
        //       axisTick: {
        //         show: false,
        //       },
        //       axisLabel: {
        //         interval: 0,
        //       },
        //       splitArea: {
        //         show: false,
        //       },
        //     },
        //     {
        //       name: '占比',
        //       type: 'value',
        //       axisLabel: {
        //         formatter: '{value} %',
        //       },
        //       max: 100,
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '户数',
        //       type: 'bar',
        //       stack: '户数',
        //       zoom: 1.1,
        //       barWidth: 30,
        //       itemStyle: {
        //         normal: {
        //           color: '#2496FF',
        //           barBorderRadius: 0,
        //         },
        //       },
        //       data: data1,
        //     },
        //     {
        //       name: '占比',
        //       type: 'line',
        //       symbolSize: 10,
        //       yAxisIndex: 1,
        //       symbol: 'circle',
        //       itemStyle: {
        //         normal: {
        //           color: 'rgba(252,230,48,1)',
        //           barBorderRadius: 0,
        //         },
        //       },
        //       data: data2,
        //     },
        //   ],
        // }
      },
    },
  }
</script>