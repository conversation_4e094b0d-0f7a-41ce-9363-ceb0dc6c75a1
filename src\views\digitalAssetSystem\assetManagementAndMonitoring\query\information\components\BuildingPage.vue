<!--实物资产档案-->
<template>
  <div class="custom-table-container">

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产编号:">
              <el-input v-model="searchForm.zchiAssetsNo" placeholder="请输入房屋名称" class="inputW" />
            </el-form-item>
            <el-form-item label="产权证号:">
              <el-input v-model="searchForm.zchiCertificateCode" placeholder="请输入产权证号" class="inputW" />
            </el-form-item>
            <el-form-item label="现状/用途:">
              <el-input v-model="searchForm.zchiUseDescribe" placeholder="请输入现状/用途" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">综合查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
<!--            <el-button type="primary" @click="handleAdd" icon="el-icon-plus">添加</el-button>-->
<!--            <el-button icon="el-icon-download" type="primary" @click="handleExportTmpl">导出模板</el-button>-->
<!--            <el-upload :show-file-list="false" action="" :accept="fileAccept" auto-upload :disabled="fileUploadBtnText == '正在导入'" :http-request="uploadFile" style="margin-left: 10px;display: inline-block;">-->
<!--              <el-button type="primary" :icon="uploadBtnIcon">导入</el-button>-->
<!--            </el-upload>-->
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="buildingsTable" :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <el-table-column type="index" label="序号" width="60" align="center" label-class-name="number" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" label-class-name="zchiAssetsNo" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="120" show-overflow-tooltip align="center" label-class-name="zchiAssetsName" />
        <el-table-column prop="zchiUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" label-class-name="zchiUseDescribe" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" label-class-name="companyName" />
        <el-table-column prop="zchiCertificateCode" label="产权证号" width="100" show-overflow-tooltip align="center" label-class-name="zchiCertificateCode" />
        <el-table-column prop="zchiAddress" label="地理位置" width="140" show-overflow-tooltip align="center" label-class-name="zchiAddress" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="100" show-overflow-tooltip align="center" label-class-name="zchiHouseSource" />
        <el-table-column prop="zchiDate" label="取得时间" width="120" show-overflow-tooltip align="center" label-class-name="zchiDate" />
        <el-table-column prop="zchiArea" label="建筑面积(㎡)" width="120" align="center" label-class-name="zchiArea" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" label-class-name="zchiOriginalValue" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" label-class-name="zchiNetValue" />
        <el-table-column prop="zchiTotalDepreciation" label="累计折旧(万元)" width="140" show-overflow-tooltip align="center" label-class-name="zchiTotalDepreciation" />
        <el-table-column prop="zchiCountry" label="境内/境外" width="100" show-overflow-tooltip align="center" label-class-name="zchiCountry" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="120" align="center" label-class-name="zchiIfAssets" />
        <el-table-column prop="zchiIfExist" label="是否取得房屋产权证" width="150" show-overflow-tooltip align="center" label-class-name="zchiIfExist" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" label-class-name="zchiIfDispute" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" label-class-name="zchiIfDispose" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="120" show-overflow-tooltip align="center" label-class-name="zchiIfMortgage" />
        <el-table-column prop="zchiOperator" label="联系人" width="100" show-overflow-tooltip align="center" label-class-name="zchiOperator" />
        <el-table-column prop="zchiOperatorTel" label="联系电话" width="120" show-overflow-tooltip align="center" label-class-name="zchiOperatorTel" />
        <el-table-column prop="zchiProvince" label="省份" width="100" show-overflow-tooltip align="center" label-class-name="zchiProvince" />
        <el-table-column prop="zchiCity" label="城市" width="100" show-overflow-tooltip align="center" label-class-name="zchiCity" />
        <el-table-column prop="zchiDeptName" label="业务管理部门" width="140" show-overflow-tooltip align="center" label-class-name="zchiDeptName" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" label-class-name="zchiDepartmentLeader" />
        <el-table-column prop="zchiDepartmentTel" label="部门电话" width="120" show-overflow-tooltip align="center" label-class-name="zchiDepartmentTel" />
        <el-table-column prop="zchiCompanyLeader" label="分公司负责人" width="120" show-overflow-tooltip align="center" label-class-name="zchiCompanyLeader" />
        <el-table-column prop="zchiCompanyTel" label="分公司电话" width="120" show-overflow-tooltip align="center" label-class-name="zchiCompanyTel" />
        <el-table-column prop="zchiEvaluateValue" label="评估价值(万元)" width="140" show-overflow-tooltip align="center" label-class-name="zchiEvaluateValue" />
        <el-table-column prop="zchiEvaluateDate" label="评估日期" width="120" show-overflow-tooltip align="center" label-class-name="zchiEvaluateDate" />
        <el-table-column prop="zchiServiceLife" label="使用年限(年)" width="120" show-overflow-tooltip align="center" label-class-name="zchiServiceLife" />
        <el-table-column prop="zchiDepreciableYear" label="计提年限" width="100" show-overflow-tooltip align="center" label-class-name="zchiDepreciableYear" />
        <el-table-column prop="zchiOfficeArea" label="科研办公面积" width="120" show-overflow-tooltip align="center" label-class-name="zchiOfficeArea" />
        <el-table-column prop="zchiCommercialArea" label="商业面积" width="100" show-overflow-tooltip align="center" label-class-name="zchiCommercialArea" />
        <el-table-column prop="zchiResidentialArea" label="住宅面积" width="100" show-overflow-tooltip align="center" label-class-name="zchiResidentialArea" />
        <el-table-column prop="zchiIndustrialArea" label="工业面积" width="100" show-overflow-tooltip align="center" label-class-name="zchiIndustrialArea" />
        <el-table-column prop="zchiUndergroundArea" label="地下建筑面积" width="120" show-overflow-tooltip align="center" label-class-name="zchiUndergroundArea" />
        <el-table-column prop="zcliCertificateCode" label="对应土地权属证明编号" width="120" show-overflow-tooltip align="center" label-class-name="zcliCertificateCode" />
        <el-table-column prop="zcfaAbility" label="资产能力" width="120" show-overflow-tooltip align="center" label-class-name="zcfaAbility" />
        <el-table-column prop="zchiOtherArea" label="其他面积" width="100" show-overflow-tooltip align="center" label-class-name="zchiOtherArea" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zchiRemark" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" label-class-name="createdTime" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" label-class-name="createdBy" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" label-class-name="updatedTime" />
        <el-table-column label="操作" width="120" fixed="right" align="center" label-class-name="_lesoper">
          <template slot-scope="scope">
<!--            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>-->
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
<!--            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 添加/编辑弹窗组件 -->
    <building-add-dialog ref="buildingAddDialog" @refresh="fetchData" />

    <!-- 详情弹窗组件 -->
    <building-detail-dialog ref="buildingDetail" />

    <!-- 高级查询弹窗组件 -->
    <building-advanced-search-dialog ref="buildingAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getBuildingsList, deleteBuilding } from '@/api/digitalAssetSystem/buildings'
import BuildingAddDialog from './BuildingAddDialog.vue'
import BuildingDetailDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/buildings/components/BuildingDetailDialog.vue'
import BuildingAdvancedSearchDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/buildings/components/BuildingAdvancedSearchDialog.vue'
import { exportRearEnds } from '@/api/exportExcel'
import { exportTmpl } from '@/api/excel'
import { baseURL } from '@/config'
import axios from 'axios'
import store from '@/store'

export default {
  name: "index",
  components: {
    BuildingAddDialog,
    BuildingDetailDialog,
    BuildingAdvancedSearchDialog
  },
  data () {
    return {
      height: this.$baseTableHeight(1, 2.7),
      searchForm: {
        zchiAssetsNo: '',
        zchiCertificateCode: '',
        companyName: '',
        zchiOperator: '',
        zchiCity: '',
        zchiBusinessDirection: '',
        zchiUseDescribe: '',
        zchiIfDispose: '',
        // 高级查询字段
        zchiAssetsName: '',
        zchiAddress: '',
        zchiHouseSource: '',
        zchiDate: '',
        zchiArea: '',
        zchiOriginalValue: '',
        zchiNetValue: '',
        zchiTotalDepreciation: '',
        zchiCountry: '',
        zchiIfAssets: '',
        zchiIfExist: '',
        zchiIfDispute: '',
        zchiIfMortgage: '',
        zchiOperatorTel: '',
        zchiProvince: '',
        zchiDeptName: '',
        zchiDepartmentLeader: '',
        zchiDepartmentTel: '',
        zchiCompanyLeader: '',
        zchiCompanyTel: '',
        zchiEvaluateValue: '',
        zchiEvaluateDate: '',
        zchiServiceLife: '',
        zchiDepreciableYear: '',
        zchiOfficeArea: '',
        zchiCommercialArea: '',
        zchiResidentialArea: '',
        zchiIndustrialArea: '',
        zchiUndergroundArea: '',
        zcliCertificateCode: '',
        zcfaAbility: '',
        zchiOtherArea: '',
        zchiRemark: '',
        createdBy: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      fileUploadBtnText: "导入",
      uploadBtnIcon: "el-icon-upload2",
      fileAccept: ".xls,.xlsx"
    }
  },
  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm
      }

      getBuildingsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },



    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.fetchData()
    },



    handleAdd () {
      this.$refs.buildingAddDialog.showDialog()
    },

    handleEdit (row) {
      this.$refs.buildingAddDialog.showDialog(row)
    },

    handleDelete (row) {
      this.$confirm(`确定要删除资产编号为"${row.zchiAssetsNo}"的房屋信息吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const loading = this.$loading({
          lock: true,
          text: '删除中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })

        try {
          const response = await deleteBuilding(row.zchiId)
          if (response && response.code == 200) {
            this.$message.success('删除成功')
            this.fetchData() // 刷新列表
          } else {
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        } finally {
          loading.close()
        }
      }).catch(() => {
        // 用户取消删除，不做任何操作
      })
    },

    handleDetail (row) {
      this.$refs.buildingDetail.showDialog(row)
    },

    handleAdvancedSearch () {
      this.$refs.buildingAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    // 导出excel模板
    async handleExportTmpl () {
      //导出模板
      let params = { fileName: "房屋建筑物资产信息模板.xls", excelIstmpl: true, excelTmplListIds: "INVESTSORTS", excelTmplListCcs: "tptSort", uncols: "createdTime,createdBy,updatedTime," }
      let qf = exportRearEnds("#buildingsTable", params)

      // 直接从excelExps中移除uncols指定的列
      if (params.uncols && qf.excelExps && qf.excelExps.length > 0) {
        let uncolsArray = params.uncols.split(',').filter(col => col.trim() !== '')

        // 遍历每个表头行
        qf.excelExps.forEach(headerRow => {
          // 过滤掉uncols中指定的列
          for (let i = headerRow.length - 1; i >= 0; i--) {
            if (uncolsArray.includes(headerRow[i].field)) {
              headerRow.splice(i, 1)
            }
          }
        })
      }

      const data = await exportTmpl(qf)
      if (data.code == 200) {
        window.open(baseURL + "/" + data.msg)
      } else {
        this.$message({ message: '导出模板操作失败!', type: 'warning' })
      }
    },

    // 导入文件
    async uploadFile (param) {
      let file = param.file
      let fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
      let acceptArr = this.fileAccept + ","
      if (acceptArr.indexOf(fileType + ",") == -1) {
        this.$message({
          message: `${file.name}文件类型不符，请重新选择${this.fileAccept}格式文件`, type: "warning"
        })
      } else {
        this.uploadBtnIcon = "el-icon-loading"
        this.fileUploadBtnText = "正在导入"

        // 创建FormData对象
        let formdata = new FormData()
        formdata.append('file', file)
        formdata.append('fileparam', '对应excel列的实体类名称,逗号分隔，具体此参数什么格式内容请与后端商量')
        let url = baseURL + "/zcgl-house-info/upload"
        try {

          axios({
            url,
            method: 'post',
            data: formdata,
            headers: {
              'Authorization': store.getters['user/token'],
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.data.code == 200) {
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$message.success('导入成功！')
              this.fetchData()
            } else {
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$message.error(res.data.msg)
              if (res.data.data) {
                window.open(baseURL + "/" + res.data.data)
              }
            }
          }).catch(err => {
            this.uploadBtnIcon = "el-icon-upload2"
            this.fileUploadBtnText = "导入"
            this.$message.error('导入失败！')
          })

        } catch (error) {
          this.uploadBtnIcon = "el-icon-upload2"
          this.fileUploadBtnText = "导入"
          this.$message.error('导入失败！')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  // justify-content: space-between;
}
.inputW {
  width: 250px;
}

.custom-table-container {
  padding: 0px;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
