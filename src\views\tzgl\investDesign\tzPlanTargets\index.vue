<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <tzPlanTargetsSearch
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      :currentOdId="currentOdId"
      ref="tzPlanTargetsTs"
      :selectOption="optionsData.selectOption"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"/>

    <el-table
      ref="tzPlanTargetsTable"
      v-loading="listLoading"
      border
      :data="tableDataWithSubtotal"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzPlanTargets"
      row-key="tptId"
      highlight-current-row 
      :span-method="objectSpanMethod"
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="55"
        :index="count"
        type=index
        label-class-name="number"
      />

      <template v-for="(item, index) in finallyColumns">
        <el-table-column
          align="center" 
          v-if="!item.children"
          :label="item.label"
          :prop="item.prop"
          :sortable="item.sortable"
          :width="item.width"
          :label-class-name="item.prop"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <LesColumn v-else :col="item" :sortable="item.sortable"/>
      </template>
      <!-- 左侧列表切换时只有查看权限 -->
      <el-table-column
        v-if="!this.currentOdId || type != 'detail'"
        align="center"
        label="操作"
        fixed="right"
        show-overflow-tooltip
        width="100"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">{{ row.tptId ? '编辑' : '' }}</el-button>
          <el-button type="text" @click="handleDelete(row)">{{ row.tptId ? '删除' : '' }}</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="50%"
      height="530px"
    >
      <tzPlanTargetsForm
        ref="tzPlanTargetsForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"/>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard>
  </div>
</template>

<script>
  import LesColumn from 'components/VabTable/LesColumn'
  import { 
    tzPlanTargetsDoDeleteELog,
    tzPlanTargetsGetList,
    tzPlanTargetsDoSaveOrUpdLog,
  } from '@/api/tzgl/investDesign/tzPlanTargets'
  import { getSysValRedisList } from '@/api/lesysparamvals'
  import DialogCard from 'common/DialogCard'
  import tzPlanTargetsSearch from './components/Search.vue'
  import tzPlanTargetsForm from './components/Form.vue'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzPlanTargets',
    props: {
      currentOdId: {
        type: String,
        default: ''
      },
      tptType: {
        type: String
      },
      type: {
        type: String
      }
    },
    components: {
      LesColumn,
      DialogCard,
      tzPlanTargetsSearch,
      tzPlanTargetsForm,
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tptAmount: [
            { required: true, message: '请输入规划投资总金额', trigger: 'blur' }
          ],
          tptSort: [
            { required: true, message: '请选择投资方向一级', trigger: 'blur' }
          ],
          tptDirectionSubfield: [
            { required: true, message: '请选择投资方向二级', trigger: 'blur' }
          ],
          tptDirection: [
            { required: true, message: '请选择投资方向三级', trigger: 'blur' }
          ],
          // tptFixedAmount: [
          //   { required: true, message: '请输入固投规划立项总投资', trigger: 'blur' }
          // ],
          // tptExecAmount: [
          //   { required: true, message: '请输入固投规划执行总投资', trigger: 'blur' }
          // ],
          // tptShareAmount: [
          //   { required: true, message: '请输入股权规划执行总投资', trigger: 'blur' }
          // ],
          tptAmount0: [
            { required: true, message: '请输入当年规划投资金额', trigger: 'blur' }
          ],
          tptAmount1: [
            { required: true, message: '请输入第一年规划投资金额', trigger: 'blur' }
          ],
          tptAmount2: [
            { required: true, message: '请输入第二年规划投资金额', trigger: 'blur' }
          ],
          tptAmount3: [
            { required: true, message: '请输入第三年规划投资金额', trigger: 'blur' }
          ],
          tptAmount4: [
            { required: true, message: '请输入第四年规划投资金额', trigger: 'blur' }
          ],
          tptAmount5: [
            { required: true, message: '请输入第五年规划投资金额', trigger: 'blur' }
          ],
          tptOdId: [
            { required: false, message: '请输入单位ID', trigger: 'blur' }
          ],
          tptOeId: [
            { required: false, message: '请输入操作人员ID', trigger: 'blur' }
          ],
          tptOeName: [
            { required: false, message: '请输入操作人员', trigger: 'blur' }
          ],
          tptStatus: [
            { required: false, message: '请输入投资指标状态', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        isFullscreen: false,
        dialogVisible: false,
        height: this.$baseTableHeight(1, 1) - 15,
        checkList: ['投资方向','分类','项目规划来源','重点投资方向', '规划投资总金额', '当年规划投资金额' ,'第一年规划投资金额','第二年规划投资金额','第三年规划投资金额','第四年规划投资金额','第五年规划投资金额'],
        columns: [
          { label: '投资方向', align: 'center',children:[
            { showOverflowTooltip: true, prop: 'tptSort', label: '一级', align: 'center', width: '130' },
            { showOverflowTooltip: true, prop: 'tptDirectionSubfield', label: '二级', align: 'center', width: '130' },
            { showOverflowTooltip: true, prop: 'tptDirection', label: '三级', align: 'center', width: '150' },
          ] },
          // { prop:'tptDirection', label:'重点投资方向', width:'200'},
          // { prop:'tptFixedAmount', label:'固投规划立项总投资', width:'200'},
          // { prop:'tptExecAmount', label:'固投规划执行总投资', width:'200'},
          // { prop:'tptShareAmount', label:'股权规划执行总投资', width:'200'},
          { prop:'tptAmount', label:'规划投资总金额', width:'200'},
          { prop:'tptAmount0', label:'当年规划投资金额', width:'200'},
          { prop:'tptAmount1', label:'第一年规划投资金额', width:'200'},
          { prop:'tptAmount2', label:'第二年规划投资金额', width:'200'},
          { prop:'tptAmount3', label:'第三年规划投资金额', width:'200'},
          { prop:'tptAmount4', label:'第四年规划投资金额', width:'200'},
          { prop:'tptAmount5', label:'第五年规划投资金额', width:'200'}
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          tptType:'投资',
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:'',
        },
        optionsData: {
          selectOption: []
        },
        // 分组字段
        groupByField: 'tptSort',
        // 带小计行的表格数据
        tableDataWithSubtotal: [],
        // 用于记录合并单元格信息
        spanArr: [],
        // 用于记录当前分组索引
        position: 0
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.searchForm.tptType = this.tptType
      this.getSelectOptions()
      this.fetchData()
    },
    methods: {
      /**
       * 处理表格数据，添加小计行并准备合并单元格信息
       */
      processTableData() {
        // 1. 按指定字段分组
        const groups = this.groupData(this.list, this.groupByField);
        
        // 2. 为每个组添加小计行
        const result = [];
        Object.keys(groups).forEach(groupKey => {
          const groupItems = groups[groupKey];
          
          // 添加组内数据行
          groupItems.forEach(item => {
            result.push(item);
          });
          
          // 计算小计并添加小计行
          const subtotal = this.calculateSubtotal(groupItems, groupKey);
          result.push(subtotal);
        });
        
        this.tableDataWithSubtotal = result;
        
        // 3. 计算合并单元格信息
        this.calculateSpanArr();
      },
      /**
       * 将数据按指定字段分组
       * @param {Array} data - 原始数据
       * @param {String} field - 分组字段
       * @returns {Object} 分组后的对象
       */
      groupData(data, field) {
        return data.reduce((groups, item) => {
          const key = item[field];
          if (!groups[key]) {
            groups[key] = [];
          }
          groups[key].push(item);
          return groups;
        }, {});
      },
    
      /**
       * 计算分组小计
       * @param {Array} groupItems - 组内数据
       * @param {String} groupKey - 组键值
       * @returns {Object} 小计行数据
       */
      calculateSubtotal(groupItems, groupKey) {
        const tptFixedAmountTotal = groupItems.reduce((sum, item) => sum + item.tptFixedAmount, 0);
        const tptExecAmountTotal = groupItems.reduce((sum, item) => sum + item.tptExecAmount, 0);
        const tptshareAmountTotal = groupItems.reduce((sum, item) => sum + item.tptshareAmount, 0);
        const tptAmountTotal = groupItems.reduce((sum, item) => sum + item.tptAmount, 0);
        const tptAmount0Total = groupItems.reduce((sum, item) => sum + item.tptAmount0, 0);
        const tptAmount1Total = groupItems.reduce((sum, item) => sum + item.tptAmount1, 0);
        const tptAmount2Total = groupItems.reduce((sum, item) => sum + item.tptAmount2, 0);
        const tptAmount3Total = groupItems.reduce((sum, item) => sum + item.tptAmount3, 0);
        const tptAmount4Total = groupItems.reduce((sum, item) => sum + item.tptAmount4, 0);
        const tptAmount5Total = groupItems.reduce((sum, item) => sum + item.tptAmount5, 0);
        // 创建小计行，标记为小计行
        return {
          [this.groupByField]: "",
          tptDirection: '小计',
          tptFixedAmount: tptFixedAmountTotal,
          tptExecAmount: tptExecAmountTotal, 
          tptshareAmount: tptshareAmountTotal,
          tptAmount: tptAmountTotal,
          tptAmount0: tptAmount0Total,
          tptAmount1: tptAmount1Total,
          tptAmount2: tptAmount2Total,
          tptAmount3: tptAmount3Total,
          tptAmount4: tptAmount4Total,
          tptAmount5: tptAmount5Total,
          isSubtotal: true, // 标记为小计行，用于样式处理
          groupKey: groupKey, // 记录所属分组，用于合并计算
        };
      },
      
      /**
       * 计算需要合并的单元格信息
       */
      calculateSpanArr() {
        this.spanArr = [];
        this.position = 0;
        if (this.tableDataWithSubtotal.length === 0) return;
        let currentGroup = this.tableDataWithSubtotal[0][this.groupByField];
        let count = 1;
        this.spanArr.push(1);
        for (let i = 1; i < this.tableDataWithSubtotal.length; i++) {
          const row = this.tableDataWithSubtotal[i];
          const groupValue = row[this.groupByField];
          
          // 如果是小计行，单独处理
          if (row.isSubtotal) {
            this.spanArr.push(1);
            currentGroup = null;
            count = 1;
            continue;
          }
          
          // 如果和上一行属于同一分组，则计数+1
          if (groupValue === currentGroup) {
            count++;
            this.spanArr[this.position] = count;
            this.spanArr.push(0);
          } else {
            // 不同分组，重新计数
            currentGroup = groupValue;
            this.position = i;
            count = 1;
            this.spanArr.push(count);
          }
        }
      },
      /**
       * 合并单元格的方法
       */
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        // 只对分组字段列进行合并
        if (column.property === "tptSort") {
          const _row = this.spanArr[rowIndex];
          const _col = _row > 0 ? 1 : 0;
          return {
            rowspan: _row,
            colspan: _col
          };
        }
      },
      // 计算表格索引
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 查询字典项
      async getSelectOptions(){
        const tptSort = await getSysValRedisList({ "lpvLpdId": "WNGH" });
        if(tptSort.code==200){
          this.getOptionsData(tptSort.data, "selectOption");
        }
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvName,label:data[d].lpvName})
          }
          data.forEach(item => {
            let yearRange = item.lpvVal.split('-')
            if(this.loginUser.year >= yearRange[0] &&  this.loginUser.year <= yearRange[1]) {
              this.$set(this.searchForm, 'tptFyplan', item.lpvName)
            }
          })
        }
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzPlanTargetsForm.$refs.form.validate(async (valid) => {
          if (valid) {
            // this.$set(this.form, 'tptSort', this.$refs.tzPlanTargetsForm.$refs.tptSort.selectedLabel)
            // this.$set(this.form, 'tptDirection', this.$refs.tzPlanTargetsForm.$refs.tptDirection.selectedLabel)
            const msg = await tzPlanTargetsDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form = {
          tptType: this.tptType,
          tptStatus: '',
          tptOeName: this.loginUser.oeName,
          tptOeId: this.loginUser.oeId,
          tptOdId: this.loginUser.zcbiId,
          tptFyplan: this.searchForm.tptFyplan
        }
        this.editType = 'add'
        this.title = '添加'
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({
          type: 'update'
        },row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tptId) {
          this.$baseConfirm('确定删除投资规划吗', null, async () => {
            const msg = await tzPlanTargetsDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        this.searchForm.tptOdId = this.currentOdId || this.loginUser.zcbiId
        const {
          data: { list, total },
        } = await tzPlanTargetsGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
        this.processTableData()
      },
      // 关闭弹窗
      closeDialog() {
        this.dialogVisible = false
      },
    },
    watch: {
      currentOdId() {
        this.fetchData()
      }
    }
  }
</script>
