<template>
  <div class="stockCompanyType-container">
    <VabChartPie :option="option" title='ygcgqk'/>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { NatureShareholdingEnterprise } from '@/api/sam/assetFx'
  export default {
    name: "StockCompanyType",
    components: {
      VabChartPie,
    },
    data() {
      return {
        option: {}
      }
    },
    mounted() {
      this.getEchartData()
    },
    methods: {
      async getEchartData() {
        // let { data } = await NatureShareholdingEnterprise()
        // let echartData = []
        // data.forEach(item => {
        //   echartData.push({
        //     name: item.esopNature,
        //     value: item.total
        //   })
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'item'
        //   },
        //   grid: {
        //     top:'1%',
        //     containLabel: true
        //   },
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)','rgba(93,112,146,0.85)'],
        //   series: [
        //     {
        //       name: '持股企业性质',
        //       type: 'pie',
        //       radius: ['50%', '70%'],
        //       center: ['50%', '50%'],
        //       avoidLabelOverlap: false,
        //       label: {
        //         show: false,
        //         position: 'center'
        //       },
        //       emphasis: {
        //         label: {
        //           show: true,
        //           fontSize: 20,
        //           fontWeight: 'bold'
        //         }
        //       },
        //       labelLine: {
        //         show: false
        //       },
        //       data: echartData
        //     }
        //   ]
        // }
      }
    },
  }
</script>

<style lang="scss" scoped>
  .stockCompanyType-container {
    width: 100%;
    height: 100%;
  }

  :deep() {
    .echarts {
      width: 100%;
      height: 204px;
    }
  }
</style>
