<template>
  <DialogCard
    :dialogTableVisible="visible"
    :close="handleClose"
    title="关联固定资产信息"
    width="90%"
    top="10vh"
    height="68vh"
    :flag="true"
  >
    <template #content>
      <div class="asset-detail-container">
        <!-- 表格容器 -->
        <div class="table-container" v-loading="loading" element-loading-text="加载资产数据...">
          <!-- 错误状态显示 -->
          <div v-if="error && !loading" class="error-container">
            <div class="error-content">
              <i class="el-icon-warning-outline error-icon"></i>
              <p class="error-message">{{ errorMessage }}</p>
              <el-button type="primary" size="small" @click="retryLoad">重新加载</el-button>
            </div>
          </div>

          <!-- 空数据状态显示 -->
          <div v-else-if="!loading && !error && fullData.length === 0 && tableData.length === 0" class="empty-container">
            <div class="empty-content">
              <i class="el-icon-document empty-icon"></i>
              <p class="empty-message">暂无关联的固定资产信息</p>
            </div>
          </div>

          <!-- 有数据时的展示：统计信息 + 表格 + 分页 -->
          <div v-else>
            <!-- 统计信息（整体汇总） -->
            <div class="summary-container" v-if="!loading && !error && fullData.length > 0">
              <div class="summary-item">
                <span class="summary-label">总数量：</span>
                <span class="summary-value">{{ totalCount }} 台/套</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">总投资：</span>
                <span class="summary-value">{{ totalInvestment }} 万元</span>
              </div>
            </div>

            <!-- 数据表格（仅当前页） -->
            <el-table
              :data="tableData"
              :height="500"
              border
              stripe
              highlight-current-row
              class="asset-table"
            >
              <el-table-column type="index" label="序号" width="70" align="center" />
              <el-table-column prop="zcasAssetsName" label="设备/仪器名称" min-width="150" show-overflow-tooltip />
              <el-table-column prop="zcasProjectName" label="所属项目名称" min-width="150" show-overflow-tooltip />
              <el-table-column prop="zcasKeyFigures" label="主要性能指标" min-width="180" show-overflow-tooltip />
              <el-table-column prop="zcasCountry" label="国别" width="100" align="center" />
              <el-table-column label="数量" width="80" align="center">
                <template #default="{ row }">
                  <span>{{ formatNumber(row.zcasCount) }} {{ row.zcasUnit || '' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价(万元)" width="120" align="center">
                <template #default="{ row }">
                  <span>{{ formatPrice(row.zcasPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="总价(万元)" width="120" align="center">
                <template #default="{ row }">
                  <span>{{ formatPrice(row.zcasSumPrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="zcasManufacturer" label="生产厂家" min-width="150" show-overflow-tooltip />
              <el-table-column prop="zcasType" label="资产类别" width="120" align="center" />
              <el-table-column prop="zcasAssetsState" label="资产状态" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="getAssetStateType(row.zcasAssetsState)" size="small">
                    {{ row.zcasAssetsState || '未知' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="zcasLocation" label="放置位置" min-width="120" show-overflow-tooltip />
              <el-table-column prop="zcasFundsSource" label="资金来源" min-width="150" show-overflow-tooltip />
              <el-table-column prop="zcasSpecialCategory" label="专用类别" width="120" align="center" />
              <el-table-column prop="zcasSerialNum" label="资产编号" min-width="150" show-overflow-tooltip />
            </el-table>

            <!-- 分页组件 -->
            <div class="pagination-container">
              <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pagination.currentPage"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pagination.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
              />
            </div>
          </div>
        </div>


      </div>
    </template>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard'
import { getAbilitySystemByCapabilityId } from '@/api/digitalAssetSystem/assetCapability'

export default {
  name: 'AssetDetailDialog',
  components: {
    DialogCard
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    capabilityData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      error: false,
      errorMessage: '',
      // 当前页展示数据
      tableData: [],
      // 全量数据（用于统计与前端分页）
      fullData: [],
      // 分页信息
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      }
    }
  },

  computed: {
    // 计算总数量（基于全部数据）
    totalCount() {
      return this.fullData.reduce((sum, item) => {
        const count = parseFloat(item.zcasCount) || 0
        return sum + count
      }, 0)
    },

    // 计算总投资（基于全部数据）
    totalInvestment() {
      const total = this.fullData.reduce((sum, item) => {
        const price = parseFloat(item.zcasSumPrice) || 0
        return sum + price
      }, 0)
      return total.toFixed(2)
    }
  },

  watch: {
    visible: {
      handler(newVal) {
        if (newVal && this.capabilityData && this.capabilityData.tciId) {
          this.loadAbilitySystemSummary()
          this.loadAbilitySystemData()
        }
      }
    },
    capabilityData: {
      handler(newData) {
        if (this.visible && newData && newData.tciId) {
          this.loadAbilitySystemSummary()
          this.loadAbilitySystemData()
        }
      },
      deep: true
    }
  },

  methods: {
    handleClose() {
      this.$emit('close')
      this.resetData()
    },

    resetData() {
      this.tableData = []
      this.fullData = []
      this.pagination = {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      }
      this.error = false
      this.errorMessage = ''
    },

    async loadAbilitySystemData() {
      if (!this.capabilityData || !this.capabilityData.tciId) {
        console.warn('缺少能力指标ID，无法加载能力体系数据')
        return
      }

      try {
        this.loading = true
        this.error = false
        this.errorMessage = ''

        const tciId = this.capabilityData.tciId
        const { currentPage, pageSize } = this.pagination
        console.log('加载能力体系数据（当前页）:', { tciId, currentPage, pageSize })

        const response = await getAbilitySystemByCapabilityId(tciId, {
          pageNo: currentPage,
          pageSize,
        })
        console.log('API响应:', response)

        // 根据API文档处理响应数据
        if (response && response.code == 200) {
          if (response.data && Array.isArray(response.data.list)) {
            this.tableData = response.data.list
            this.pagination.total = response.data.total || 0
          } else {
            this.fullData = []
            this.tableData = []
            this.pagination.total = 0
            console.warn('API返回数据格式异常:', response)
          }
        } else {
          // 业务错误
          this.handleLoadError(new Error(response?.message || '查询失败'))
        }
      } catch (error) {
        console.error('加载能力体系数据失败:', error)
        this.handleLoadError(error)
      } finally {
        this.loading = false
      }
    },

    // 加载汇总数据（用于统计信息）
    async loadAbilitySystemSummary() {
      if (!this.capabilityData || !this.capabilityData.tciId) return
      try {
        const tciId = this.capabilityData.tciId
        const response = await getAbilitySystemByCapabilityId(tciId, {
          pageNo: 1,
          pageSize: 100000,
        })
        if (response && response.code == 200) {
          if (Array.isArray(response.data)) {
            this.fullData = response.data
          } else if (response.data && Array.isArray(response.data.list)) {
            this.fullData = response.data.list
          } else {
            this.fullData = []
          }
        }
      } catch (e) {
        console.warn('loadAbilitySystemSummary error:', e)
        this.fullData = []
      }
    },

    handleLoadError(error) {
      this.error = true
      this.tableData = []

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
        this.errorMessage = '网络连接失败，请检查网络连接后重试'
      } else if (error.response?.status === 404) {
        this.errorMessage = '接口不存在，请联系系统管理员'
      } else if (error.response?.status === 500) {
        this.errorMessage = '服务器内部错误，请稍后重试'
      } else if (error.response?.status === 403) {
        this.errorMessage = '没有权限访问该数据'
      } else if (error.message?.includes('能力指标ID不能为空')) {
        this.errorMessage = '能力指标ID参数错误'
      } else {
        this.errorMessage = error.message || '获取能力体系数据失败，请稍后重试'
      }
    },

    retryLoad() {
      this.loadAbilitySystemData()
    },

    // 分页：页面大小变化
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.currentPage = 1
      this.loadAbilitySystemData()
    },

    // 分页：页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page
      this.loadAbilitySystemData()
    },

    // 格式化数字
    formatNumber(value) {
      if (value === null || value === undefined || value === '') return '-'
      return Number(value).toLocaleString()
    },

    // 格式化价格（万元）
    formatPrice(value) {
      if (value === null || value === undefined || value === '') return '-'
      const numValue = parseFloat(value)
      if (isNaN(numValue)) return '-'
      return numValue.toLocaleString()
    },

    // 获取资产状态标签类型
    getAssetStateType(state) {
      const typeMap = {
        '正常使用': 'success',
        '在用': 'success',
        '维修中': 'warning',
        '闲置': 'info',
        '报废': 'danger'
      }
      return typeMap[state] || 'info'
    }
  }

}
</script>

<style lang="scss" scoped>
.asset-detail-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;

  .table-container {
    flex: 1;
    min-height: 0;
    margin-bottom: 20px;

    .asset-table {
      height: 100%;

      :deep(.el-table__header) {
        th {
          background-color: #fafafa;
          color: #262626;
          font-weight: 600;
          border-bottom: 1px solid #e8e8e8;
        }
      }

      :deep(.el-table__body) {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }
        }

        td {
          border-bottom: 1px solid #f0f0f0;
        }
      }
    }
  }

  .summary-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    padding: 16px 0;
    border-top: 1px solid #e8e8e8;
    background-color: #fafafa;

    .summary-item {
      display: flex;
      align-items: center;

      .summary-label {
        font-size: 14px;
        color: #606266;
        margin-right: 8px;
      }

      .summary-value {
        font-size: 16px;
        font-weight: 600;
        color: #CC1214;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    padding: 12px 0;
    background-color: #fff;
  }

}

// 错误和空状态样式
.error-container,
.empty-container {
  display: flex;

  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;

  .error-content,
  .empty-content {
    text-align: center;
    color: #909399;

    .error-icon,
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    .error-icon {
      color: #f56c6c;
    }

    .empty-icon {
      color: #c0c4cc;
    }

    .error-message,
    .empty-message {
      margin: 0 0 16px 0;
      font-size: 14px;
      line-height: 1.5;
    }

    .error-message {
      color: #f56c6c;
    }
  }
}
</style>
