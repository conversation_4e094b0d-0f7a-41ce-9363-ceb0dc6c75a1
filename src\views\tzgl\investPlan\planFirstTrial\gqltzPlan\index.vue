<template>
  <div class="gqltzPlan">

    <VabForm ref="VabForm" class="VabForm" :formModel="searchForm" :formItem="formItem" :formConfig="{ inline: true }" width="100%" />
    <VabTable :tableHeader="tableHeader" :tableData="tableData" :tableHeight="tableHeight" :showPagination="false" />
    <div class="totalBox">
      <span>{{ '合计本年申报投资总额：' + sbtotalNum + '万元' }}</span>
      <span>{{ '合计外部股权融资计划申报总额：' + gqtotalNum + '万元' }}</span>
      <span>{{ '合计预计回收资金总额：' + hstotalNum + '万元' }}</span>
    </div>

    <DialogCard ref="dialogCardRef" :dialogTableVisible="dialogVisible" v-if="dialogVisible" :close="closeDialigCard"
      :title="title" :flag="true" height="92vh" width="99%" top="1vh">
      <GqltrzPlanTabs :type="type" slot="content" :currentRow="currentRow" :parentTitle="title" :parentCloseDialog="() => {
        closeDialigCard();
        getTableData();
      }" />
    </DialogCard>
  </div>
</template>

<script>
import VabForm from 'components/VabForm';
import VabTable from 'components/VabTable';
import DialogCard from 'common/DialogCard';
import GqltrzPlanTabs from './components/GqltrzPlanTabs';
import { getParentList, checkProgressParentReset } from 'api/tzgl/investPlan/planFirstTrial/gqltzPlan';
import { getsecondaryunitsList } from 'api/tzgl/investPlan/searchList'
import { mapGetters } from 'vuex'

export default {
  name: 'gqltzPlan',
  props: {
    type: {
      type: String,
    }
  },
  components: {
    VabForm,
    VabTable,
    DialogCard,
    GqltrzPlanTabs
  },
  data() {
    return {
      searchForm: {},
      formItem: [
        { name: 'dateTime', type: 'year', clearable: false, prop: 'tprYear', format: 'yyyy', valueFormat: 'yyyy', label: '年份', placeholder: '请选择年份' },
        {
          name: 'selectTree', 
          props: { value: 'zcbiId', label: 'zcbiName', children: 'children' }, 
          options: [], 
          label: '单位名称',
          isChangeParent:true,
          getSelectTreeValue:this.getSelectTreeValue
        },
        this.type != 'investDetail' ? {
          name: 'select', options: [
            { label: '未上报', value: '未上报' },
            { label: '已上报，未审核', value: '已上报，未审核' },
            { label: '审核通过', value: '审核通过' },
            { label: '退回调整', value: '退回调整' },
             { label: '批复下达', value: '批复下达' },
          ], prop: 'tprState', label: '评审状态', placeholder: '请选择评审状态'
        } : {},
        { name: 'button', prop: 'button', label: '查询', type: "primary", click: this.getTableData },
        { name: 'button', prop: 'button', label: '重置', click: this.handleResetFun },
      ],
      tableHeaderField: [
        { type: 'index', width: '55', label: "序号", align: 'center', },
        { prop: 'tprYear', label: '年份', align: 'center', width: '100' },
        { prop: 'zcbiName', label: '单位名称', align: 'center', width: '400' },
        { prop: 'ampTotalinvest', label: '本年申报投资总额（万元）', align: 'center' },
        { prop: 'ampTotalinvest2', label: '外部股权融资计划申报金额（万元）', align: 'center' },
        { prop: 'ampTotalinvest3', label: '预计回收资金（万元）', align: 'center' },
        { prop: 'tprState', label: '审核状态', align: 'center', width: '170' },
      ],
      investDetailHeader: [
        { type: 'index', width: '55', label: "序号", align: 'center', },
        { prop: 'tprYear', label: '年份', align: 'center', width: '100' },
        { prop: 'zcbiName', label: '单位名称', align: 'center', width: '400' },
        { prop: 'ampTotalinvest', label: '本年申报投资总额（万元）', align: 'center' },
        { prop: 'ampTotalinvest2', label: '外部股权融资计划申报金额（万元）', align: 'center' },
        { prop: 'ampTotalinvest3', label: '预计回收资金（万元）', align: 'center' },
        { prop: 'count', label: '投资金额项数', align: 'center' },
        { prop: 'count1', label: '回收金额项数', align: 'center' },
      ],
      czBtn: [
        {
          type: 'action', label: '操作', align: 'center', render: (h, scope) => {
            const progressBtnShow = scope.row.tprState === '已上报，未审核';
            const progressResetBtnShow = scope.row.tprState === '审核通过';
            return <div>
              {progressBtnShow && <el-button type="text" size="small" onClick={() => this.handleProgress(scope.row)}>审核</el-button>}
              {progressResetBtnShow && <el-button type="text" size="small" onClick={() => this.handleProgressReset(scope.row)}>审核重置</el-button>}
              <el-button type="text" size="small" onClick={() => this.handleDetail(scope.row)}>详情</el-button>
            </div>
          }
        },
      ],
      tableHeader: [],
      tableData: [],
      tableHeight: this.$baseTableHeight(1) - 58,
      // 弹框数据
      dialogVisible: false,
      title: '',
      sbtotalNum: 0,
      gqtotalNum: 0,
      hstotalNum: 0,
      currentRow: {},
      secondaryunitsList: []
    }
  },
  created() {
    this.getsecondaryunitsList()
    this.$set(this.searchForm, 'tprYear', new Date().getFullYear() + '');
    this.getTableData();
    const header = this.type == 'investDetail' ? [...this.investDetailHeader] :  [...this.tableHeaderField];
    this.tableHeader = [ ...header, ...this.czBtn]
  },
  
  computed: {
    ...mapGetters({
      loginUser: 'user/loginUser'
    }),
  },
  methods: {
    //获取二级单位
    async getsecondaryunitsList() {
      const { data, code, msg } = await getsecondaryunitsList()
      if (code == 200) {
        this.formItem[1].options = [data];
      } else {
        this.$message.error(msg);
      }
    },
    getSelectTreeValue(id, name) {
      this.searchForm.tprCompanyId = id
    },

    //数字累加
    calculateTotal(data) {  
      // 检查 data 是否为数组
      if (!Array.isArray(data)) {
        return 0
      }

      this.sbtotalNum = data.reduce((sum, num) => {
        const amount = parseFloat(num.ampTotalinvest)
        return isNaN(amount) ? sum : sum + amount
      }, 0)
      this.gqtotalNum = data.reduce((sum, num) => {
        const amount2 = parseFloat(num.ampTotalinvest2)
        return isNaN(amount2) ? sum : sum + amount2
      }, 0)
      this.hstotalNum = data.reduce((sum, num) => {
        const amount3 = parseFloat(num.ampTotalinvest3)
        return isNaN(amount3) ? sum : sum + amount3
      }, 0)


    },
    // 获取列表数据
    async getTableData() {
      const params = {
        ...this.searchForm,
        ...this.tablePage,
        tprCompanyId: this.loginUser.zcbiId,
        type: 1
      }
      const { code, msg, data } = await getParentList(params);
      if (code == 200) {
        this.tableData = data;
        this.calculateTotal(data)
      } else {
        this.$message.error(msg)
      }
    },
    // 查询重置
    handleResetFun() {
      this.$refs.VabForm.$refs.SelectTree[0].clearHandle()
      this.searchForm = {};
      this.$set(this.searchForm, 'tprYear', new Date().getFullYear() + '');
      this.getTableData();
    },
    // 审核
    handleProgress(row) {
      this.dialogVisible = true;
      this.currentRow = row;
      this.title = '审核';
    },
    // 审核重置
    async handleProgressReset(row) {
      const params = {
        tprId: row.tprId,
        state: '1'
      }
      const { code, msg } = await checkProgressParentReset(params);
      if (code == 200) {
        this.$message.success('审核重置成功');
        this.getTableData();
      } else {
        this.$message.error(msg);
      }
    },
    // 详情
    handleDetail(row) {
      this.dialogVisible = true;
      this.currentRow = row;
      this.title = '详情';
    },
    // 关闭弹框
    closeDialigCard() {
      this.dialogVisible = false;
    },
  }
}
</script>

<style lang="scss" scoped>
.gqltzPlan {
  padding: 16px;
  background: white;
  border: 1px solid #e4e4e4;
}

.totalBox {
  margin-bottom: 24px;
  width: 100%;
  height: 56px;
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  font-weight: 600;
  font-size: 16px;
  color: #cc1214;
  line-height: 56px;
  text-align: center;
  display: flex;
  justify-content: space-around;
}

.VabForm .el-select ::v-deep .el-input {
  width: 600px !important;
}
</style>