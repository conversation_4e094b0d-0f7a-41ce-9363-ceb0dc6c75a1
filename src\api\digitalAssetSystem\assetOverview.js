import request from '@/utils/request'

// 查询左上角资产概览的数据
export function getLeftTopData(params) {
  return request({
    url: '/overview/assetOverview',
    method: 'get',
    params
  })
}

// 查询左下角企业规模情况的数据
export function getLeftBottomData(params) {
  return request({
    url: '/overview/enterpriseScaleSituation',
    method: 'get',
    params
  })
}

// 查询右上角产权、土地、房产echarts图表的数据
export function getRightTopData(params) {
  return request({
    url: '/overview/getPropertyRight',
    method: 'get',
    params
  })
}

// 企业查询
export function getCompanyPlat(params) {
  return request({
    url: '/overview/getCompanyPlat',
    method: 'get',
    params
  })
}

// 土地
export function getLandPlat(params) {
  return request({
    url: '/overview/getLandPlat',
    method: 'get',
    params
  })
}

// 房屋
export function getHousePlat(params) {
  return request({
    url: '/overview/getHousePlat',
    method: 'get',
    params
  })
}

// 仪器设备
export function getFixedAssetsPlat(params) {
  return request({
    url: '/overview/getFixedAssetsPlat',
    method: 'get',
    params
  })
}

// 查询右上角地图图表的数据
export function getRightTopMapData(params) {
  return request({
    url: '/overview/getPlat',
    method: 'get',
    params
  })
}


// 查询右下角资产能力数量及金额分布柱状图图表的数据
export function getRightBottomData(params) {
  return request({
    url: '/overview/assetCapability',
    method: 'get',
    params
  })
}

export function getIndicatorValue(params) {
  return request({
    url: '/zcgl/data-indicator/getIndicatorValue',
    method: 'get',
    params
  })
}

export function getassetBasicEj(params) {
  return request({
    url: '/tz-zc-company-basic-info/getassetBasicEj',
    method: 'get',
    params
  })
}

// 企业-二级页面
export function getIndicatorValueWithParamsPage (data) {
  return request({
    // url: '/zcgl/data-indicator/getIndicatorValueWithParamsPage',
    url: '/zcgl/data-indicator/getIndicatorValueWithParamConfigPage',
    method: 'post',
    data
  })
}


// 地图指标接口
export function getTaskDataByIndicatorAndCompany(params) {
  return request({
    url: '/zcgl/data-indicator/getTaskDataByIndicatorAndCompany',
    method: 'get',
    params
  })
}
