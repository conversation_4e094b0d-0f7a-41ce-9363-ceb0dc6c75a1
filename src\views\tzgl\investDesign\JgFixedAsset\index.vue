<template>
  <CardBox>
    <TzFixedInvestProjectSearch
      slot="leftCont"
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      :flag="flag"
      :selectOption="optionsData.selectOption"
      ref="tzFixedInvestProjectTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"/>

      <el-table
        ref="tzFixedInvestProjectTable"
        v-loading="listLoading"
        border
        :data="list"
        :height="height"
        stripe
        @cell-dblclick="cellDblClick"
        id="TzFixedInvestProject"
        row-key=""
        highlight-current-row 
        @current-change="currentSelectRow"
      >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />

      <template v-for="(item, index) in finallyColumns">
        <el-table-column
          align="center" 
          v-if="!item.children"
          :label="item.label"
          :prop="item.prop"
          :sortable="item.sortable"
          :width="item.width"
          :label-class-name="item.prop"
          show-overflow-tooltip
        >

            <template slot="header" slot-scope="scope" >
            <template v-if="item.label =='是否纳入储备库' ">
               <el-tooltip content="是否纳入三年滚动计划" placement="top">
              <span>{{item.label}} <span class="isIcon">?</span></span>
            </el-tooltip>
            </template>
            <template  v-else >
            {{ item.label }}
          </template>
         </template>

          <template #default="{ row }">
            <span>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <LesColumn v-else :col="item" :sortable="item.sortable"/>
      </template>

      <el-table-column
        align="center"
        v-if="flag != 'sum'"
        label="操作"
        show-overflow-tooltip
        width="100"
        fixed="right"
        label-class-name="_lesoper"
      > 
        <template #default="{ row }">
          <el-button type="text" v-if="row.tfipIfReserve == '否'" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" v-if="row.tfipIfReserve == '否'" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="70%"
      height="530px"
    >
      <TzFixedInvestProjectForm
        ref="tzFixedInvestProjectForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"
        :tfipCategoryData="tfipCategoryData"
        :tfipCzCompanyNameData="tfipCzCompanyNameData"
        :tifpDirectionData="tifpDirectionData"
      />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard >
  </CardBox>
</template>

<script>
  import LesColumn from 'components/VabTable/LesColumn'
  import CardBox from 'common/CardBox'
  import DialogCard from 'common/DialogCard.vue'
  import TzFixedInvestProjectSearch from './components/Search'
  import TzFixedInvestProjectForm from './components/Form'
  import { 
    tzFixedInvestProjectDoDeleteELog,
    tzFixedInvestProjectGetList,
    tzFixedInvestProjectDoSaveOrUpdLog,
  } from '@/api/tzgl/investDesign/tzFixedInvestProject'
  import { mapGetters } from 'vuex'
  import { listToTree } from '@/utils/tools/tree'
  import { searchSecoundCompanyInfo } from 'api/common'
  import { getSysValRedisList } from '@/api/lesysparamvals'
  export default {
    name: 'tzFixedInvestProject',
    props: {
      tfipSort: {
        type: String,
        default: '军工'
      },
      flag: {
        type: String
      },
      lpvId: {
        type: String,
      }
    },
    components: {
      CardBox,
      DialogCard,
      TzFixedInvestProjectSearch,
      TzFixedInvestProjectForm,
      LesColumn
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tfipName: [
            { required: true, message: '请输入项目名称', trigger: 'blur' }
          ],
          tfipCompanyName: [
            { required: true, message: '请输入二级单位名称', trigger: 'blur' }
          ],
          tfipCzCompanyName: [
            { required: true, message: '请输入投资主体', trigger: 'blur' }
          ],
          tfipTaskName: [
            { required: true, message: '请输入任务名称', trigger: 'blur' }
          ],
          tfipIsplan: [
            { required: true, message: '请选择是否纳入计划', trigger: 'blur' }
          ],
          tfipFwork: [
            { required: true, message: '请选择归属四大能力工程', trigger: 'blur' }
          ],
          tfipDirectionId: [
            { required: true, message: '请输入投资方向', trigger: 'blur' }
          ],
          tfipCategory: [
            { required: true, message: '请输入投资类别', trigger: 'blur' }
          ],
          tfipField: [
            { required: true, message: '请输入投资性质', trigger: 'blur' }
          ],
          tfipType: [
            { required: true, message: '请输入投资类型', trigger: 'blur' }
          ],
          tfipBusiType: [
            { required: true, message: '请输入业务类型', trigger: 'blur' }
          ],
          tfipDeveType: [
            { required: true, message: '请输入发展类别', trigger: 'blur' }
          ],
          tfipDemand: [
            { required: true, message: '请输入建设必要性', trigger: 'blur' }
          ],
          tfipApprovalAmount: [
            { required: true, message: '请输入项目总投资', trigger: 'blur' }
          ],
          tfipTargets: [
            { required: true, message: '请输入建设目标', trigger: 'blur' }
          ],
          tfipConstruction: [
            { required: true, message: '请输入建设内容', trigger: 'blur' }
          ],
          tfipApprovalTime: [
            { required: true, message: '请输入拟启动时间', trigger: 'blur' }
          ],
          tfipCycle: [
            { required: true, message: '请输入建设周期（月）', trigger: 'blur' }
          ],
          tfipAddress: [
            { required: true, message: '请输入建设地址', trigger: 'blur' }
          ],
          tfipContact: [
            { required: true, message: '请输入联系人', trigger: 'blur' }
          ],
          tfipContactWay: [
            { required: true, message: '请输入联系方式', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        dialogVisible: false,
        isFullscreen: false,
        height: this.$baseTableHeight(1, 1),
        checkList: ['二级单位名称','是否纳入储备库', '项目名称', '项目规划来源', '投资主体', '任务名称', '归属四大能力工程', '投资方向', '投资类别', '投资性质', '投资类型', '业务类型', '发展类别', '建设必要性',
          '项目总投资', '建设目标', '建设内容', '拟启动时间', '建设周期（月）', '建设地址', '联系人', '联系方式', '备注'
        ],
        columns: [
          { prop:'tfipCompanyName', label:'二级单位名称', width:'300'},
          { prop:'tfipCzCompanyName', label:'投资主体', width:'300'},
          { prop:'tfipIfReserve', label:'是否纳入储备库', width:'180'},
          { prop:'tfipName', label:'项目名称', width:'300'},
          { prop:'tfipTaskName', label:'任务名称', width:'300'},
          { prop:'tfipFyplan', label:'项目规划来源', width:'200'},
          { prop:'tfipIsplan', label:'是否纳入计划', width:'200'},
          { prop:'tfipFwork', label:'归属四大能力工程', width:'200'},
          { label: '投资方向', align: 'center',children:[
            { showOverflowTooltip: true, prop: 'tfipDirection', label: '一级', align: 'center', width: '130' },
            { showOverflowTooltip: true, prop: 'tfipDirection2', label: '二级', align: 'center', width: '130' },
            { showOverflowTooltip: true, prop: 'tfipDirection3', label: '三级', align: 'center', width: '150' },
          ] },
          { prop:'tfipCategory', label:'投资类别', width:'150'},
          { prop:'tfipField', label:'投资性质', width:'150'},
          { prop:'tfipType', label:'投资类型', width:'150'},
          { prop:'tfipBusiType', label:'业务类型', width:'150'},
          { prop:'tfipDeveType', label:'发展类别', width:'150'},
          { prop:'tfipDemand', label:'建设必要性', width:'150'},
          { prop:'tfipApprovalAmount', label:'项目总投资', width:'150'},
          { prop:'tfipTargets', label:'建设目标', width:'200'},
          { prop:'tfipConstruction', label:'建设内容', width:'200'},
          { prop:'tfipApprovalTime', label:'拟启动时间', width:'200'},
          { prop:'tfipCycle', label:'建设周期（月）', width:'150'},
          { prop:'tfipAddress', label:'建设地址', width:'300'},
          { prop:'tfipContact', label:'联系人', width:'150'},
          { prop:'tfipContactWay', label:'联系方式', width:'150'},
          { prop:'tfipDesc', label:'备注', width:'200'},
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        tfipCategoryData: [], // 被投资企业所属行业
        tfipCzCompanyNameData: [],
        tifpDirectionData: [],
        optionsData: {
          selectOption: [],
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.getSelectOptions()
      this.handleSearchCompany()
      this.fetchData()
    },
    methods: {
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzFixedInvestProjectForm.$refs.form.validate(async (valid) => {
          if (valid) {
             const regex = /^[^-]+-[^-]+(-[^-]+)?$/;
            let isValid= regex.test(this.form.tfipAddress);
            if(!isValid){
              this.$message({
              type: 'warning',
              message: '地址格式不正确，请使用"省-市-区"或"直辖市-区"格式',
            })
            return false
            }
            const msg = await tzFixedInvestProjectDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      closeDialog() {
        this.dialogVisible = false
      },
      async getSelectOptions(){
        // 被投资企业所属行业
        const tfipCategory = await getSysValRedisList({ "lpvLpdId": "TZXMFL" });
        if(tfipCategory.code==200){
          this.tfipCategoryData = listToTree(
            'lpvId',
            'lpvPid',
            tfipCategory.data,
            'lpvName'
          )
        }
        const tifpDirection = await getSysValRedisList({ "lpvLpdId": "INVESTSORTS" })
        if(tifpDirection.code == 200){
          this.tifpDirectionData = listToTree('lpvId', 'lpvPid', tifpDirection.data, 'lpvName')
        }

        const tptSort = await getSysValRedisList({ "lpvLpdId": "WNGH" });
        if(tptSort.code == 200){
          this.getOptionsData(tptSort.data, "selectOption");
        }
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvName,label:data[d].lpvName})
          }
          data.forEach(item => {
            let yearRange = item.lpvVal.split('-')
            if(this.loginUser.year >= yearRange[0] &&  this.loginUser.year <= yearRange[1]) {
              this.$set(this.searchForm, 'tfipFyplan', item.lpvName)
            }
          })
        }
      },
      async handleSearchCompany() {
        const { data: {
          list
        }} = await searchSecoundCompanyInfo({
          odPathid: this.loginUser.zcbiId
        })
        this.tfipCzCompanyNameData = list
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.title = '添加'
        // 像表单里塞入二级单位
        this.$set(this.form, 'tfipCompanyName', this.loginUser.zcbiName)
        this.$set(this.form, 'tfipCompanyId', this.loginUser.zcbiId)
        this.$set(this.form, 'tfipSort', this.tfipSort)
        this.$set(this.form, 'tfipFyplan', this.searchForm.tfipFyplan)
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tfipId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzFixedInvestProjectDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        this.searchForm.tfipSort = this.tfipSort
        this.searchForm.tfipDirectionId = this.lpvId
        this.flag == 'sum' ? this.searchForm.cOdId = this.loginUser.zcbiId  : this.searchForm.tfipCompanyId = this.loginUser.zcbiId
        const {
          data: { list, total },
        } = await tzFixedInvestProjectGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
    watch: {
      lpvId() {
        this.fetchData()
      }
    }
  }
</script>

<style lang="scss" scoped>
.isIcon{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #aeafb0;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  cursor: help;
  margin-left: 4px;
  user-select: none;
}
</style>