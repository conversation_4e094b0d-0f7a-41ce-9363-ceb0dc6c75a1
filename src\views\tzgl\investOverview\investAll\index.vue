<template>
  <div>
    <div class="searchPage">
      <SelectTree
        :props="{
          children: 'children',
          label: 'zcbiName', 
          value: 'zcbiId'
        }"
        style="margin-right: 16px;width: 300px;"
        :options="secondaryunitsList"
        :value="orgValue"
        :clearable="false"
        :isAccordion="true"
        :isChangeParent="true"
        @getSelectTreeValue="getSelectTreeData(arguments,'orgValue','orgName')"/>
      
      <el-date-picker
        v-model="year"
        type="year"
        placeholder="选择年"
        value-format="yyyy"
        format="yyyy"
      />
    </div>
    <div style="display: flex;">
      <div class="leftBox">
        <SmallCard title="年度数据总览" style="height: auto;">
          <div style="display: flex;">
            <InvestAllLeft :parentData="dataAll" style="width: 55%;" :yearAll="yearAll" />
            <SmallCardVerticalLine title="投资后评价" class="dataAllRight" style="width: 45%;position: relative;">
              <!-- <span slot="rightTitle">单位：数量（个）</span> -->
              <span class="detail-btn" slot="rightTitle" @click="handleLookMoreData">
                查看详情
                <!--<img :src="require(`@/assets/tzgl/icon_ckgd.png`)"/>-->
              </span>
              <div style="display: flex;">
                <span style="line-height: 40px;">单位：数量（个）</span>
                <div class="category-tabs">
                  <button 
                    v-for="(item,key) in tzhpjOptions" 
                    :key="key"
                    :class="{ active: currentMainCategory == key }"
                    @click="currentMainCategory = key"
                  >
                    {{ item.item }}
                  </button>
                </div>
              </div>
              <PieEcharts
                height="268px"
                legendTop="60%"
                :data="tzhpjPieData"
                :unit="' '"
                :isHandleNumberData="true"
                :legendOrient="'vertical'" 
                :legendLeft="'center'"
                :center="['50%','30%']" 
              />
            </SmallCardVerticalLine>
          </div>
        </SmallCard>
        <!--年度投资计划分类-->
        <YearType 
          style="margin-top: 17px;position: relative;"
          :params="{year: year,zcbiId: orgValue}"
        />
      </div>
      <SmallCard title="年度投资项目情况" style="margin-left: 16px;flex: 1;">
        <ProjectQk 
          :params="{year: year,zcbiId: orgValue}"
          @handleSecondPage="handleSecondPage"
        />
      </SmallCard>
    </div>  
    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="1vh"
      width="99%"
      height="85vh"
    >
      <SecoundPage
        slot="content"
        :year="year"
        :orgValue="orgValue"
        :title="title"
        :rightClickParams="rightClickParams"
        :xmqkType="xmqkType"
      />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
      </template>
    </DialogCard>
  </div>
</template>

<script>
  import SmallCard from "common/SmallCard";
  import SmallCardVerticalLine from "common/SmallCardVerticalLine";
  import PieEcharts from "common/PieEcharts";
  import InvestAllLeft from "./components/InvestAllLeft";
  import YearType from './components/YearType';
  import ProjectQk from './components/ProjectQk';
  import DialogCard from "common/DialogCard";
  import SecoundPage from "./components/SecoundPage";
  import SelectTree from "components/SelectTree";
  import { mapGetters } from 'vuex';
  import { getsecondaryunitsList } from 'api/tzgl/investPlan/searchList'
  import { getYearAllData, getTzhpjList } from 'api/tzgl/investOverview/investAll';

  export default {
    name: 'InvestAll',
    components: {
      SmallCard,
      SmallCardVerticalLine,
      PieEcharts,
      InvestAllLeft,
      YearType,
      ProjectQk,
      DialogCard,
      SecoundPage,
      SelectTree
    },
    data(){
      return {
        secondaryunitsList:[],
        orgValue:'',
        orgName:'',
        year: new Date().getFullYear()+'',
        dataAll:{},
        tzhpjPieData:[],
        yearAll:{},
        currentMainCategory: '0',
        tzhpjOptions:[],
        tzhpjData:[],
        // 二级页面数据
        dialogVisible:false,
        title:'',
        rightClickParams:[],
        xmqkType:''
      }
    },
    computed: {
      ...mapGetters({
        loginUser: 'user/loginUser',
      }),
    },
    created(){
      this.getsecondaryunitsList().then(() => {
        this.orgValue = this.loginUser.zcbiId;
        this.orgName = this.loginUser.zcbiName;
        this.handleInit();
      });
    },
    watch: {
      currentMainCategory(val) {
        this.getEchartsData(val);
      },
      year(val){
        this.year = val;
        this.handleInit();
      },
      orgValue(val){
        this.orgValue = val;
        this.handleInit();
      }
    },
    methods: {
      // 初始化数据
      handleInit(){
        const params = {year: this.year,zcbiId: this.orgValue}; 
        this.getYearAllData(params);
        this.getTzhpjList(params);
      },
      //获取二级单位
      async getsecondaryunitsList() {
        const { data, code, msg } = await getsecondaryunitsList()
        if (code == 200) {
          this.secondaryunitsList = [data];
        } else {
          this.$message.error(msg);
        }
      },
      getEchartsData(type){
        this.tzhpjPieData = this.tzhpjData[type*1];
      },
      // 获取投资计划
      async getYearAllData(params){
        const {data,code,msg} = await getYearAllData(params);
        if(code == 200){
          this.yearAll = data;
        }else{
          this.$message.error(msg);
        }
      },
      // 获取投资后评价
      async getTzhpjList(params){
        const {data,code,msg} = await getTzhpjList(params);
        if(code == 200){
          this.tzhpjOptions = Object.keys(data).map((item,index)=>{
            return {
              key: index+'',
              item: item,
            }
          });
          this.tzhpjData = Object.values(data);
          this.tzhpjPieData = this.tzhpjData[0];
        }else{
           this.$message.error(msg);
        }
      },
      // 投后评价二级下转页
      handleLookMoreData(){
        this.title = this.tzhpjOptions[this.currentMainCategory*1]['item'] +'投资后评价';
        this.dialogVisible = true;
      },
      closeDialog(){
        this.dialogVisible = false;
      },
      // 切换单位
      getSelectTreeData(arr,id,name) {
        this.orgValue = arr[0];
        this.orgName = arr[1];
      },
      handleSecondPage(arg,title){
        this.xmqkType = title;
        this.rightClickParams = arg;
        if (title == '股权类' || title == '资产处置') {
          this.title = '项目情况详情';
        } else if(title == '自筹类固投' || title == '军工类固投'){
          // 待上报取计划编制中自筹和军工的表头
          if ((arg[4] == '待上报') && (arg[3] == '拟立项' || arg[3] == '建议书')) {
            this.title = title == '自筹类固投' ?  '项目自筹待上报情况' : '项目军工待上报情况'
          }else{
            this.title = '项目情况详情';
          }
        }
        this.dialogVisible = true;
      }
    }
  }       
</script>

<style lang="scss" scoped>
  .searchPage{
    background: white;
    margin-bottom: 8px;
    text-align: right;
    padding: 8px 8px;
  }
  .detail-btn{
    cursor: pointer;
  }
  
  .category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    position: absolute;
    right: 16px;
  }

  .category-tabs button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: ndtzzc 0.2s;
  }

  .category-tabs button.active {
    background: rgba(204,18,20,0.04);
    color: #CC1214;
    border-color: #CC1214;
  }
  .leftBox,.rightBox{
    flex: 1;
    .investAllLeft{
      margin-top: 17px;
    }
    .investAllLeft:first-child{
      margin-top: 0;
    }
  }
  .dataAllRight{
    margin-left: 16px;
  }
</style>