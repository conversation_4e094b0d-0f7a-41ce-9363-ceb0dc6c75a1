<template>
  <div class="outfinancing-container">
    <el-row :gutter="24">
      <el-col :span="8">
        <SmallCard :title="'本年批复项目情况'">
          <ThisYearApprovalPro :year="year"/>
        </SmallCard>
      </el-col>
      <el-col :span="16">
        <SmallCard :title="'本年融资计划情况'">
          <ThisYearStockPlan :year="year"/>
        </SmallCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import SmallCard from '@/views/common/SmallCard'
  import ThisYearApprovalPro from './ThisYearApprovalPro'
  import ThisYearStockPlan from './ThisYearStockPlan'
  export default {
    name: "Outfinancing",
    props:['year'],
    components: {
      SmallCard,
      ThisYearApprovalPro,
      ThisYearStockPlan
    },
    data() {
      return {

      }
    },
    methods: {

    },
  }
</script>

<style lang="scss" scoped>
  .outfinancing-container {
    width: 100%;
    height: 100%;
  }
</style>