<template>
  <div ref="chartRef" class="pie-chart-container" v-loading="mapLoading" element-loading-text="加载中..."></div>
</template>

<script>
import * as echarts from 'echarts'
import { getProvinceT4 } from '@/api/digitalAssetSystem/capabilityIndex'

export default {
  data () {
    return {
      chart: null,
      mapLoading: false,
      mapData: [], // [{ name: '江苏省', value: 12 }]
      maxNum: 10
    }
  },

  mounted () {
    this.initChart()
    // 初始加载：不指定参数则由后续父组件触发；也可在此使用默认参数
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    initChart () {
      this.chart = echarts.init(this.$refs.chartRef)
      this.setChartOption(this.mapData)
    },

    async fetchMapData (params = {}) {
      try {
        this.mapLoading = true
        const res = await getProvinceT4(params)
        let data = []
        if (res && res.code == 200) {
          const list = Array.isArray(res.data) ? res.data : (res.data && res.data.list) || []
          this.$emit('updateNumber',{nldyNumber: res.data[0].sum_tjz,unitNumber: res.data[0].sum_company_count})
          data = list.map(item => {
            const name = this.normalizeProvinceName(item.province)
            const value = item.tjz
            return { name, value }
          })
        }
        this.mapData = data
         if (this.mapData[0]) {
          let maxNumObj = this.mapData.reduce((max, current) => {
            return (current.value > max.value) ? current : max
          }, this.mapData[0] || {})
          // console.log("🚀🚀 ~ initChart ~ maxNumObj ~ 🚀🚀", maxNumObj)
          this.maxNum = maxNumObj.value
        }
        
          console.log("🚀🚀 ~ aaaa ~ 🚀🚀",this.mapData)
        this.setChartOption(this.mapData)
      } catch (e) {
        console.error('加载省份地图数据失败:', e)
        // 失败时也刷新图表，展示空数据
        this.mapData = []
        this.setChartOption(this.mapData)
      } finally {
        this.mapLoading = false
      }
    },

    normalizeProvinceName (name) {
      if (!name) return ''
      const n = String(name).trim()
      const special = {
        '北京': '北京市', '天津': '天津市', '上海': '上海市', '重庆': '重庆市',
        '内蒙古': '内蒙古自治区', '广西': '广西壮族自治区', '西藏': '西藏自治区', '宁夏': '宁夏回族自治区', '新疆': '新疆维吾尔自治区',
        '香港': '香港特别行政区', '澳门': '澳门特别行政区', '台湾': '台湾省'
      }
      if (special[n]) return special[n]
      if (/省$|市$|自治区$|特别行政区$/.test(n)) return n
      // 默认补“省”
      return n + '省'
    },

    setChartOption (data = []) {
      if (!this.chart) return
      const chinaJson = require('@/assets/digitalAssetSystem/china.json')
      echarts.registerMap('china', chinaJson)

      const option = {
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: '#fff',
          borderColor: 'rgba(24,144,255,0.35)',
          borderWidth: 1,
          padding: [8, 12],
          textStyle: { color: '#333' },
          extraCssText: 'border-radius:8px; box-shadow:0 2px 8px rgba(0,0,0,0.1);',
          formatter: function (params) {
            console.log(params);
            const name = params.name || ''
            const val = params.data.value?params.data.value:"0"
            return '<div style="font-size:12px;line-height:1.6;color:#333;">'
              + '<div style="font-weight:600;margin-bottom:6px;">' + name + '：</div>'
              + '<div><span style="color:#8c8c8c;">能力数量：</span>'
              + '<span style="font-weight:700;font-size:16px;">' + val + '</span></div>'
              + '</div>'
          },
        },
         visualMap: {
          show: true,
          type: 'continuous',
          // text: ['占比高', '占比低'],
          showLabel: true,
          calculable: true,
          seriesIndex: [0],
          min: 0,
          max: this.maxNum,
          inRange: {
            color: ['#a3d4ff', '#83b9f1', '#5b99d5', '#2a67a1', '#024484']
          },
          itemWidth: 10,
          textStyle: {
            color: '#000'
          },
          bottom: 10,
          right: '250',
        },
        xAxis: { show: false },
        yAxis: { show: false },
        geo: {
          roam: true,
          map: 'china',
          top: '10', left: '100', right: '300',
          layoutSize: '100%',
          label: { emphasis: { show: false } },
          itemStyle: { emphasis: { areaColor: '#fff464' } },
          regions: [{ name: '南海诸岛', value: 0, itemStyle: { normal: { opacity: 0, label: { show: false } } } }],
        },
        series: [{
          name: 'mapSer', type: 'map', roam: false, geoIndex: 0,
          label: { show: false },
          data: data
        }]
      }
      this.chart.setOption(option, true)
    },

    handleResize () {
      if (this.chart) this.chart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-chart-container {
  width: 873px;
  height: 386px;
  // height: 510px;
}
</style>
