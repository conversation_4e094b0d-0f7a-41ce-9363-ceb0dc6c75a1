<template>
  <CardBox>
    <TzInvestmentFinancingProjectSearch
      slot="leftCont"
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      :selectOption="optionsData.selectOption"
      :flag="flag"
      ref="tzInvestmentFinancingProjectTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"
    />

    <el-table
      ref="tzInvestmentFinancingProjectTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzInvestmentFinancingProject"
      row-key="tifpId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="95"
        :index="count"
        type=index
        label-class-name="number"
      />
      <template v-for="(item, index) in finallyColumns">
        <el-table-column
          align="center" 
          v-if="!item.children"
          :label="item.label"
          :prop="item.prop"
          :sortable="item.sortable"
          :width="item.width"
          :label-class-name="item.prop"
          show-overflow-tooltip>

          <template slot="header" slot-scope="scope" >
            <template v-if="item.label =='是否纳入储备库' ">
               <el-tooltip content="是否纳入三年滚动计划" placement="top">
              <span>{{item.label}} <span class="isIcon">?</span></span>
            </el-tooltip>
            </template>
            <template  v-else >
            {{ item.label }}
          </template>
         </template>
         
          <template #default="{ row }">
            <span>{{ row[item.prop] }}</span>
          </template>
        </el-table-column>
        <LesColumn v-else :col="item" :sortable="item.sortable"/>
      </template>

      <el-table-column
        v-if="flag != 'sum'"
        align="center"
        label="操作"
        show-overflow-tooltip
        width="100"
        fixed="right"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text"  v-if="row.tifpIfReserve == '否'" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text"  v-if="row.tifpIfReserve == '否'" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="70%"
      height="530px"
    >
      <TzInvestmentFinancingProjectForm
        ref="tzInvestmentFinancingProjectForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"
        :tifpCzCompanyNameData="tifpCzCompanyNameData"
        :tifpDirectionData="tifpDirectionData"/>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard >
  </CardBox>
</template>

<script>
  import CardBox from 'common/CardBox'
  import DialogCard from 'common/DialogCard'
  import LesColumn from 'components/VabTable/LesColumn';
  import TzInvestmentFinancingProjectSearch from './components/Search'
  import TzInvestmentFinancingProjectForm from './components/Form'
  
  import { 
    tzInvestmentFinancingProjectDoDeleteELog,
    tzInvestmentFinancingProjectGetList,
    tzInvestmentFinancingProjectDoSaveOrUpdLog,
  } from '@/api/tzgl/investDesign/tzInvestmentFinancingProject'
  import { getSysValRedisList } from '@/api/lesysparamvals'
  import { searchSecoundCompanyInfo } from 'api/common'
  import { mapGetters } from 'vuex'
  import { listToTree } from '@/utils/tools/tree'

  export default {
    name: "ShareholdingInvest",
    components: {
      CardBox,
      DialogCard,
      LesColumn,
      TzInvestmentFinancingProjectSearch,
      TzInvestmentFinancingProjectForm
    },
    props: {
      flag: {
        type: String
      },
      lpvId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tifpAddress: [
            { required: true, message: '请输入项目地点', trigger: 'blur' }
          ],
          tifpAnalysis: [
            { required: true, message: '请输入必要性分析', trigger: 'blur' }
          ],
          tifpCompanyName: [
            { required: true, message: '请输入二级成员单位', trigger: 'blur' }
          ],
          tifpContacts: [
            { required: true, message: '请输入项目联系人', trigger: 'blur' }
          ],
          tifpContactsWay: [
            { required: true, message: '请输入联系方式', trigger: 'blur' }
          ],
          tifpContent: [
            { required: true, message: '请输入项目内容', trigger: 'blur' }
          ],
          tifpCzCompanyName: [
            { required: true, message: '请输入投资主体', trigger: 'blur' }
          ],
          tifpDirectionId: [
            { required: true, message: '请输入投资方向', trigger: 'blur' }
          ],
          tifpExterFinanc: [
            { required: true, message: '请输入外部股权融资总额', trigger: 'blur' }
          ],
          tifpField: [
            { required: true, message: '请输入投资性质', trigger: 'blur' }
          ],
          // tifpIsplan: [
          //   { required: true, message: '请输入是否计划内投资', trigger: 'blur' }
          // ],
          tifpName: [
            { required: true, message: '请输入项目名称', trigger: 'blur' }
          ],
          tifpSort: [
            { required: true, message: '请输入投资类别', trigger: 'blur' }
          ],
          tifpTotalAmount: [
            { required: true, message: '请输入项目总投资', trigger: 'blur' }
          ],
          tifpType: [
            { required: true, message: '请输入经济行为类型', trigger: 'blur' }
          ],
          tifpTargets: [
            { required: true, message: '请输入投资目的', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        dialogVisible: false,
        isFullscreen: false,
        height: this.$baseTableHeight(1,1),
        checkList: ['投资方向','项目地点','必要性分析','是否纳入储备库','二级成员单位', '项目规划来源', '项目联系人','联系方式','项目内容','投资主体','投资目的','备注','投资方向','外部股权融资总额','投资性质','项目名称','投资类别','项目总投资','经济行为类型'],
        columns: [
          { prop:'tifpCompanyName', label:'二级成员单位', width:'300'},
          { prop:'tifpName', label:'项目名称', width:'300'},
          { prop:'tifpIfReserve', label:'是否纳入储备库', width:'180'},
          { prop:'tifpFyplan', label:'项目规划来源', width:'200'},
          { prop:'tifpCzCompanyName', label:'投资主体', width:'300'},
          { prop:'tifpTargets', label:'投资目的', width:'300'},
          { prop:'tifpType', label:'经济行为类型', width:'150'},
          { prop:'tifpTotalAmount', label:'项目总投资', width:'150'},
          { prop:'tifpExterFinanc', label:'外部股权融资总额', width:'150'},
          { label: '投资方向', align: 'center',children:[
            { showOverflowTooltip: true, prop: 'tifpDirection', label: '一级', align: 'center', width: '130' },
            { showOverflowTooltip: true, prop: 'tifpDirection2', label: '二级', align: 'center', width: '130' },
            { showOverflowTooltip: true, prop: 'tifpDirection3', label: '三级', align: 'center', width: '150' },
          ] },
          { prop:'tifpField', label:'投资性质', width:'150'},
          { prop:'tifpSort', label:'投资类别', width:'150'},
          { prop:'tifpContent', label:'项目内容', width:'200'},
          { prop:'tifpAnalysis', label:'必要性分析', width:'150'},
          { prop:'tifpAddress', label:'项目地点', width:'150'},
          { prop:'tifpContacts', label:'项目联系人', width:'150'},
          { prop:'tifpContactsWay', label:'联系方式', width:'150'},
          // { prop:'tifpIsplan', label:'是否计划内投资', width:'200'},
          { prop:'tifpDesc', label:'备注', width:'200'},
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        tifpCzCompanyNameData: [],
        tifpDirectionData: [],
        optionsData: {
          selectOption: []
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.getSelectOptions()
      this.handleSearchCompany()
    },
    methods: {
      
      async getSelectOptions(){
        const tptSort = await getSysValRedisList({ "lpvLpdId": "WNGH" });
        if(tptSort.code == 200){
          this.getOptionsData(tptSort.data, "selectOption");
        }

        const tifpDirection = await getSysValRedisList({ "lpvLpdId": "INVESTSORTS" })
        if(tifpDirection.code == 200){
          this.tifpDirectionData = listToTree('lpvId', 'lpvPid', tifpDirection.data, 'lpvName')
        }
      },
      getOptionsData(data,optionfield){
        if(data.length>0){
          this.optionsData[optionfield]=[]
          for(let d in data){
            this.optionsData[optionfield].push({value:data[d].lpvName,label:data[d].lpvName})
          }
          data.forEach(item => {
            let yearRange = item.lpvVal.split('-')
            if(this.loginUser.year >= yearRange[0] &&  this.loginUser.year <= yearRange[1]) {
              this.$set(this.searchForm, 'tifpFyplan', item.lpvName)
            }
          })
          this.fetchData()
        }
      },
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzInvestmentFinancingProjectForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const regex = /^[^-]+-[^-]+(-[^-]+)?$/;
            let isValid= regex.test(this.form.tifpAddress);
            if(!isValid){
              this.$message({
              type: 'warning',
              message: '地址格式不正确，请使用"省-市-区"或"直辖市-区"格式',
            })
            return false
            }
            const  msg  = await tzInvestmentFinancingProjectDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      closeDialog() {
        this.dialogVisible = false
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.title = '添加'
        this.$set(this.form, 'tifpCompanyName', this.loginUser.zcbiName)
        this.$set(this.form, 'tifpCompanyId', this.loginUser.zcbiId)
        this.$set(this.form, 'tifpFyplan', this.searchForm.tifpFyplan)
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      
      async handleSearchCompany() {
        const { data: {
          list
        }} = await searchSecoundCompanyInfo({
          odPathid: this.loginUser.zcbiId
        })
        this.tifpCzCompanyNameData = list
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tifpId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzInvestmentFinancingProjectDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        this.flag == 'sum' ? this.searchForm.cOdId = this.loginUser.zcbiId  : this.searchForm.tifpCompanyId = this.loginUser.zcbiId
        this.searchForm.tifpDirectionId = this.lpvId
        const {
          data: { list, total },
        } = await tzInvestmentFinancingProjectGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
    watch: {
      lpvId() {
        this.fetchData()
      }
    }
  }
</script>
<style lang="scss" scoped>
.isIcon{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #aeafb0;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  cursor: help;
  margin-left: 4px;
  user-select: none;
}
</style>