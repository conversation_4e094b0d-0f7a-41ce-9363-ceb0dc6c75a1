<template>
  <DialogCard
    :dialogTableVisible="visible"
    :title="dialogTitle"
    :close="handleClose"
    width="90%"
    height="70vh"
  >
    <template #content>
      <div class="dialog-content">
        <div class="table-container">
          <!-- 错误状态 -->
          <div v-if="error" class="table-error-container">
            <i class="el-icon-warning-outline"></i>
            <span class="error-message">{{ errorMessage || '加载数据失败，请稍后重试' }}</span>
            <el-button type="primary" size="mini" @click="retry">重试</el-button>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!loading && tableData.length === 0" class="table-empty-container">
            <i class="el-icon-s-grid placeholder-icon"></i>
            <p class="placeholder-message">暂无数据</p>
          </div>

          <!-- 表格 -->
          <el-table
            v-else
            :data="tableData"
            border
            stripe
            :height="tableHeight"
            v-loading="loading"
          >
            <el-table-column type="index" label="序号" width="70" align="center" show-overflow-tooltip />
            <el-table-column prop="t1Name" label="大类(T1)" width="180" align="center" show-overflow-tooltip />
            <el-table-column prop="t2Name" label="领域(T2)" width="180" align="center" show-overflow-tooltip />
            <el-table-column prop="t3Name" label="子类(T3)" width="180" align="center" show-overflow-tooltip />
            <el-table-column prop="t4Name" label="能力单元(T4)" width="180" align="center" show-overflow-tooltip />
            <el-table-column prop="tciTotalNumberDevices" label="总设备数量（台套）" width="180" align="center" />
            <el-table-column prop="tciTotalMilitaryProject" label="军工技改设备数量（台套）" width="200" align="center" />
            <el-table-column prop="tciTotalMilitaryProportion" label="军工技改设备数量占比（%）" width="220" align="center" />
            <el-table-column prop="tciTotalMilitaryLocalization" label="军工技改设备中国产化数量（台套）" width="250" align="center" />
            <el-table-column prop="tciMilitaryTechRatio" label="军工技改设备中国产化数量占比" width="250" align="center" />
            <el-table-column prop="tciOriginalValue" label="总设备原值（万元）" width="180" align="center" />
            <el-table-column prop="tciOriginalValueJg" label="军工技改设备总原值（万元）" width="220" align="center" />
            <el-table-column prop="tciOriginalValueProportion" label="军工技改设备原值占比" width="180" align="center" />
            <el-table-column prop="tciOriginalValueGc" label="军工技改设备中国产化原值（万元）" width="260" align="center" />
            <el-table-column prop="tciOriginalValueGczb" label="军工技改设备中国产化原值占比" width="260" align="center" />
            <el-table-column prop="tciNetWorth" label="军工技改设备净值（万元）" width="220" align="center" />
            <el-table-column prop="tciBenchmarkingLevel" label="对标的强敌水平" width="120" align="center" />
            <el-table-column prop="tciEvaluateStatus" label="评价自身地位" width="120" align="center" />
            <el-table-column prop="tciShortboardGap" label="短板差距" width="120" align="center" />
            <el-table-column prop="tciCompanyName" label="填报单位" width="160" align="center" />
            <el-table-column prop="tciBusinessFieldLevel1" label="业务领域一级" width="140" align="center" />
            <el-table-column prop="tciBusinessDomain" label="业务领域" width="140" align="center" />
            <el-table-column prop="tciAddress" label="资产所在地点" width="140" align="center" />
            <el-table-column prop="tciProportionTotalGc" label="总设备中国产化数量占比" width="220" align="center" />
            <el-table-column prop="tciRemarks" label="备注" width="200" align="center" />
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="handleDetail(scope.row)" v-show="scope.row.hasAssets">资产详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 资产详情弹窗 -->
        <AssetDetailDialog
          :visible="assetDetailDialogVisible"
          :capabilityData="currentCapabilityData"
          @close="handleCloseAssetDetail"
        />

        <!-- 分页 -->
<!--        <div class="pagination-container">-->
<!--          <el-pagination-->
<!--            background-->
<!--            @size-change="handleSizeChange"-->
<!--            @current-change="handleCurrentPageChange"-->
<!--            :current-page="pagination.currentPage"-->
<!--            :page-sizes="[10, 20, 50, 100]"-->
<!--            :page-size="pagination.pageSize"-->
<!--            layout="total, sizes, prev, pager, next, jumper"-->
<!--            :total="pagination.total"-->
<!--          />-->
<!--        </div>-->
      </div>
    </template>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard.vue'
import { getCapabilityt4 } from '@/api/digitalAssetSystem/capabilityIndex'
import AssetDetailDialog from '@/views/digitalAssetSystem/capabilityMap/assetCapability/coreCompetency/components/AssetDetailDialog.vue'
export default {
  name: 'CapabilityDetailDialog',
  components: { DialogCard,AssetDetailDialog },
  data () {
    return {
      visible: false,
      dialogTitle: '能力详情',
      category: '',
      zcbiId: '', // 存储单位ID
      loading: false,
      error: false,
      errorMessage: '',
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      tableHeight: 'calc(70vh - 100px)',
      // 资产详情弹窗相关
      assetDetailDialogVisible: false,
      currentCapabilityData: {},
    }
  },
  methods: {
    show (title, zcbiId = '') {
      this.dialogTitle = `${title} - 能力详情`
      this.category = title
      this.zcbiId = zcbiId
      this.visible = true
      this.pagination.currentPage = 1
      this.fetchData()
    },
    handleClose () {
      this.visible = false
    },
    async fetchData () {
      try {
        this.loading = true
        this.error = false
        this.errorMessage = ''
        // const resp = await getCapabilityt4({
        //   tciCategory: this.category,
        //   // pageNo: this.pagination.currentPage,
        //   // pageSize: this.pagination.pageSize,
        //   zcbiId: this.zcbiId // 添加单位ID参数
        // })
        const resp = await getCapabilityt4({
          tciCategory: this.category,
          // pageNo: this.pagination.currentPage,
          // pageSize: this.pagination.pageSize,
          // zcbiId: this.zcbiId // 添加单位ID参数
        })
        if (resp && resp.code == 200 && resp.data) {
          // 兼容 records/list/rows 三种返回
          const arr = resp.data.treeData
          // this.pagination.total = resp.data.total
          this.tableData = this.flattenTreeData(arr)
        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      } catch (e) {
        console.error('加载能力详情失败:', e)
        this.error = true
        this.errorMessage = (e && e.message) || ''
      } finally {
        this.loading = false
      }
    },
    flattenTreeData (nodes) {
      const list = []
      const walk = (arr, nameMap = { t1Name: '', t2Name: '', t3Name: '', t4Name: '' }) => {
        if (!Array.isArray(arr)) return
        arr.forEach(n => {
          const nextMap = { ...nameMap }
          const mark = (n.tciMark || '').toUpperCase()
          const indicator = n.tciCategory || n.tiiName || n.label || ''
          if (mark === 'T1') { nextMap.t1Name = indicator; nextMap.t2Name = ''; nextMap.t3Name = ''; nextMap.t4Name = '' }
          else if (mark === 'T2') { nextMap.t2Name = indicator; nextMap.t1Name = ''; nextMap.t3Name = ''; nextMap.t4Name = '' }
          else if (mark === 'T3') { nextMap.t3Name = indicator; nextMap.t1Name = ''; nextMap.t2Name = ''; nextMap.t4Name = '' }
          else if (mark === 'T4') { nextMap.t4Name = indicator; nextMap.t1Name = ''; nextMap.t2Name = ''; nextMap.t3Name = '' }
          const current = { ...n, ...nextMap }
          list.push(current)
          if (Array.isArray(n.children) && n.children.length > 0) walk(n.children, nextMap)
        })
      }
      walk(nodes)
      return list
    },
    handleSizeChange (val) {
      this.pagination.pageSize = val
      this.pagination.currentPage = 1
      this.fetchData()
    },
    handleCurrentPageChange (val) {
      this.pagination.currentPage = val
      this.fetchData()
    },
    handleDetail (row) {
      console.log('查看资产详情:', row)
      this.currentCapabilityData = row
      this.assetDetailDialogVisible = true
    },
    // 关闭资产详情弹窗
    handleCloseAssetDetail() {
      this.assetDetailDialogVisible = false
      this.currentCapabilityData = {}
    },
    retry () {
      this.fetchData()
    }
  }
}
</script>

<style scoped>
.dialog-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.table-container {
  flex: 1;
  min-height: 0;
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}
.table-error-container,
.table-empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #909399;
  flex-direction: column;
}
.placeholder-icon {
  font-size: 36px;
  color: #dcdfe6;
}
.placeholder-message {
  margin-top: 8px;
}
</style>

