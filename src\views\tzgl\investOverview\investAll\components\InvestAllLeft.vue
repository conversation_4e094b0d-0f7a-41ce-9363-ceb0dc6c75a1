<template>
  <div>
    <div class="investAllLeft">
       <div class="cardHeader">
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;">
            <p>
              <el-image
                alt=""
                :src="require('@/assets/tzgl/icon_swwghqk.png')"
              />
            </p>
            <span style="margin-left: 2px;">规划情况</span>
          </div>
          <p style="margin-right: 10px;cursor: pointer; font-size: 14px;font-weight: 400;" @click="handleOpenDialog('gh')">查看详情</p>
        </div>
      </div>
      <div class="cardBody">
        <div class="cardMain">
          <p class="title">规划总投资</p>
          <p>
            <span class="num">{{ yearAll.gh }}</span>
            <span class="unit">亿元</span>
          </p>
        </div>
        <div class="cardMain">
          <p class="title">规划执行总投资</p>
          <p>
            <span class="num">{{ yearAll.ghzx }}</span>
            <span class="unit">亿元</span>
          </p>
        </div>
      </div>
    </div>
    <div class="investAllLeft">
       <div class="cardHeader">
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;">
            <p>
              <el-image
                alt=""
                :src="require('@/assets/tzgl/icon_tzjh.png')"
              />
            </p>
            <span style="margin-left: 2px;">投资计划</span>
          </div>
          <p style="margin-right: 10px;cursor: pointer; font-size: 14px;font-weight: 400;" @click="handleOpenDialog('jh')">查看详情</p>
        </div>
      </div>
      <div class="cardBody">
        <div class="cardMain">
          <p class="title">计划投资金额/项数</p>
          <p>
            <span class="num">{{ (yearAll.tzje || 0).toFixed(2) }}</span>
            <span class="unit">亿元</span>
             <span></span>
          <span class="num">{{ yearAll.tzs }}</span>
          <span class="unit">项</span>
          </p>
        </div>
        <div class="cardMain">
          <p class="title">计划回收金额/项数</p>
          <p>
            <span class="num">{{ (yearAll.hsje || 0).toFixed(2) }}</span>
            <span class="unit">亿元</span>
             <span></span>
          <span class="num">{{ yearAll.hss }}</span>
          <span class="unit">项</span>
          </p>
        </div>
      </div>
    </div>
    <div class="investAllLeft">
       <div class="cardHeader">
        <div style="display: flex;align-items: center;">
          <p>
             <el-image
              alt=""
              :src="require('@/assets/tzgl/icon_tzjc.png')"
            />
          </p>
          <span style="margin-left: 2px;">投资决策</span>
        </div>
      </div>
      <div class="cardBody">
        <div class="cardMain">
          <p class="title">预审项目数</p>
          <p>
            <span class="num">{{ yearAll.ysxms }}</span>
            <span class="unit">项</span>
          </p>
        </div>
         <div class="cardMain">
          <p class="title">审批中项目数</p>
          <p>
            <span class="num">{{ yearAll.spxms }}</span>
            <span class="unit">项</span>
          </p>
        </div>
        <div class="cardMain">
          <p class="title">已批复项目数</p>
          <p>
            <span class="num">{{ yearAll.pfxd }}</span>
            <span class="unit">项</span>
          </p>
        </div>
       
      </div>
    </div>

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="1vh"
      width="99%"
      height="85vh"
    > 
      <memberUnits slot="content" v-if="dialogName == 'gh' && loginUser.roles == 'MemberUnit'" />
      <InvestPlanDetail slot="content" v-else-if="dialogName == 'jh'" />
      <groupCompany slot="content" v-else />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
      </template>
    </DialogCard>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import DialogCard from "common/DialogCard";
  import InvestPlanDetail from './InvestPlanDetail'
  import InvestPlanHsje from './InvestPlanHsje'
  import memberUnits from '@/views/tzgl/investDesign/memberUnits'
  import groupCompany from '@/views/tzgl/investDesign/groupCompany'
  export default {
    name: "investAllLeft",
    props: {
      parentData: {
        type: Object,
        default: () => {}
      },
      yearAll:{
        type: Object,
        default: () => {}
      }
    },
    components: {
      DialogCard,
      InvestPlanDetail,
      InvestPlanHsje,
      memberUnits,
      groupCompany
    },
    data() {
      return {
        dialogVisible: false,
        dialogName: ''
      }
    },
    computed: {
      ...mapGetters({
        loginUser: 'user/loginUser'
      }),
    },
    methods: {
      // 打开弹窗
      handleOpenDialog(dialogName) {
        this.title = '投资计划详情'
        this.dialogName = dialogName
        this.dialogVisible = true
      },
      // 关闭弹窗
      closeDialog() {
        this.dialogVisible = false
      }
    },
  }
</script>

<style lang="scss" scoped>
  
  .investAllLeft {
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #E4E7ED;
    margin-top: 16px;
    .cardHeader {
      height: 36px;
      background: linear-gradient( 270deg, rgba(204,18,20,0) 0%, rgba(204,18,20,0.08) 100%);
      border-bottom: 1px solid #E4E7ED;
      display: flex;
      align-items: center;
      font-weight: 600;
      color: #303133;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // padding-right: 16px;
      &>div {
        display: flex;
      }
      p {
        margin: 0px;
        padding-left: 16px;
        height: 20px;
        display: flex;
        >img{
          width: 16px;
          height: 16px;
        }
      }
    }
    .cardBody {
      display: flex;
      .cardMain{
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-right: 1px solid;
        /* 设置渐变边框 */
        // border-right: 1px solid #E4E7ED;
        border-image: linear-gradient(180deg, rgba(228, 231, 237, 0), rgba(228, 231, 237, 1), rgba(228, 231, 237, 0)) 1 1;
        padding: 19px 0;
        p{
          margin: 0;
        }
      }
      .cardMain:last-child{
        border: none;
      }
      .title{
        color: #303133;
        line-height: 20px;
      }
      .num{
        font-size: 20px;
        color: #CC1214;
        line-height: 23px;
        font-weight: bolder;
      }
      .unit{
        font-weight: 500;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
      }
    }
  }
  .investAllLeft:first-child{
    margin-top: 0;
  }
</style>