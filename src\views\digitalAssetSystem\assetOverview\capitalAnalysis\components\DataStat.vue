<template>
  <div class="dataStat-container">
    <div class="bottom">
      <div
        class="bottomItem"
      >
        <div class="itemUp" @click='openPage("企业数量")'>
          <p class="title">企业数量</p>
          <p><span class="nums">{{ total }}</span>户</p>
        </div>
        <div class="itemDown">
          <div class="item_left" @click='openPage("上市公司")'>
            <p class="title">上市公司</p>
            <p><span class="nums">{{ ssNum }}</span>户</p>
          </div>
          <div class="item_left" @click='openPage("科技型公司")'>
            <p class="title">科技型公司</p>
            <p><span class="nums">{{ kjNums }}</span>户</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  // import { enterpriseQuantity } from '@/api/sam/assetFx'
  export default {
    name: "DataStat",
    components: {},
    data() {
      return {
        total: null,
        ssNum: null,
        kjNums: null
      }
    },
    mounted() {
      this.getCompanyData()
    },
    methods: {
      async getCompanyData() {
        // let { data } = await enterpriseQuantity()
        // this.total = data[1].total
        // this.ssNum = data[2].total
        // this.kjNums = data[0].total

      },
      openPage(title){
        // this.$router.push({
        //   path: '/views/cockpit/assetCapitalAnalysis/DataStatisticsDetail',
        //   query: {
        //     title: title
        //   }
        // })
      }
    },
  }
</script>

<style lang="scss" scoped>
  .itemUp,.itemDown{
    cursor: pointer;
  }
  .dataStat-container {
    width: 100%;
    height: 100%;
    .bottom {
      width: 100%;
      height: 204px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .bottomItem {
        width: 100%;
        height: 100%;
        background: rgba(204,18,20,0.04);
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E4E7ED;
        .itemUp {
          width: 100%;
          height: 50%;
          border-bottom: 1px solid #E4E7ED;
          text-align: center;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000000;
          line-height: 22px;
          padding-top: 22px;
          p:first-child {
            margin-bottom: 8px;
          }
          .nums {
            font-size: 24px;
            font-weight: 500;
          }
        }
        .itemDown {
          display: flex;
          width: 100%;
          height: 50%;
          .item_left {
            width: 50%;
            height: 100%;
            text-align: center;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000000;
            line-height: 22px;
            padding-top: 22px;
            p:first-child {
              margin-bottom: 8px;
            }
            .nums {
              font-size: 24px;
              font-weight: 500;
            }
          }
          .item_left:first-child {
            border-right: 1px solid #E4E7ED;
          }
        }
        p {
          margin: 0px;
        }
      }
    }
  }
</style>
