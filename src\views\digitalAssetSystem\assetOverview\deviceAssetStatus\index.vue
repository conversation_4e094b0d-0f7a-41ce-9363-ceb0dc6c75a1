<template>
  <div class="assetInstrument-container">
    <div class="container-body">
      <div class="houseOverall">
        <CardBox :title="'仪器设备总体情况'">
          <IstrumentOverall/>
        </CardBox>
      </div>
      <!-- 仪器设备统计情况 -->
      <div class="ysqbStatDetail">
        <CardBox :title="'仪器设备统计情况'">
          <YsqbStatDetail />
        </CardBox>
      </div>

      <!-- 2023年仪器设备出租计划情况 -->
      <div class="memberUnitsDetail">
        <CardBox title="仪器设备出租计划情况">
          <MemberUnitsDetail />
        </CardBox>
      </div>

    </div>
  </div>
</template>

<script>
  import CardBox from '@/views/common/CardBox'
  import SmallCard from '@/views/digitalAssetSystem/assetOverview/components/SmallCard.vue'
  import IstrumentOverall from './components/IstrumentOverall'
  import YsqbStatDetail from './components/YsqbStatDetail'
  import MemberUnitsDetail from './components/MemberUnitsDetail'
  // import { getZcglDateManage } from '@/api/projectManagement/fixedAssets'
  export default {
    name: "AssetInstrument",
    components: {
      CardBox,
      SmallCard,
      IstrumentOverall,
      YsqbStatDetail,
      MemberUnitsDetail
    },
  }
</script>

<style lang="scss" scoped>
  .assetInstrument-container {
    width: 100%;
    height: 100%;
    background: #F6F8F9!important;
    padding: 0px!important;
    .houseOverall {
      height: 196px;
    }
    .ysqbStatDetail {
      margin-top: 24px;
    }
    .memberUnitsDetail {
      margin-top: 24px;
    }
  }
</style>