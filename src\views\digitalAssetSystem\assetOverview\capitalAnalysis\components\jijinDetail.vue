<template>
  <div class="container">
    <el-form
      ref="form"
      :inline="true"
      :model="queryForm"
      @submit.native.prevent
    >
      <el-form-item label='基金名称：' v-if='type==1'>
        <el-input v-model="queryForm.zclfName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label='基金名称：' v-if='type==2'>
        <el-input v-model="queryForm.zcpfName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label='年份：'>
        <el-date-picker v-model.trim="queryForm.year" format="yyyy" value-format="yyyy" clearable type="year" placeholder="选择年"></el-date-picker>
      </el-form-item>
      <el-form-item label='月份：'>
        <el-date-picker v-model.trim="queryForm.month" format="MM" value-format="MM" clearable type="month" placeholder="选择年"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          native-type="submit"
          @click='clear'
        >
          重置
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button
          native-type="submit"
          type="primary"
          @click='search'
        >
          搜索
        </el-button>
      </el-form-item>
    </el-form>
    <el-table v-if='type==1'
      border
      :data="tableData"
      height="441px"
      highlight-current-row
      style="margin-top: 16px"
    >
      <template slot="empty">
        <img src="@/assets/empty_images/data_empty.png" alt="" />
      </template>
      <el-table-column label="序号" type="index" align="center" width="80" :index="indexMethod" />
      <el-table-column
        align="center"
        label="基金名称"
        prop="zclfName"
        width="200"
        show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        align="center"
        label="成立日期"
        prop="zclfEstDate"
        width="95"
      />
      <el-table-column
        align="center"
        label="备案编号"
        prop="zclfFilingNum"
        width="200"
        show-overflow-tooltip
      />
      <el-table-column
        align="center"
        label="管理人"
        width="300"
        prop="zclfManageName"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        label="本期规模"
        prop="zclfCurrentScale"
        width="200"
        show-overflow-tooltip
      />
      <el-table-column
        align="center"
        label="集团内认缴金额"
        prop="zclfSubAmount"
        width="150"
      />
      <el-table-column
        align="center"
        label="实缴规模"
        prop="zclfPaidinScale"
        width="150"
      />
      <el-table-column
        align="center"
        label="集团内实缴金额"
        prop="zclfPaidinAmount"/>
      <el-table-column
        align="center"
        label="投资领域"
        prop="zclfInvestment"
        width="150"
        show-overflow-tooltip/>
      <el-table-column
        align="center"
        label="存续期"
        prop="zclfDuration"
        width="150"
        show-overflow-tooltip/>
      <el-table-column
        align="center"
        label="当前阶段"
        prop="zclfCurrentStage"
        width="150"
        show-overflow-tooltip/>
      <el-table-column
        align="center"
        label="累计已投金额"
        prop="zclfTotalInvestedAmount"
        width="200"/>
      <el-table-column
        align="center"
        label="其中：本年累计已投金额"
        prop="zclfInvestedAmount"
        width="200"/>
      <el-table-column
        align="center"
        label="累计已投项目个数"
        prop="zclfTotalProj"/>
      <el-table-column
        align="center"
        label="其中：本年累计已投项目个数"
        width="200"
        prop="zclfProj"/>
      <el-table-column
        align="center"
        label="累计已回收金额"
        width="200"
        prop="zclfTotalRecoveredAmount"/>
      <el-table-column
        align="center"
        label="其中：本年累计已回收金额"
        width="200"
        prop="zclfRecoveredAmount"/>
      <el-table-column
        align="center"
        label="累计已退出项目个数"
        width="200"
        prop="zclfTotalExitedProj"/>
      <el-table-column
        align="center"
        label="其中：本年累计已退出项目个数"
        width="200"
        prop="zclfExitedProj"/>
      <el-table-column
        align="center"
        label="剩余可用投资金额"
        width="200"
        prop="zclfRemainAvailableAmount"/>
      <el-table-column
        align="center"
        label="累计已分配金额"
        width="200"
        prop="zclfTotalAllocatedAmount"/>
      <el-table-column
        align="center"
        label="其中：本年累计已分配金额"
        width="200"
        prop="zclfAllocatedAmount"/>
      <el-table-column
        align="center"
        label="集团内收回金额"
        width="200"
        prop="zclfTotalRecoveryAmount"/>
      <el-table-column
        align="center"
        label="其中：本年集团内收回金额"
        width="200"
        prop="zclfRecoveryAmount"/>
    </el-table>
    <el-table v-if='type==2'
      border
      :data="tableData"
      height="441px"
      highlight-current-row
      style="margin-top: 16px"
    >
      <template slot="empty">
        <img src="@/assets/empty_images/data_empty.png" alt="" />
      </template>
      <el-table-column label="序号" type="index" align="center" width="80" :index="indexMethod" />
      <el-table-column
        align="center"
        label="基金名称"
        width="250"
        show-overflow-tooltip
        prop="zcpfName">
      </el-table-column>
      <el-table-column
        align="center"
        label="投资主体"
        width="300"
        show-overflow-tooltip
        prop="zcpfInvestSub">
      </el-table-column>
      <el-table-column
        align="center"
        label="成立日期"
        width="100"
        show-overflow-tooltip
        prop="zcpfEstDate">
      </el-table-column>
      <el-table-column
        align="center"
        label="管理人"
        width="100"
        show-overflow-tooltip
        prop="zcpfManageName">
      </el-table-column>
      <el-table-column
        align="center"
        label="本期规模"
        width="150"
        show-overflow-tooltip
        prop="zcpfCurrentScale">
      </el-table-column>
      <el-table-column
        align="center"
        label="集团认缴金额"
        width="150"
        show-overflow-tooltip
        prop="zcpfSubAmount">
      </el-table-column>
      <el-table-column
        align="center"
        label="集团实缴金额"
        width="150"
        show-overflow-tooltip
        prop="zcpfPaidinAmount">
      </el-table-column>
      <el-table-column
        align="center"
        label="投资领域"
        width="150"
        show-overflow-tooltip
        prop="zcpfInvestment">
      </el-table-column>
      <el-table-column
        align="center"
        label="集团收回金额"
        width="150"
        show-overflow-tooltip
        prop="zcpfTotalRecoveryAmount">
      </el-table-column>
      <el-table-column
        align="center"
        label="其中：本年集团收回金额"
        prop="zcpfRecoveryAmount">
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryForm.pageNo"
      :page-sizes="[10,20,50,100, 200, 500]"
      :page-size="queryForm.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>
  </div>
</template>

<script>
  import CardBox from '@/views/common/CardBox'
  // import { getFundDetails,getGroupParticipationFund  } from '@/api/sam/assetCapitalAnalysis'
  export default {
    name: 'jijinDetail',
    components: {
      CardBox
    },
    props: ['type'],
    data() {
      return {
        tableData: [],
        total:0,
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          zclfName:'',
          zcpfName:'',
          year: new Date().getFullYear() + '',
          month: '',
        },
      }
    },
    watch: {
      esopId: {
        handler: function (val) {

        },
        immediate: true,
      },
      type: {
        handler: function (val) {
          this.getData()
        },
        immediate: true,
      },
    },
    mounted() {

    },
    created() {
      this.title = this.$route.query.title
      // this.getData()
    },
    methods: {
      search(){
        this.queryForm.pageNo = 1
        this.getData()
      },
      clear(){
        let pageNo = this.queryForm.pageNo
        let pageSize = this.queryForm.pageSize
        this.queryForm = {
          pageNo: pageNo,
          pageSize: pageSize,
          esopCompanyName: '',
          year:'',
        }
        this.search()
      },
      indexMethod(index){
        index = (index + 1) + (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        return index
      },
      getData(){
        if(this.type=='1'){
          // getFundDetails(this.queryForm).then((res) => {
          //   if (res.code === '200') {
          //     this.tableData = res.data.list
          //     this.total = res.data.total
          //   } else {
          //     this.$message.warning(res.msg || '获取数据失败')
          //   }
          // })
        }else if(this.type=='2'){
          // getGroupParticipationFund(this.queryForm).then((res) => {
          //   if (res.code === '200') {
          //     this.tableData = res.data.list
          //     this.total = res.data.total
          //   } else {
          //     this.$message.warning(res.msg || '获取数据失败')
          //   }
          // })
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.getData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.getData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .company {
    display: flex;
    height: 248px;
    .left {
      width: 20%;
      margin-right: 24px;
      img {
        width: 100%;
        height: 225px;
      }
    }
    .right {
      width: 80%;
    }
  }
  .closePopBtn {
    cursor: pointer;
  }
  .container {
    width: 100%;
  }
  :deep(.el-table__empty-block) {
    position: relative;
    .el-table__empty-text {
      position: fixed;
      left: 25vw
    }
  }
</style>
