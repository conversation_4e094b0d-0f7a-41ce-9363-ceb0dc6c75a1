<template>
  <el-row :gutter="20">
    <el-col :span="6">
      <CardBox title="规划方向">
        <el-form
          ref="form"
          label-width="100px"
          @submit.native.prevent
          :model="searchForm">
        </el-form>
        <el-tree 
          :data="treeData" 
          :style="{height: treeHeight}" 
          default-expand-all
          :props="defaultProps" 
          :expand-on-click-node="false"
          @node-click="handleNodeClick" />
      </CardBox>
    </el-col>
    <el-col :span="18">
      <AssetDispose flag="sum" :lpvId="lpvId"/>
    </el-col>
  </el-row>
</template>

<script>
  import CardBox from 'common/CardBox'
  import AssetDispose from '../AssetDispose'
  import { getSysValRedisList, getLeftTree } from '@/api/lesysparamvals'
  import { listToTree } from '@/utils/tools/tree'
  export default {
    name: "DesignZc",
    components: {
      CardBox,
      AssetDispose,
      
    },
    data() {
      return {
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        treeHeight: this.$baseTableHeight() - 67 + 'px',
        defaultProps: {
          children: 'children',
          label: 'name'
        },
        treeData: [],
        activeName: 'first',
        queryForm: {},
        lpvId: ""
      }
    },
    created() {
      this.getSelectOptions()
    },
    methods: {
      async getSelectOptions(){
        const tptSort = await getLeftTree();
        if(tptSort.code == 200){
          this.treeData = tptSort.data
        }
      },
      handleNodeClick(node) {
        this.lpvId = node.lpvId
      },
    },
  }
</script>


<style lang="scss" scoped>
  .myRadio {
    display: flex;
    justify-content: center;
  }
  
  ::v-deep .el-radio-button {
    width: 50%;
    border: none;
  }
  ::v-deep .el-radio-button__inner {
    display: inline-block;
    width: 100%;
  }
  ::v-deep .el-tree-node__expand-icon {
    color: #000!important;
  }
  ::v-deep .el-tree-node.is-current > .el-tree-node__content {
    background: rgba(204,18,20,0.04) !important;
    font-weight: 500;
    font-size: 16px;
    color: #CC1214;
  }
  ::v-deep .el-tree-node__content {
    height: 48px!important;
    border-radius: 4px;
    font-weight: 400;
    font-size: 16px;
    color: #303133;
    &:hover {
      background: rgba(204,18,20,0.04) !important;
    }
  }
</style>