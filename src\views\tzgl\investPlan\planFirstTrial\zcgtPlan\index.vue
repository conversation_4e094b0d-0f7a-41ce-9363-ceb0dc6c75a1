<template>
  <div class="gqltzPlan">
    <VabForm  ref="VabForm" :formModel="searchForm" :formItem="formItem" :formConfig="{ inline: true }" width="100%" />
    <VabTable 
      :tableHeader="tableHeader" 
      :tableData="tableData" 
      :tableHeight="tableHeight" 
      :showPagination="false"
    />
     <div class="totalBox">
        {{ '合计本年申报投资总额：' + totalNum + '万元' }}
      </div>
    <DialogCard
      ref="dialogCardRef"
      :dialogTableVisible="dialogVisible"
      v-if="dialogVisible"
      :close="closeDialigCard"
      :title="title"
      :flag="true"
      height="92vh"
      width="99%"
      top="1vh"
    >
      <ZcgtPlanTabs 
        slot="content" 
        :currentRow="currentRow" 
        :parentTitle="title" 
        :parentCloseDialog="()=>{
          closeDialigCard();
          getTableData();
        }" 
      />
    </DialogCard>
  </div>
</template>

<script>
  import VabForm from 'components/VabForm';
  import VabTable from 'components/VabTable';
  import DialogCard from 'common/DialogCard';
  import ZcgtPlanTabs from './components/ZcgtPlanTabs';
  import { getParentList, checkProgressParentReset } from 'api/tzgl/investPlan/planFirstTrial/gqltzPlan';
  import { getsecondaryunitsList } from 'api/tzgl/investPlan/searchList'
  import { mapGetters } from 'vuex'

  export default {
    name:'gqltzPlan',
    props: {
      type: String
    },
    components: {
      VabForm,
      VabTable,
      DialogCard,
      ZcgtPlanTabs
    },
    data(){
      return {
        searchForm:{},
        formItem: [
          { name: 'dateTime', type: 'year', clearable:false, prop: 'tprYear', format: 'yyyy', valueFormat: 'yyyy', label: '年份', placeholder: '请选择年份' },
            {
          name: 'selectTree', 
          props: { value: 'zcbiId', label: 'zcbiName', children: 'children' }, 
          options: [], 
          label: '单位名称',
          isChangeParent:true,
          getSelectTreeValue:this.getSelectTreeValue
        },
        this.type != 'investDetail' ? { name: 'select', options: [
            { label: '未上报', value: '未上报' },
            { label: '已上报，未审核', value: '已上报，未审核' },
            { label: '审核通过', value: '审核通过' },
            { label: '退回调整', value: '退回调整' },
            { label: '批复下达', value: '批复下达' },
          ], prop: 'tprState', label: '评审状态', placeholder: '请选择评审状态' } : {},
          { name: 'button', prop: 'button', label: '查询', type: "primary", click: this.getTableData },
          { name: 'button', prop: 'button', label: '重置', click: this.handleResetFun },
        ],
        tableHeaderField: [
          { type: 'index', width:'55', label: "序号",align:'center',},
          { prop:'tprYear', label: '年份', align: 'center', width:'100' },
          { prop:'zcbiName', label: '单位名称', align: 'center', width:'400' },
          { prop:'ampTotalinvest', label: '本年申报投资总额（万元）', align: 'center' },
          { prop:'tprState', label: '审核状态', align: 'center', width:'170' },
        ],
        investDetailHeader: [
          { type: 'index', width: '55', label: "序号", align: 'center', },
          { prop:'tprYear', label: '年份', align: 'center', width:'100' },
          { prop:'zcbiName', label: '单位名称', align: 'center', width:'400' },
          { prop:'ampTotalinvest', label: '本年申报投资总额（万元）', align: 'center' },
          { prop: 'count', label: '投资金额项数', align: 'center' },
        ],
        czBtn:[
          {type: 'action', label: '操作', align: 'center', render: (h, scope) => {
            const progressBtnShow = scope.row.tprState === '已上报，未审核';
            const progressResetBtnShow = scope.row.tprState === '审核通过';
            return <div>
              {progressBtnShow && <el-button type="text" size="small" onClick={() => this.handleProgress(scope.row)}>审核</el-button>}
              {progressResetBtnShow && <el-button type="text" size="small" onClick={() => this.handleProgressReset(scope.row)}>审核重置</el-button>}
              <el-button type="text" size="small" onClick={() => this.handleDetail(scope.row)}>详情</el-button>
            </div>
          }},
        ],
        tableHeader: [],
        tableData: [],
        tableHeight: this.$baseTableHeight(1) - 58,
        // 弹框数据
        dialogVisible: false,
        title:'',
        currentRow:{},
        totalNum:0
      }
    },
    computed: {
      ...mapGetters({
        loginUser: 'user/loginUser'
      }),
    },
    created(){
      this.getsecondaryunitsList()
      this.$set(this.searchForm, 'tprYear', new Date().getFullYear()+'');
      this.getTableData();
      const header = this.type == 'investDetail' ? [...this.investDetailHeader] :  [...this.tableHeaderField];
      this.tableHeader = [ ...header, ...this.czBtn]
    },
    methods:{
         //获取二级单位
    async getsecondaryunitsList() {
      const { data, code, msg } = await getsecondaryunitsList()
      if (code == 200) {
        this.formItem[1].options = [data];
      } else {
        this.$message.error(msg);
      }
    },
    getSelectTreeValue(id, name) {
      this.searchForm.tprCompanyId = id
    },
      calculateTotal(data) {
      // 检查 data 是否为数组
      if (!Array.isArray(data)) {
        return 0
      }
      return data.reduce((sum, num) => {
        // 检查 num.TOTAL_DECLARED_AMOUNT 是否为有效的数字
        const amount = parseFloat(num.ampTotalinvest)
        return isNaN(amount) ? sum : sum + amount
      }, 0)
    },
      // 获取列表数据
      async getTableData(){
        const params = {
          ...this.searchForm,
          ...this.tablePage,
          type:3,
          tprCompanyId: this.loginUser.zcbiId
        }
        const {code,msg,data} = await getParentList(params);
        if (code == 200) {
          this.tableData = data;
           this.totalNum = this.calculateTotal(data)
        } else {
          this.$message.error(msg)
        }
      },
      // 查询重置
      handleResetFun(){
         this.$refs.VabForm.$refs.SelectTree[0].clearHandle()
        this.searchForm = {};
        this.$set(this.searchForm, 'tprYear', new Date().getFullYear()+'');
        this.getTableData();
      },
      // 审核
      handleProgress(row){
        this.dialogVisible = true;
        this.currentRow = row;
        this.title = '审核';
      },
      // 审核重置
      async handleProgressReset(row){
        const params = {
          tprId:row.tprId,
          state:'3'
        }
        const {code,msg} = await checkProgressParentReset(params);
        if(code == 200){
          this.$message.success('审核重置成功');
          this.getTableData();
        }else{
          this.$message.error(msg);
        }
      },
      // 详情
      handleDetail(row){
        this.dialogVisible = true;
        this.currentRow = row;
        this.title = '详情';
      },
      // 关闭弹框
      closeDialigCard(){
        this.dialogVisible = false;
      },
    }
  }
</script>

<style lang="scss" scoped>
  .gqltzPlan{
    padding: 16px;
    background: white;
    border: 1px solid #e4e4e4;
  }
  .totalBox {
  margin-bottom: 24px;
  width: 100%;
  height: 56px;
  background: #f5f7fa;
  border: 1px solid #ebeef5;
  font-weight: 600;
  font-size: 16px;
  color: #cc1214;
  line-height: 56px;
  text-align: center;
}
</style>