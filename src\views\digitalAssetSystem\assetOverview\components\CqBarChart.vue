<template>
  <div ref="chartRef" class="bar-chart-container"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    echartsData: {
      type: Array,
      default: () => []
    },
  },
  data () {
    return {
      chart: null,
      chartOption: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach(function (item) {
              if (item.seriesName === '户数') {
                result += item.seriesName + ': ' + item.value + '<br/>'
              } else {
                result += item.seriesName + ': ' + item.value + '%<br/>'
              }
            })
            return result
          }
        },
        legend: {
          data: ['户数', '占比'],
          right: 'center',
          top: 0,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 12,
            color: '#606266'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#E4E7ED'
            }
          },
          axisLabel: {
            color: '#606266',
            fontSize: 12
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '户数',
            position: 'left',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#606266',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: '#F2F6FC',
                type: 'solid'
              }
            }
          },
          {
            type: 'value',
            name: '占比(%)',
            position: 'right',
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#606266',
              fontSize: 12,
              formatter: '{value}'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '户数',
            type: 'bar',
            yAxisIndex: 0,
            data: [],
            itemStyle: {
              color: '#4A90E2',
              borderRadius: [2, 2, 0, 0]
            },
            barWidth: '40%'
          },
          {
            name: '占比',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            itemStyle: {
              color: '#FFFFFF',
              borderColor: '#4A90E2',
              borderWidth: 1
            },
            lineStyle: {
              color: '#4A90E2',
              width: 2
            },
            symbol: 'circle',
            symbolSize: 6
          }
        ]
      }
    }
  },
  watch: {
    echartsData: {
      deep: true,
      handler () {
        this.initChart() // 数据变化时重新渲染图表
      },
    },
  },
  mounted () {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    initChart () {
      this.$nextTick(() => {
        this.chartOption.xAxis.data = this.echartsData.map(item => { return item.zcbiLevel + '级' })
        this.chartOption.series[0].data = this.echartsData.map(item => item.quantity)

        // 计算总数
        const totalQuantity = this.echartsData.reduce((sum, item) => sum + item.quantity, 0)

        // 计算每项的占比值（保留两位小数）
        this.chartOption.series[1].data = this.echartsData.map(item => {
          return totalQuantity > 0 ? parseFloat(((item.quantity / totalQuantity) * 100).toFixed(2)) : 0
        })

        this.chart = echarts.init(this.$refs.chartRef, null, { renderer: 'svg' })
        this.chart.setOption(this.chartOption)
      })

    },

    handleResize () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bar-chart-container {
  width: 100%;
  height: 100%;
}
</style>
