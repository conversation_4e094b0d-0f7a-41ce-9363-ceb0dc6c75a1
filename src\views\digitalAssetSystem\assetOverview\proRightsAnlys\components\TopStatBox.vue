<template>
  <div class="topStatBox-container">
    <div 
      class="statItem"
      v-for="(item,index) in statItemData"
      :key="index"
      :style="{'background': item.bgColor}"
      @click="statItemClick(item,index)"
    >
      <img class="iconImg" :src="item.iconSrc" alt="">
      <div class="itemCont">
        <p>{{ item.title }}</p>
        <p><span class="nums">{{ item.nums }}</span>{{ item.util }}</p>
      </div>
      <img class="bgImg" :src="item.bgSrc" alt="">
    </div>
  </div>
</template>

<script>
  // import { getPropertyStatistics } from '@/api/sam/property'
  export default {
    name: "TopStatBox",
    components: {},
    data() {
      return {
        statItemData: [
          {
            title: '总户数',
            nums: 0,
            util: '家',
            iconSrc: require('@/assets/assetCqfx/icon_zhs.png'),
            bgSrc: require('@/assets/assetCqfx/bg_zhs.png'),
            bgColor: 'rgba(255,157,79,0.04)'
          },
          {
            title: '二级单位',
            nums: 0,
            util: '家',
            iconSrc: require('@/assets/assetCqfx/icon_ejdw.png'),
            bgSrc: require('@/assets/assetCqfx/bg_ejdw.png'),
            bgColor: 'rgba(36,150,255,0.04)'
          },
          {
            title: '科研院所',
            nums: 0,
            util: '家',
            iconSrc: require('@/assets/assetCqfx/icon_kyys.png'),
            bgSrc: require('@/assets/assetCqfx/bg_kyys.png'),
            bgColor: 'rgba(99,218,171,0.04)'
          },
          {
            title: '上市公司',
            nums: 0,
            util: '家',
            iconSrc: require('@/assets/assetCqfx/icon_ssgs.png'),
            bgSrc: require('@/assets/assetCqfx/bg_ssgs.png'),
            bgColor: 'rgba(101,119,152,0.04)'
          }
        ]
      }
    },
    created() {
      this.getStatData()
    },
    methods: {
      async getStatData() {
        // let {data} = await getPropertyStatistics()
        // this.statItemData[0].nums = data[0].total
        // this.statItemData[1].nums = data[0].totalEJ
        // this.statItemData[2].nums = data[0].totalKY
        // this.statItemData[3].nums = data[0].totalSH
      },
      statItemClick(item,index) {
        this.$router.push({
          name: 'AssetCaAssayDetail',
          query: {
            data: JSON.stringify(this.statItemData),
            title: '企业总数',
            currentName: item.title,
            tableIndex: index
          }
        })
      } 
    },
  }
</script>

<style lang="scss" scoped>
  .topStatBox-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .statItem {
      width: calc((100% - 72px) / 4);
      height: 100%;
      display: flex;
      align-items: center;
      background: rgba(255,157,79,0.04);
      box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
      border-radius: 4px;
      border: 1px solid #E4E7ED;
      position: relative;
      cursor: pointer;
      p {
        margin: 0px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        margin-left: 24px;
      }
      .iconImg {
        margin-left: 48px;
      }
      .nums {
        font-size: 32px;
        font-weight: 600;
        margin-right: 8px;
      }
      .bgImg {
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
  }
</style>