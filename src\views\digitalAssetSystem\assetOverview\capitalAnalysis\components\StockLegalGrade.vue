<template>
  <div class="stockCompanyType-container">
    <VabChartPie :option="option" title='ygcgqk'/>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { LegalEntityLevel } from '@/api/sam/assetFx'
  export default {
    name: "StockLegalGrade",
    components: {
      VabChartPie,
    },
    data() {
      return {
        option: {}
      }
    },
    mounted() {
      this.getEchartData()
    },
    methods: {
      async getEchartData() {
        // let { data } = await LegalEntityLevel()
        // let xData = []
        // let yData = []
        // data.forEach(item => {
        //   xData.push(item.esopLegalLevel)
        //   yData.push(item.total)
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow'
        //     }
        //   },
        //   // legend: {},
        //   grid: {
        //     left: '2%',
        //     right: '8%',
        //     bottom: '1%',
        //     top:'14%',
        //     containLabel: true
        //   },
        //   xAxis: {
        //     name: '层级',
        //     type: 'category',
        //     data: xData
        //   },
        //   yAxis: {
        //     name: '户数',
        //     type: 'value'
        //   },
        //   series: [
        //     {
        //       name: '总户数',
        //       data: yData,
        //       barWidth: '30',
        //       type: 'bar'
        //     }
        //   ]
        // }
      }
    },
  }
</script>

<style lang="scss" scoped>
  .stockCompanyType-container {
    width: 100%;
    height: 100%;
  }

  :deep() {
    .echarts {
      width: 100%;
      height: 204px;
    }
  }
</style>
