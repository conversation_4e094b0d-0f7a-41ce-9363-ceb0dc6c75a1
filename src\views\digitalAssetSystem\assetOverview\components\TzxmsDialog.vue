<!--添加新分组弹窗-->
<template>
  <DialogCard :dialogTableVisible="dialogVisible" :title="title" :close="handleCancel" :flag="true" width="1700px" height="700px" top="5vh">
    <div slot="content" class="add-group-content">

      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="项目名称:">
              <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="inputW" />
            </el-form-item>
            <el-form-item label="项目编号:">
              <el-input v-model="searchForm.code" placeholder="请输入项目名称" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
          </el-form-item>
        </div>
      </el-form>

      <el-table :data="tableData" style="width: 100%;margin-bottom: 50px;" height="500px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="PROJECT_NAME" label="项目名称" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="PROJECT_CODE" label="项目编号" align="center" width="100" show-overflow-tooltip />
        <!-- <el-table-column prop="PROJECT_FULL_NAME" label="项目全称" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="PARENT_PROJECT_NAME" label="父级项目" align="center" width="100" show-overflow-tooltip /> -->
        <el-table-column prop="CORP_NAME" label="投资主体" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="SECONDARY_UNIT_NAME" label="二级单位" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="APPROVAL_TYPE_NAME" label="批复主体类型" align="center" width="200" show-overflow-tooltip />
        <!-- <el-table-column prop="PARENT_PROJECT_TYPE_NAME" label="父级项目类型" align="center" width="100" show-overflow-tooltip /> -->
        <el-table-column prop="PROJECT_TYPE_NAME" label="项目类型" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="PARENT_PROJECT_PHASE_NAME" label="父级项目阶段" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="PROJECT_PHASE_NAME" label="项目阶段" align="center" width="100" show-overflow-tooltip />
        <!-- <el-table-column prop="PROJECT_PHASE_STATUS" label="项目阶段状态" align="center" width="100" show-overflow-tooltip /> -->
        <el-table-column prop="INVEST_TYPE_NAME" label="投资类型" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="INVEST_DIRECTION_NAME" label="投资方向" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="COUNTRY_NAME" label="国家" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="PROVINCE_NAME" label="省份" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="CITY_NAME" label="城市" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="AREA_NAME" label="区域" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="STREET_NAME" label="街道" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ADDRESS" label="位置" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="IS_INVOLVE_MAJOR_ADJUST" label="是否涉及重大调整" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="IS_ADVANCE_START" label="是否提前启动" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="TOTAL_INVEST_AMOUNT" label="总投资" align="center" width="100" show-overflow-tooltip />
        <!-- <el-table-column prop="PROJECT_STATUS" label="项目状态" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="IS_PLAN_PROJECT" label="是否计划项目" align="center" width="100" show-overflow-tooltip /> -->
        <!-- <el-table-column prop="VERSION_NO" label="版本号" align="center" width="100" show-overflow-tooltip /> -->
        <!-- <el-table-column prop="FORMAL_TIME" label="正式时间" align="center" width="100" show-overflow-tooltip /> -->
        <el-table-column prop="REMARK" label="备注" align="center" width="100" show-overflow-tooltip />
      </el-table>

      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />

    </div>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard.vue'
import { getIndicatorValueWithParamsPage } from '@/api/digitalAssetSystem/assetOverview.js'

export default {
  components: { DialogCard },

  data () {
    return {
      dialogVisible: false,
      tableData: [],
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        code: ''
      },
      total: 0,
      title: ''
    }
  },

  methods: {

    handleShow (title) {
      this.title = title
      this.dialogVisible = true
      this.getList()
    },

    getList () {
      getIndicatorValueWithParamsPage({
        indicatorCode: 'TZ_PROJECT_DETAIL',
        pageNo: this.searchForm.pageNo,
        pageSize: this.searchForm.pageSize,
        params: {
          name: this.searchForm.name,
          code: this.searchForm.code,
        },
      }).then(res => {
        console.log("🚀🚀 ~ 投资项目-二级页面 ~ 🚀🚀", res.data.list)
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetQuery () {
      // 重置所有查询字段
      this.rest()
      this.getList()
    },

    rest () {
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
    },

    handleCancel () {
      this.dialogVisible = false
      this.tableData = []
      this.rest()
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.getList()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 100%;
}

.leftRight {
  display: flex;
  // justify-content: space-between;
}
</style>
