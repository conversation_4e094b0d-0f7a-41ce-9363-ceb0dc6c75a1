<template>
  <div class="thisYearApprovalPro-container">
    <div class="contItem">
      <p>项目数</p>
      <p><span>{{ xmNum }}</span>项</p>
    </div>
    <div class="contItem">
      <p>融资总金额</p>
      <p><span>{{ rzNum }}</span>万元</p>
    </div>
  </div>
</template>

<script>
  // import { approvedProjectsForThisYear } from '@/api/sam/assetFx'
  export default {
    name: "ThisYearApprovalPro",
    components: {},
    props: ['year'],
    data() {
      return {
        xmNum: null,
        rzNum: null
      }
    },
    mounted() {
      this.getData()
    },
    methods: {
      async getData() {
        let params = {
          year: this.year
        }
        // let { data } = await approvedProjectsForThisYear(params)
        // this.xmNum = data[1].total || 0
        // this.rzNum = data[0].total || 0
      }
    },
  }
</script>

<style lang="scss" scoped>
  .thisYearApprovalPro-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    
    .contItem {
      width: calc((100% - 24px) / 2);
      height: 204px;
      background: rgba(255,132,135,0.04);
      border-radius: 4px;
      border: 1px solid #E4E7ED;
      text-align: center;
      p {
        margin: 0px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        span {
          color: #000000;
          font-size: 24px;
          margin-right: 4px;
        }
      }
      p:first-child {
        margin-top: 73px;
      }
      p:last-child {
        margin-top: 4px;
      }
    }
  }
</style>