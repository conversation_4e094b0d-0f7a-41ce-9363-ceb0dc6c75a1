<!--实物资产档案 - 土地-->
<template>
  <div class="land-container">

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产编号:">
              <el-input v-model="searchForm.zcliAssetsNo" placeholder="请输入资产编号" class="inputW" />
            </el-form-item>
            <el-form-item label="土地权属证号:">
              <el-input v-model="searchForm.zcliCertificateCode" placeholder="请输入土地权属证明编号" class="inputW" />
            </el-form-item>
            <el-form-item label="现状/用途:">
              <el-input v-model="searchForm.zcliUseDescribe" placeholder="请输入现状/用途" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">综合查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
<!--            <el-button icon="el-icon-plus" type="primary" @click="handleAdd">添加</el-button>-->
<!--            <el-button icon="el-icon-download" type="primary" @click="handleExportTmpl">导出模板</el-button>-->
<!--            <el-upload :show-file-list="false" action="" :accept="fileAccept" auto-upload :disabled="fileUploadBtnText == '正在导入'" :http-request="uploadFile" style="margin-left: 10px;display: inline-block;">-->
<!--              <el-button type="primary" :icon="uploadBtnIcon">导入</el-button>-->
<!--            </el-upload>-->
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="landTable" :data="tableData" :height="height" border stripe highlight-current-row style="width: 100%;" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center" label-class-name="number" />
        <el-table-column prop="zcliAssetsNo" label="资产编号" width="100" show-overflow-tooltip align="center" label-class-name="zcliAssetsNo" />
        <el-table-column prop="zcliUseDescribe" label="现状/用途" width="120" show-overflow-tooltip align="center" label-class-name="zcliUseDescribe" />
        <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip align="center" label-class-name="companyName" />
        <el-table-column prop="zcliCertificateCode" label="土地权属证号" width="120" show-overflow-tooltip align="center" label-class-name="zcliCertificateCode" />
        <el-table-column prop="zcliAddress" label="具体土地位置" width="140" show-overflow-tooltip align="center" label-class-name="zcliAddress" />
        <el-table-column prop="zcliType" label="取得方式" width="100" show-overflow-tooltip align="center" label-class-name="zcliType" />
        <el-table-column prop="zcliDate" label="登记时间" width="120" show-overflow-tooltip align="center" label-class-name="zcliDate" />
        <el-table-column prop="zcliArea" label="土地总面积(㎡)" width="120" align="center" label-class-name="zcliArea" />
        <el-table-column prop="zcliBookValue" label="原值(万元)" width="120" show-overflow-tooltip align="center" label-class-name="zcliBookValue" />
        <el-table-column prop="zcliNetbookValue" label="净值(万元)" width="120" show-overflow-tooltip align="center" label-class-name="zcliNetbookValue" />
        <el-table-column prop="zcliTotalDepreciation" label="本年计提折旧(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zcliTotalDepreciation" />
        <el-table-column prop="zcliRange" label="境内/境外" width="100" show-overflow-tooltip align="center" label-class-name="zcliRange" />
        <el-table-column prop="zcliIfAssets" label="是否两非资产" width="120" align="center" label-class-name="zcliIfAssets" />
        <el-table-column prop="zcliIfExist" label="是否取得土地权属证明" width="170" show-overflow-tooltip align="center" label-class-name="zcliIfExist" />
        <el-table-column prop="zcliIfDispute" label="是否存在纠纷" width="120" show-overflow-tooltip align="center" label-class-name="zcliIfDispute" />
        <el-table-column prop="zcliIfDispose" label="是否可处置" width="120" show-overflow-tooltip align="center" label-class-name="zcliIfDispose" />
        <el-table-column prop="zcliIfMortgage" label="是否已抵押" width="120" show-overflow-tooltip align="center" label-class-name="zcliIfMortgage" />
        <el-table-column prop="zcliOperator" label="经办人" width="100" show-overflow-tooltip align="center" label-class-name="zcliOperator" />
        <el-table-column prop="zcliOperatorTel" label="经办人联系方式" width="120" show-overflow-tooltip align="center" label-class-name="zcliOperatorTel" />
        <el-table-column prop="zcliProvince" label="省份" width="100" show-overflow-tooltip align="center" label-class-name="zcliProvince" />
        <el-table-column prop="zcliCity" label="城市" width="100" show-overflow-tooltip align="center" label-class-name="zcliCity" />
        <el-table-column prop="zcliDistrict" label="坐落位置（区）" width="120" show-overflow-tooltip align="center" label-class-name="zcliDistrict" />
        <el-table-column prop="zcliDeptName" label="业务主管部门" width="140" show-overflow-tooltip align="center" label-class-name="zcliDeptName" />
        <el-table-column prop="zcliDepartmentLeader" label="部门负责人" width="120" show-overflow-tooltip align="center" label-class-name="zcliDepartmentLeader" />
        <el-table-column prop="zcliDepartmentTel" label="部门负责人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepartmentTel" />
        <el-table-column prop="zcliCompanyLeader" label="分管所(公司)领导" width="140" show-overflow-tooltip align="center" label-class-name="zcliCompanyLeader" />
        <el-table-column prop="zcliCompanyTel" label="分管所(公司)领导联系方式" width="110" show-overflow-tooltip align="center" label-class-name="zcliCompanyTel" />
        <el-table-column prop="zcliEvaluateValue" label="最近评估价值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zcliEvaluateValue" />
        <el-table-column prop="zcliEvaluateDate" label="最近评估日期" width="120" show-overflow-tooltip align="center" label-class-name="zcliEvaluateDate" />
        <el-table-column prop="zcliServiceLife" label="土地使用年限(年)" width="140" show-overflow-tooltip align="center" label-class-name="zcliServiceLife" />
        <el-table-column prop="zcliDepreciableYear" label="折旧年限" width="100" show-overflow-tooltip align="center" label-class-name="zcliDepreciableYear" />
        <el-table-column prop="zcliRemark" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zcliRemark" />
        <el-table-column prop="createdTime" label="创建时间" width="120" show-overflow-tooltip align="center" label-class-name="createdTime" />
        <el-table-column prop="createdBy" label="创建人" width="100" show-overflow-tooltip align="center" label-class-name="createdBy" />
        <el-table-column prop="updatedTime" label="更新时间" width="120" show-overflow-tooltip align="center" label-class-name="updatedTime" />
        <el-table-column label="操作" width="120" fixed="right" align="center" label-class-name="_lesoper">
          <template slot-scope="scope">
<!--            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>-->
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
<!--            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <land-detail-dialog ref="landDetail" />

    <!-- 高级查询弹窗组件 -->
    <land-advanced-search-dialog ref="landAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />

    <!-- 添加弹窗组件 -->
    <land-add-dialog ref="landAddDialog" @refresh="fetchData" />
  </div>
</template>

<script>
import { getLandsList, deleteLand } from '@/api/digitalAssetSystem/land'
import LandDetailDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/land/components/LandDetailDialog.vue'
import LandAdvancedSearchDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/land/components/LandAdvancedSearchDialog.vue'
import LandAddDialog from './LandAddDialog.vue'
import { exportRearEnds } from '@/api/exportExcel'
import { exportTmpl } from '@/api/excel'
import { baseURL } from '@/config'
import axios from 'axios'
import store from '@/store'

export default {
  components: {
    LandDetailDialog,
    LandAdvancedSearchDialog,
    LandAddDialog
  },
  data () {
    return {
      height: this.$baseTableHeight(1, 2.7),
      searchForm: {
        zcliAssetsNo: '',
        zcliCertificateCode: '',
        companyName: '',
        zcliOperator: '',
        zcliCity: '',
        zcliAddress: '',
        zcliUseDescribe: '',
        zcliIfDispose: '',
        zcliIfDispute: '',
        zcliIfMortgage: '',
        // 高级查询字段
        zcliType: '',
        zcliDate: '',
        zcliRange: '',
        zcliIfAssets: '',
        zcliIfExist: '',
        zcliArea: '',
        zcliBookValue: '',
        zcliNetbookValue: '',
        zcliTotalDepreciation: '',
        zcliEvaluateValue: '',
        zcliEvaluateDate: '',
        zcliServiceLife: '',
        zcliDepreciableYear: '',
        zcliProvince: '',
        zcliDistrict: '',
        zcliNotobtainedCertificateReason: '',
        zcfaAbility: '',
        zcliOperatorTel: '',
        zcliDeptName: '',
        zcliDepartmentLeader: '',
        zcliDepartmentTel: '',
        zcliCompanyLeader: '',
        zcliCompanyTel: '',
        zcliRemark: '',
        createdBy: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      fileUploadBtnText: "导入",
      uploadBtnIcon: "el-icon-upload2",
      fileAccept: ".xls,.xlsx"
    }
  },
  created () {
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm
      }

      getLandsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },



    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.fetchData()
    },



    handleAdvancedSearch () {
      this.$refs.landAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleDetail (row) {
      this.$refs.landDetail.showDialog(row)
    },

    handleAdd () {
      this.$refs.landAddDialog.showDialog()
    },

    handleEdit (row) {
      this.$refs.landAddDialog.showDialog(row)
    },

    handleDelete (row) {
      this.$confirm(`确定要删除资产编号为"${row.zcliAssetsNo}"的土地信息吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {

        try {
          const response = await deleteLand(row.zcliId)
          if (response && response.code == 200) {
            this.$message.success('删除成功')
            this.fetchData() // 刷新列表
          } else {
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 用户取消删除，不做任何操作
      })
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.fetchData()
    },

    // 导出excel模板
    async handleExportTmpl () {
      //导出模板
      let params = { fileName: "土地资产信息模板.xls", excelIstmpl: true, excelTmplListIds: "INVESTSORTS", excelTmplListCcs: "tptSort", uncols: "createdTime,createdBy,updatedTime," }
      let qf = exportRearEnds("#landTable", params)

      // 直接从excelExps中移除uncols指定的列
      if (params.uncols && qf.excelExps && qf.excelExps.length > 0) {
        let uncolsArray = params.uncols.split(',').filter(col => col.trim() !== '')

        // 遍历每个表头行
        qf.excelExps.forEach(headerRow => {
          // 过滤掉uncols中指定的列
          for (let i = headerRow.length - 1; i >= 0; i--) {
            if (uncolsArray.includes(headerRow[i].field)) {
              headerRow.splice(i, 1)
            }
          }
        })
      }

      const data = await exportTmpl(qf)
      if (data.code == 200) {
        window.open(baseURL + "/" + data.msg)
      } else {
        this.$message({ message: '导出模板操作失败!', type: 'warning' })
      }
    },

    // 导入文件
    async uploadFile (param) {
      let file = param.file
      let fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
      let acceptArr = this.fileAccept + ","
      if (acceptArr.indexOf(fileType + ",") == -1) {
        this.$message({
          message: `${file.name}文件类型不符，请重新选择${this.fileAccept}格式文件`, type: "warning"
        })
      } else {
        this.uploadBtnIcon = "el-icon-loading"
        this.fileUploadBtnText = "正在导入"

        // 创建FormData对象
        let formdata = new FormData()
        formdata.append('file', file)
        formdata.append('fileparam', '对应excel列的实体类名称,逗号分隔，具体此参数什么格式内容请与后端商量')
        let url = baseURL + "/zcgl-land-info/upload"
        try {

          axios({
            url,
            method: 'post',
            data: formdata,
            headers: {
              'Authorization': store.getters['user/token'],
              'Content-Type': 'multipart/form-data'
            }
          }).then(res => {
            if (res.data.code == 200) {
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$message.success('导入成功！')
              this.fetchData()
            } else {
              this.uploadBtnIcon = "el-icon-upload2"
              this.fileUploadBtnText = "导入"
              this.$message.error(res.data.msg)
              if (res.data.data) {
                window.open(baseURL + "/" + res.data.data)
              }
            }
          }).catch(() => {
            this.uploadBtnIcon = "el-icon-upload2"
            this.fileUploadBtnText = "导入"
            this.$message.error('导入失败！')
          })

        } catch (error) {
          this.uploadBtnIcon = "el-icon-upload2"
          this.fileUploadBtnText = "导入"
          this.$message.error('导入失败！')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  // justify-content: space-between;
}
.inputW {
  width: 250px;
}

.land-container {
  padding: 0px;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
