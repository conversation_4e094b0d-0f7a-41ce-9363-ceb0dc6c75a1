<template>
  <div class="container">
    <SmallCard style="margin-bottom: 14px;" :title="'持股企业情况'">
      <el-descriptions direction="vertical" border :column="4">
        <el-descriptions-item label="企业名称">
          {{obj.esopCompanyName}}
        </el-descriptions-item>
        <el-descriptions-item label="企业级次(法人层级)">
          {{obj.esopLegalLevel}}
        </el-descriptions-item>
        <el-descriptions-item label="企业层级(管理层级)">
          {{obj.esopLevel}}
        </el-descriptions-item>
        <el-descriptions-item label="企业成立时间">
          {{obj.esopFormatDate}}
        </el-descriptions-item>
        <el-descriptions-item label="企业性质">
          {{obj.esopNature}}
        </el-descriptions-item>
        <el-descriptions-item label="是否为上市公司">
          {{obj.esopIfPublic}}
        </el-descriptions-item>
        <el-descriptions-item label="是否为科技型企业">
          <template slot="label">是否为科技型企业
            <el-tooltip class="item" effect="dark" placement="bottom">
              <i class="el-icon-question" style="font-size: 14px; vertical-align: middle;"></i>
              <div slot="content">
                按照国有科技型企业股权激<br/>励政策规定确定
              </div>
            </el-tooltip>
          </template>
          {{obj.esopIfTech}}
        </el-descriptions-item>
        <el-descriptions-item label="所属二级单位">
          {{obj.esopCompanyTname}}
        </el-descriptions-item>
        <el-descriptions-item label="企业股东及持股比例">
          {{obj.esopShareholder}}
        </el-descriptions-item>
      </el-descriptions>
    </SmallCard>
    <SmallCard style="margin-bottom: 14px;" :title="'员工持股情况'">
      <el-descriptions direction="vertical" border :column="4">
        <el-descriptions-item label="员工持股时间">
          {{obj.esopStockDate}} ~ {{obj.esopStockDate2}}
        </el-descriptions-item>
        <el-descriptions-item label="员工持股政策依据">
          <template slot="label">员工持股政策依据
            <el-tooltip class="item" effect="dark" placement="bottom">
              <i class="el-icon-question" style="font-size: 14px; vertical-align: middle;"></i>
              <div slot="content">
                133号文出台之前形成的员<br/>工持股、国有控股混合所有<br/>制企业员工持股、国有科技<br/>型企业股权激励、
                科技成果<br/>转化实施股权奖励、创新领<br/>域跟投、利用地方政策开展<br/>的员工持股、
                收购外部民营<br/>企业被动形成的员工持股、<br/>与外部团队合作新设公司被<br/>动形成的员工持股
              </div>
            </el-tooltip>
          </template>
          {{obj.esopStockAccord}}
        </el-descriptions-item>
        <el-descriptions-item label="员工持股形式">
          {{obj.esopStockWay}}
        </el-descriptions-item>
        <el-descriptions-item label="员工持股主体及目前比例">
          {{obj.esopStockMain}}
        </el-descriptions-item>
        <el-descriptions-item label="员工持股目前总比例">
          {{obj.esopStockPercent}}
        </el-descriptions-item>
        <el-descriptions-item label="实施时持股人数">
          {{obj.esopStockPeoples}}
        </el-descriptions-item>
        <el-descriptions-item label="实施时持股人数占比">
          {{obj.esopStockProportion}}
        </el-descriptions-item>
        <el-descriptions-item label="目前持股人数">
          {{obj.esopStockPeoplesNow}}
        </el-descriptions-item>
        <el-descriptions-item label="目前持股人数占比">
          {{obj.esopStockProportionNow}}
        </el-descriptions-item>
      </el-descriptions>
    </SmallCard>
    <SmallCard style="margin-bottom: 14px;" :title="'员工持股上一年度财务状况/万元'">
      <el-descriptions direction="vertical" border :column="4">
        <el-descriptions-item label="资产">
          {{obj.esopLastAssets}}
        </el-descriptions-item>
        <el-descriptions-item label="净资产">
          {{obj.esopLastNetassets}}
        </el-descriptions-item>
        <el-descriptions-item label="收入">
          {{obj.esopLastIncome}}
        </el-descriptions-item>
        <el-descriptions-item label="净利润">
          {{obj.esopLastNetprofit}}
        </el-descriptions-item>
      </el-descriptions>
    </SmallCard>
    <SmallCard style="margin-bottom: 14px;" :title="'员工持股当年财务状况/万元'">
      <el-descriptions direction="vertical" border :column="4">
        <el-descriptions-item label="资产">
          {{obj.esopThisAssets}}
        </el-descriptions-item>
        <el-descriptions-item label="净资产">
          {{obj.esopThisNetassets}}
        </el-descriptions-item>
        <el-descriptions-item label="收入">
          {{obj.esopThisIncome}}
        </el-descriptions-item>
        <el-descriptions-item label="净利润">
          {{obj.esopThisNetprofit}}
        </el-descriptions-item>
      </el-descriptions>
    </SmallCard>
    <SmallCard style="margin-bottom: 14px;" :title="'员工持股第二年财务状况/万元'">
      <el-descriptions direction="vertical" border :column="4">
        <el-descriptions-item label="资产">
          {{obj.esopNextAssets}}
        </el-descriptions-item>
        <el-descriptions-item label="净资产">
          {{obj.esopNextNetassets}}
        </el-descriptions-item>
        <el-descriptions-item label="收入">
          {{obj.esopNextIncome}}
        </el-descriptions-item>
        <el-descriptions-item label="净利润">
          {{obj.esopNextNetprofit}}
        </el-descriptions-item>
      </el-descriptions>
    </SmallCard>
    <SmallCard style="" :title="'2022年公司财务状况/万元'">
      <el-descriptions direction="vertical" border :column="4">
        <el-descriptions-item label="资产">
          {{obj.propertygs}}
        </el-descriptions-item>
        <el-descriptions-item label="净资产">
          {{obj.netassetsgs}}
        </el-descriptions-item>
        <el-descriptions-item label="收入">
          {{obj.incomegs}}
        </el-descriptions-item>
        <el-descriptions-item label="净利润">
          {{obj.netprofitgs}}
        </el-descriptions-item>
      </el-descriptions>
    </SmallCard>
  </div>
</template>

<script>
  import CardBox from '@/views/common/CardBox'
  import SmallCard from '@/views/common/SmallCard'
  // import { getDetailsShareholdingCompanies  } from '@/api/sam/assetFx'
  export default {
    name: 'CompanyDetail',
    components: {
      CardBox,
      SmallCard
    },
    props: ['year','zcciCompanyId','esopId'],
    data() {
      return {
        obj:{},
        zcliCertificateCode: '',
        zcliAsssetsmType: '',
        zcliUseDescribe: '',
        zcliAddress: '',
        zcliBookValue: '',
        zcliNetbookValue: '',
        zcliArea: '',
        zcliStateZy: '',
        zcliStateCz: '',
        zcliStateXz: '',
      }
    },
    watch: {
      esopId: {
        handler: function (val) {
          this.getInfoData()
        },
        immediate: true,
      },
    },
    mounted() {
      // this.getInfoData()
    },
    methods: {
      async getInfoData() {
        let params = {
          year: 2023,
          zcciCompanyId: this.zcciCompanyId,
          esopId: this.esopId,
        }
        // let { data } = await getDetailsShareholdingCompanies(params)
        // this.obj = data
      },
      handleCloseCompanyInfo() {
        this.$emit('closeCompanyInfo')
      },
    },
  }
</script>

<style lang="scss" scoped>
  /* :deep()
    .el-descriptions-item__cell
    .el-descriptions-item__label
    .is-bordered-label {
    width: 144px;
    color: rgba(48, 49, 51, 1);
    font-size: 16px;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
  }

  :deep() .el-descriptions-row {
    background-color: rgba(245, 247, 250, 1);
    height: 56px;
    border: 1px solid rgba(235, 238, 245, 1);
    margin: 24px 0 0 24px;
  }
  :deep() .el-descriptions-item__cell .el-descriptions-item__content {
    background-color: rgba(255, 255, 255, 1);
    height: 56px;
    border: 1px solid rgba(235, 238, 245, 1);
    color: rgba(48, 49, 51, 1);
    font-size: 16px;
  } */
  .company {
    display: flex;
    height: 248px;
    .left {
      width: 20%;
      margin-right: 24px;
      img {
        width: 100%;
        height: 225px;
      }
    }
    .right {
      width: 80%;
    }
  }
  .closePopBtn {
    cursor: pointer;
  }
</style>
