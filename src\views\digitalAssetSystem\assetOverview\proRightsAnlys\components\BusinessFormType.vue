<template>
  <div class="businessFormType-container">
    <VabChartPie v-show="radio == '图表'" :option="option" :title="'企业形成方式'"/>
    <el-table
      border
      v-show="radio == '表格'"
      :data="tableDataGf"
      max-height="210px"
      style="margin-top: 20px"
    >
      <el-table-column
        align="center"
        label="形成方式"
        min-width="35%"
        prop="zcciFormatWay"
      />
      <el-table-column
        align="center"
        label="户数"
        min-width="35%"
        prop="zcciFormatWayCount"
      />
      <el-table-column
        align="center"
        label="占比"
        min-width="30%"
        prop="zcciFormatWayPer"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.zcciFormatWayPer
                ? scope.row.zcciFormatWayPer + '%'
                : '—'
            }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { searchFormatWay } from '@api/sam/property.js'
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  export default {
    name: "BusinessFormType",
    props: ['radio'],
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {},
        tableDataGf: []
      }
    },
    created() {
      this.getsearchFormatWay()
      this.$baseEventBus.$on('echartClick',(params,title) => {
        if(title == '企业形成方式') {
          this.$router.push({
            name: 'AssetCaAssayDetail',
            query: {
              data: JSON.stringify(this.tableDataGf),
              title,
              currentName: params.name,
              tableIndex: params.dataIndex
            }
          })
        }
      })
    },
    methods: {
      async getsearchFormatWay() {
        // let { data } = await searchFormatWay()
        // this.tableDataGf = data
        // const echartData = []
        // data.forEach(item => {
        //   echartData.push({
        //     name: item.zcciFormatWay,
        //     value: item.zcciFormatWayCount,
        //     data: item.zcciFormatWayPer
        //   })
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'item'
        //   },
        //   legend: {
        //     left: '45%',
        //     top: '25%',
        //     orient: 'vertical',
        //     icon:'circle',
        //     textStyle: {
        //       color:'#303133',
        //       fontSize: 16
        //     },
        //     formatter: (name) => {
        //       let str = ''
        //       for (let i = 0; i < echartData.length; i++) {
        //         if (echartData[i].name == name) {
        //             str = `${echartData[i].name} \t ${echartData[i].value}户  |  ${echartData[i].data}%`
        //         }
        //       }
        //       return str
        //     },
        //   },
        //   grid: {
        //     top:'1%',
        //     containLabel: true
        //   },
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)','rgba(93,112,146,0.85)'],
        //   series: [
        //     {
        //       name: '房屋使用情况',
        //       type: 'pie',
        //       radius: ['50%', '70%'],
        //       center: ['25%','40%'],
        //       avoidLabelOverlap: false,
        //       label: {
        //         show: false,
        //         position: 'center'
        //       },
        //       emphasis: {
        //         label: {
        //           show: true,
        //           fontSize: 20,
        //           fontWeight: 'bold'
        //         }
        //       },
        //       labelLine: {
        //         show: false
        //       },
        //       data: echartData
        //     }
        //   ]
        // }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .businessFormType-container {
    width: 100%;
    height: 100%;
  }
  :deep() {
    .echarts {
      width: 100%;
      height: 300px;
    }
  }
</style>