<!--添加新分组弹窗-->
<template>
  <DialogCard :dialogTableVisible="dialogVisible" title="土地" :close="handleCancel" :flag="true" width="1700px" height="700px" top="5vh">
    <div slot="content" class="add-group-content">

      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="单位名称:">
              <el-input v-model="searchForm.name" placeholder="请输入企业名称" class="inputW" />
            </el-form-item>
            <el-form-item label="统一社会信用代码:">
              <el-input v-model="searchForm.tyxydm" placeholder="请输入统一社会信用代码" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
          </el-form-item>
        </div>
      </el-form>

      <el-table :data="tableData" style="width: 100%;margin-bottom: 50px;" height="500px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="ZCLI_YEAR" label="年份" width="80" show-overflow-tooltip align="center" label-class-name="zcliYear" />
        <el-table-column prop="ZCLI_MJ" label="密级" width="80" show-overflow-tooltip align="center" label-class-name="zcliMj" />
        <el-table-column prop="COMPANY_TNAME" label="二级单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyTname" />
        <el-table-column prop="COMPANY_NAME" label="本单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyName" />
        <el-table-column prop="ZCLI_CREDIT_CODE" label="本单位统一社会信用代码" width="180" show-overflow-tooltip align="center" label-class-name="zcliCreditCode" />
        <el-table-column prop="ZCLI_ASSETS_NO" label="资产编号" width="150" show-overflow-tooltip align="center" label-class-name="zcliAssetsNo" />
        <!-- <el-table-column prop="ZCLI_ASSET_TYPE" label="资产类型" width="150" show-overflow-tooltip align="center" label-class-name="zcliAsssetsmType" /> -->
        <el-table-column prop="ZCLI_CERTIFICATE_CODE" label="土地权属证明编号" width="150" show-overflow-tooltip align="center" label-class-name="zcliCertificateCode" />
        <el-table-column prop="ZCLI_IF_EXIST" label="是否取得土地权属证明" width="180" show-overflow-tooltip align="center" label-class-name="zcliIfExist" />
        <!-- <el-table-column prop="ZCLI_NOCERTIFICATE_REASON" label="未取得土地权属证明原因" width="180" show-overflow-tooltip align="center" label-class-name="zcliNocertificateReason" /> -->
        <el-table-column prop="ZCLI_USE_DESCRIBE" label="现状用途描述" width="150" show-overflow-tooltip align="center" label-class-name="zcliUseDescribe" />
        <el-table-column prop="ZCLI_REQUIREMENTS" label="地上建筑是否满足批复或者备案功能要求" width="300" show-overflow-tooltip align="center" label-class-name="zcliRequirements" />
        <el-table-column prop="ZCLI_STRATEGY_DESCRIBE" label="是否有战略安排" width="150" show-overflow-tooltip align="center" label-class-name="zcliStrategyDescribe" />
        <el-table-column prop="ZCLI_RANGE" label="境内境外" width="150" show-overflow-tooltip align="center" label-class-name="zcliRange" />
        <el-table-column prop="ZCLI_COUNTRY" label="国家或地区" width="150" show-overflow-tooltip align="center" label-class-name="zcliCountry" />
        <el-table-column prop="ZCLI_PROVINCE" label="坐落位置（省）" width="150" show-overflow-tooltip align="center" label-class-name="zcliProvince" />
        <el-table-column prop="ZCLI_CITY" label="坐落位置（市）" width="150" show-overflow-tooltip align="center" label-class-name="zcliCity" />
        <!-- <el-table-column prop="ZCLI_COUNTY" label="坐落位置（区）" width="150" show-overflow-tooltip align="center" label-class-name="zcliCounty" /> -->
        <el-table-column prop="ZCLI_ADDRESS" label="具体土地位置" width="150" show-overflow-tooltip align="center" label-class-name="zcliAddress" />
        <el-table-column prop="ZCLI_IF_CONSTRUCTION" label="是否在建" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfConstruction" />
        <el-table-column prop="ZCLI_IF_STAGNATION" label="是否延期或停滞" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfStagnation" />
        <!-- <el-table-column prop="ZCLI_REASON_STAGNATION" label="延期/停滞原因" width="150" show-overflow-tooltip align="center" label-class-name="zcliReasonStagnation" /> -->
        <el-table-column prop="ZCLI_USEFUL" label="土地性质" width="150" show-overflow-tooltip align="center" label-class-name="zcliUseful" />
        <el-table-column prop="ZCLI_TYPE" label="取得方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliType" />
        <el-table-column prop="ZCLI_OBTAIN_DATE" label="取得时间" width="150" show-overflow-tooltip align="center" label-class-name="zcliObtainDate" />
        <el-table-column prop="ZCLI_SERVICE_LIFE" label="土地使用年限（年）" width="150" show-overflow-tooltip align="center" label-class-name="zcliServiceLife" />
        <el-table-column prop="ZCLI_PLOT_RATIO" label="当前容积率" width="150" show-overflow-tooltip align="center" label-class-name="zcliPlotRatio" />
        <el-table-column prop="ZCLI_APPROVAL_RATIO" label="批复容积率" width="150" show-overflow-tooltip align="center" label-class-name="zcliApprovalRatio" />
        <el-table-column prop="ZCLI_BOOK_VALUE" label="原值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zcliBookValue" />
        <el-table-column prop="ZCLI_NETBOOK_VALUE" label="净值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zcliNetbookValue" />
        <el-table-column prop="ZCLI_IF_ASSETS" label="是否两非资产" width="150" align="center" label-class-name="zcliIfAssets" />
        <el-table-column prop="ZCLI_DEPRECIABLE_YEAR" label="折旧年限" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepreciableYear" />
        <el-table-column prop="ZCLI_TOTAL_DEPRECIATION" label="本年计提折旧总额(万元)" width="180" align="center" label-class-name="zcliTotalDepreciation" />
        <el-table-column prop="ZCLI_EVALUATE_DATE" label="最近评估日期" width="150" show-overflow-tooltip align="center" label-class-name="zcliEvaluateDate" />
        <el-table-column prop="ZCLI_EVALUATE_VALUE" label="最近评估价值(万元)" width="150" align="center" label-class-name="zcliEvaluateValue" />
        <el-table-column prop="ZCLI_AREA" label="土地总面积（亩）" width="150" align="center" label-class-name="zcliArea" />
        <el-table-column prop="ZCLI_STATE_ZY" label="其中：自用面积(亩)" width="150" align="center" label-class-name="zcliStateZy" />
        <el-table-column prop="ZCLI_STATE_CZ" label="其中：出租面积(亩)" width="150" align="center" label-class-name="zcliStateCz" />
        <el-table-column prop="ZCLI_STATE_XZ" label="其中：闲置面积(亩)" width="150" align="center" label-class-name="zcliStateXz" />
        <el-table-column prop="ZCLI_IDLE_START_TIME" label="闲置起始时间" width="150" show-overflow-tooltip align="center" label-class-name="zcliIdleStartTime" />
        <el-table-column prop="ZCLI_IDLE_TIME" label="空置时间" width="150" show-overflow-tooltip align="center" label-class-name="zcliIdleTime" />
        <el-table-column prop="ZCLI_REASON_IDLENESS" label="闲置原因" width="150" show-overflow-tooltip align="center" label-class-name="zcliReasonIdleness" />
        <el-table-column prop="ZCLI_METHODS" label="建议盘活/处置方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliMethods" />
        <el-table-column prop="ZCLI_RENTAL_INCOME_LASTYEAR" label="上年租金收入（万元）" width="180" align="center" label-class-name="zcliRentalIncomeLastyear" />
        <el-table-column prop="ZCLI_RENTAL_INCOME_THISYEAR" label="预计本年租金收入（万元）" width="220" align="center" label-class-name="zcliRentalIncomeThisyear" />
        <el-table-column prop="ZCLI_CORRESPONDING_INCOME" label="土地面积对应本年营业收入（万元）" width="250" align="center" label-class-name="zcliCorrespondingIncome" />
        <el-table-column prop="ZCLI_SURROUNDING_SALE_PRICE" label="上年周边可比土地出售单价（元/平方米/月）" width="300" align="center" label-class-name="zcliSurroundingSalePrice" />
        <el-table-column prop="ZCLI_SURROUNDING_RENT_PRICE" label="上年周边可比土地出租单价（元/平方米/月）" width="300" align="center" label-class-name="zcliSurroundingRentPrice" />
        <el-table-column prop="ZCLI_BUSINESS_DZZB" label="电子装备（亩）" width="150" align="center" label-class-name="zcliBusinessDzzb" />
        <el-table-column prop="ZCLI_BUSINESS_WXTX" label="网信体系（亩）" width="150" align="center" label-class-name="zcliBusinessWxtx" />
        <el-table-column prop="ZCLI_BUSINESS_CYJC" label="产业基础（亩）" width="150" align="center" label-class-name="zcliBusinessCyjc" />
        <el-table-column prop="ZCLI_BUSINESS_WLAQ" label="网络安全（亩）" width="150" align="center" label-class-name="zcliBusinessWlaq" />
        <el-table-column prop="ZCLI_BUSINESS_OTHER" label="其他（亩）" width="150" align="center" label-class-name="zcliBusinessOther" />
        <el-table-column prop="ZCLI_IF_DISPUTE" label="是否存在纠纷" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfDispute" />
        <el-table-column prop="ZCLI_IF_MORTGAGE" label="土地是否已抵押" width="150" show-overflow-tooltip align="center" label-class-name="zcliIfMortgage" />
        <el-table-column prop="ZCLI_MORTGAGE" label="其中:已抵押面积（亩）" width="180" align="center" label-class-name="zcliMortgage" />
        <el-table-column prop="ZCLI_IF_DISPOSE" label="是否可处置" width="150" align="center" label-class-name="zcliIfDispose" />
        <el-table-column prop="ZCLI_REASON_DISPOSAL" label="不可处置原因" width="150" show-overflow-tooltip align="center" label-class-name="zcliReasonDisposal" />
        <el-table-column prop="ZCLI_DEPT_NAME" label="业务主管部门名称" width="150" show-overflow-tooltip align="center" label-class-name="zcliDeptName" />
        <el-table-column prop="ZCLI_OPERATOR" label="经办人" width="150" show-overflow-tooltip align="center" label-class-name="zcliOperator" />
        <el-table-column prop="ZCLI_OPERATOR_TEL" label="经办人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliOperatorTel" />
        <el-table-column prop="ZCLI_DEPARTMENT_LEADER" label="部门负责人" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepartmentLeader" />
        <el-table-column prop="ZCLI_DEPARTMENT_TEL" label="部门负责人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zcliDepartmentTel" />
        <el-table-column prop="ZCLI_COMPANY_LEADER" label="分管所(公司)领导" width="150" show-overflow-tooltip align="center" label-class-name="zcliCompanyLeader" />
        <el-table-column prop="ZCLI_COMPANY_TEL" label="分管所(公司)领导联系方式" width="220" show-overflow-tooltip align="center" label-class-name="zcliCompanyTel" />
        <el-table-column prop="ZCLI_REMARK" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zcliRemark" />
        <el-table-column prop="ZCLI_LONGITUDE" label="经度" width="150" show-overflow-tooltip align="center" label-class-name="zcliLongitude" />
        <el-table-column prop="ZCLI_LATITUDE" label="纬度" width="150" show-overflow-tooltip align="center" label-class-name="zcliLatitude" />
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />

      <LandDetailDialog ref="landDetail" />
    </div>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard.vue'
import { getIndicatorValueWithParamsPage } from 'api/digitalAssetSystem/assetOverview'
import LandDetailDialog from '@/views/digitalAssetSystem/fileManagement/physicalAsset/land/components/LandDetailDialog.vue'

export default {
  components: { DialogCard, LandDetailDialog },

  data () {
    return {
      dialogVisible: false,
      tableData: [],
      indicatorCode: '',
      zcbiId: '',
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        tyxydm: ''
      },
      total: 0
    }
  },

  methods: {

    handleDetail (row) {
      row.zcliId = row.ZCLI_ID
      this.$refs.landDetail.showDialog(row)
    },

    handleShow (indicatorCode, zcbiId) {
      this.dialogVisible = true
      this.indicatorCode = indicatorCode
      this.zcbiId = zcbiId
      this.getList()
    },

    getList () {
      getIndicatorValueWithParamsPage({
        indicatorCode: "LAND_DYNAMIC_QUERY",
        pageNo: this.searchForm.pageNo,
        pageSize: this.searchForm.pageSize,
        params: {
          year: '2025',
          zcbiId: this.zcbiId,
          companyName: this.searchForm.name,
          zcliCreditCode: this.searchForm.tyxydm,
          ...this.indicatorCode,
        },
        // additionalConditions: {
        //   ...this.indicatorCode,
        //   companyName: this.searchForm.name ? `AND COMPANY_NAME LIKE '%${this.searchForm.name}%'` : '',
        //   zcliCreditCode: this.searchForm.tyxydm ? `AND zcli_credit_code LIKE '%${this.searchForm.tyxydm}%'` : ''
        // }
      }).then(res => {
        console.log("🚀🚀 ~ 土地-二级页面 ~ 🚀🚀", res.data.list)
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.getList()
    },

    handleCancel () {
      this.dialogVisible = false
      this.tableData = []
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.getList()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 100%;
}

.leftRight {
  display: flex;
  // justify-content: space-between;
}
</style>
