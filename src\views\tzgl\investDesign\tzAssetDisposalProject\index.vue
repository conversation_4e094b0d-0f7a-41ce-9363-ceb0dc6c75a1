<template>
  <CardBox>
    <tzAssetDisposalProjectSearch
      slot="leftCont"
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzAssetDisposalProjectTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"/>

    <el-table
      ref="tzAssetDisposalProjectTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzAssetDisposalProject"
      row-key="tadpId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="55"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
       <template slot="header" slot-scope="scope" >
            <template v-if="item.label =='是否纳入储备库' ">
               <el-tooltip content="是否纳入三年滚动计划" placement="top">
              <span>{{item.label}} <span class="isIcon">?</span></span>
            </el-tooltip>
            </template>
            <template  v-else >
            {{ item.label }}
          </template>
         </template>
         
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="100"
        fixed="right"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" v-if="row.tadpIfReserve == '否'" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" v-if="row.tadpIfReserve == '否'" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="70%"
      height="530px"
    >
      <tzAssetDisposalProjectForm
        ref="tzAssetDisposalProjectForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"
        :tadpCzCompanyNameData="tadpCzCompanyNameData"
      />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard>

  </CardBox>
</template>

<script>
  import { 
    tzAssetDisposalProjectDoDeleteELog,
    tzAssetDisposalProjectGetList,
    tzAssetDisposalProjectDoSaveOrUpdLog,
  } from '@/api/tzgl/investDesign/tzAssetDisposalProject'
  import { searchSecoundCompanyInfo } from 'api/common'
  import tzAssetDisposalProjectSearch from './components/Search.vue'
  import tzAssetDisposalProjectForm from './components/Form.vue'
  import { mapGetters } from 'vuex'
  import CardBox from 'common/CardBox'
  import DialogCard from 'common/DialogCard'

  export default {
    name: 'tzAssetDisposalProject',
    components: {
      CardBox,
      DialogCard,
      tzAssetDisposalProjectSearch,
      tzAssetDisposalProjectForm,
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tadpAddress: [
            { required: true, message: '请输入拟处置资产所在地区', trigger: 'blur' }
          ],
          tadpBookValue: [
            { required: true, message: '请输入资产账面原值', trigger: 'blur' }
          ],
          tadpCompanyId: [
            { required: true, message: '请输入二级成员单位id', trigger: 'blur' }
          ],
          tadpCompanyName: [
            { required: true, message: '请输入二级成员单位', trigger: 'blur' }
          ],
          tadpContacts: [
            { required: true, message: '请输入联络人', trigger: 'blur' }
          ],
          tadpContactsWay: [
            { required: true, message: '请输入联络人联系方式', trigger: 'blur' }
          ],
          tadpCzCompanyId: [
            { required: true, message: '请输入资产权属单位名称id', trigger: 'blur' }
          ],
          tadpCzCompanyName: [
            { required: true, message: '请输入资产权属单位名称', trigger: 'blur' }
          ],
          tadpDesc: [
            { required: true, message: '请输入备注', trigger: 'blur' }
          ],
          tadpIsplan: [
            { required: true, message: '请输入是否计划内', trigger: 'blur' }
          ],
          tadpName: [
            { required: true, message: '请输入拟处置资产名称', trigger: 'blur' }
          ],
          tadpNetValue: [
            { required: true, message: '请输入资产账面净值', trigger: 'blur' }
          ],
          tadpNums: [
            { required: true, message: '请输入拟处置资产数量', trigger: 'blur' }
          ],
          tadpProfit: [
            { required: true, message: '请输入资产处置完成预计形成利润', trigger: 'blur' }
          ],
          tadpRecoveryAmount: [
            { required: true, message: '请输入资产处置完成预计回收资金', trigger: 'blur' }
          ],
          tadpSerial: [
            { required: true, message: '请输入资产编号', trigger: 'blur' }
          ],
          tadpSituation: [
            { required: true, message: '请输入拟处置资产情况', trigger: 'blur' }
          ],
          tadpSort: [
            { required: true, message: '请输入资产种类', trigger: 'blur' }
          ],
          tadpTransferWay: [
            { required: true, message: '请输入转让方式', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        dialogVisible: false,
        isFullscreen: false,
        height: this.$baseTableHeight(1,1) - 72,
        checkList: ['是否计划内','二级成员单位','是否纳入储备库','资产权属单位名称','拟处置资产名称','资产编号','资产种类','拟处置资产情况','拟处置资产所在地区','拟处置资产数量','资产账面原值','资产账面净值','转让方式','资产处置完成预计回收资金','资产处置完成预计形成利润','联络人','联络人联系方式','备注'],
        columns: [
          { prop:'tadpIsplan', label:'是否计划内', width:'150' , sortable:false  },
          { prop:'tadpCompanyName', label:'二级成员单位', width:'250' , sortable:false  },
          { prop:'tadpIfReserve', label:'是否纳入储备库', width:'180', sortable:false },
          { prop:'tadpCzCompanyName', label:'资产权属单位名称', width:'250' , sortable:false  },
          { prop:'tadpName', label:'拟处置资产名称', width:'200' , sortable:false  },
          { prop:'tadpSerial', label:'资产编号', width:'200' , sortable:false  },
          { prop:'tadpSort', label:'资产种类', width:'200' , sortable:false  },
          { prop:'tadpSituation', label:'拟处置资产情况', width:'200' , sortable:false  },
          { prop:'tadpAddress', label:'拟处置资产所在地区', width:'200' , sortable:false  },
          { prop:'tadpNums', label:'拟处置资产数量', width:'200' , sortable:false  },
          { prop:'tadpBookValue', label:'资产账面原值', width:'200' , sortable:false  },
          { prop:'tadpNetValue', label:'资产账面净值', width:'200' , sortable:false  },
          { prop:'tadpTransferWay', label:'转让方式', width:'200' , sortable:false  },
          { prop:'tadpRecoveryAmount', label:'资产处置完成预计回收资金', width:'200' , sortable:false  },
          { prop:'tadpProfit', label:'资产处置完成预计形成利润', width:'200' , sortable:false  },
          { prop:'tadpContacts', label:'联络人', width:'200' , sortable:false  },
          { prop:'tadpContactsWay', label:'联络人联系方式', width:'200' , sortable:false  },
          { prop:'tadpDesc', label:'备注', width:'200' , sortable:false  },
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        tadpCzCompanyNameData: [],
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.handleSearchCompany()
      this.fetchData()
    },
    methods: {
      async handleSearchCompany() {
        const { data: {
          list
        }} = await searchSecoundCompanyInfo({
          odPathid: this.loginUser.zcbiId
        })
        this.tadpCzCompanyNameData = list
      },
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzAssetDisposalProjectForm.$refs.form.validate(async (valid) => {
          if (valid) {
             const regex = /^[^-]+-[^-]+(-[^-]+)?$/;
            let isValid= regex.test(this.form.tadpAddress);
            if(!isValid){
              this.$message({
              type: 'warning',
              message: '地址格式不正确，请使用"省-市-区"或"直辖市-区"格式',
            })
            return false
            }
            const  msg  = await tzAssetDisposalProjectDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      closeDialog() {
        this.dialogVisible = false
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.title = '添加'
        this.$set(this.form, 'tadpCompanyName', this.loginUser.zcbiName)
        this.$set(this.form, 'tadpCompanyId', this.loginUser.zcbiId)
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tadpId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzAssetDisposalProjectDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        this.searchForm.tadpCompanyId= this.loginUser.zcbiId
        const {
          data: { list, total },
        } = await tzAssetDisposalProjectGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>

<style lang="scss" scoped>
.isIcon{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #aeafb0;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  cursor: help;
  margin-left: 4px;
  user-select: none;
}
</style>