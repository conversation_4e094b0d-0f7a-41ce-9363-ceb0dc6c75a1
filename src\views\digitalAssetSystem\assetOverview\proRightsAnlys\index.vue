<template>
  <div class="assetCqAssay-container">
    <div class="topStatBox">
      <TopStatBox />
    </div>

    <div class="secondBox">
      <el-row class="" :gutter="24">
        <el-col :span="7">
          <CardBox :title="'企业类别分析'">
            <BusinessClass />
          </CardBox>
        </el-col>
        <el-col :span="10">
          <CardBox :title="'企业分布情况'">
            <BusinessFbDetail />
          </CardBox>
        </el-col>
        <el-col :span="7">
          <CardBox :title="'各省户数排行'">
            <ProvincePm :year="year"/>
          </CardBox>
        </el-col>
      </el-row>
    </div>

    <div class="thirdBox">
      <el-row :gutter="24">
        <el-col :span="12">
          <CardBox :title="'管理级次分布'">
            <el-radio-group slot="rightTitle" v-model="radioGljc">
              <el-radio-button label="图表"></el-radio-button>
              <el-radio-button label="表格"></el-radio-button>
            </el-radio-group>
            <ManageLevel :radio="radioGljc"/>
          </CardBox>
        </el-col>
        <el-col :span="12">
          <CardBox :title="'产权级次分布'">
            <el-radio-group slot="rightTitle" v-model="radioCqjc">
              <el-radio-button label="图表"></el-radio-button>
              <el-radio-button label="表格"></el-radio-button>
            </el-radio-group>
            <OwnershipLevel :radio="radioCqjc"/>
          </CardBox>
        </el-col>
      </el-row>
    </div>

    <div class="fourthBox">
      <el-row :gutter="24">
        <el-col :span="12">
          <CardBox :title="'行业户数排名'">
            <IndustryRank />
          </CardBox>
        </el-col>
        <el-col :span="12">
          <!-- 持股比例情况 -->
          <div class="shareholdDetail">
            <CardBox :title="'持股比例情况'">
              <el-radio-group slot="rightTitle" v-model="radioCgbl">
                <el-radio-button label="图表"></el-radio-button>
                <el-radio-button label="表格"></el-radio-button>
              </el-radio-group>
              <ShareholdDetail :radio="radioCgbl"/>
            </CardBox>
          </div>
          <!-- 企业形成方式 -->
          <div class="businessFormType">
            <CardBox :title="'企业形成方式'">
              <el-radio-group slot="rightTitle" v-model="radioXcfs">
                <el-radio-button label="图表"></el-radio-button>
                <el-radio-button label="表格"></el-radio-button>
              </el-radio-group>
              <BusinessFormType :radio="radioXcfs"/>
            </CardBox>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  import CardBox from '@/views/common/CardBox'
  import TopStatBox from './components/TopStatBox'
  import BusinessClass from './components/BusinessClass'
  import BusinessFbDetail from './components/BusinessFbDetail'
  import ProvincePm from './components/ProvincePm'
  import ManageLevel from './components/ManageLevel'
  import OwnershipLevel from './components/OwnershipLevel'
  import IndustryRank from './components/IndustryRank'
  import ShareholdDetail from './components/ShareholdDetail'
  import BusinessFormType from './components/BusinessFormType'
  export default {
    name: "AssetCqAssay",
    components: {
      CardBox,
      TopStatBox,
      BusinessClass,
      BusinessFbDetail,
      ProvincePm,
      ManageLevel,
      OwnershipLevel,
      IndustryRank,
      ShareholdDetail,
      BusinessFormType
    },
    data() {
      return {
        radioGljc: '图表',
        radioCqjc: '图表',
        radioCgbl: '图表',
        radioXcfs: '图表',
        year: new Date().getFullYear()
      }
    },
    methods: {

    },
  }
</script>

<style lang="scss" scoped>
  .assetCqAssay-container {
    width: 100%;
    height: 100%;
    background: #F6F8F9!important;
    padding: 0px!important;
    .topStatBox {
      height: 120px;
    }

    .secondBox {
      margin-top: 24px;
      .el-row,.el-col {
        height: 100%!important;
      } 
    }
    .thirdBox {
      margin-top: 24px;
      .el-row,.el-col {
        height: 100%!important;
      } 
    }
    .fourthBox {
      height: 832px;
      margin-top: 24px;
      .el-row,.el-col {
        height: 100%!important;
      } 
    }

    .shareholdDetail {
      height: 452px;
    }

    .businessFormType {
      height: 357px;
      margin-top: 24px;
    }
  }
</style>