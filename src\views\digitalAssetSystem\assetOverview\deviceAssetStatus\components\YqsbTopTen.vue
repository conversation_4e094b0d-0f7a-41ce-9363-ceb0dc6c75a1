<template>
  <div class="container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="总数量" name="houseZR">
        <VabChartPie :option="option" :title="'总数量'" v-if="cityData.length > 0"/>
        <div class="emptyImg" v-else>
          <img src="@/assets/empty_images/data_empty.png" alt="" width="260">
        </div>
      </el-tab-pane>
      <el-tab-pane label="损毁及待报废" name="houseBF">
        <VabChartPie :option="optionBf" :title="'损毁及待报废'"  v-if="numberBf.length > 0"/>
        <div class="emptyImg" v-else>
          <img src="@/assets/empty_images/data_empty.png" alt="" width="260">
        </div>
      </el-tab-pane>
      <el-tab-pane label="自用数量" name="houseZy">
        <VabChartPie :option="optionZy" :title="'自用数量'"  v-if="numberZy.length > 0"/>
        <div class="emptyImg" v-else>
          <img src="@/assets/empty_images/data_empty.png" alt="" width="260">
        </div>
      </el-tab-pane>
      <el-tab-pane label="出租数量" name="houseCZ">
        <VabChartPie :option="optionCz" :title="'出租数量'"  v-if="numberCz.length > 0"/>
        <div class="emptyImg" v-else>
          <img src="@/assets/empty_images/data_empty.png" alt="" width="260">
        </div>
      </el-tab-pane>
      <el-tab-pane label="闲置数量" name="houseXz">
        <VabChartPie :option="optionXz" :title="'闲置数量'"  v-if="numberXz.length > 0"/>
        <div class="emptyImg" v-else>
          <img src="@/assets/empty_images/data_empty.png" alt="" width="260">
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import {
  //   provincialLevelDistribution,
  //   provincialStatus,
  // } from '@/api/sam/instrument'
  export default {
    name: '',
    components: {
      VabChartPie,
    },
    data() {
      return {
        activeName: 'houseZR',
        option: {},
        optionBf: {},
        optionZy: {},
        optionCz: {},
        optionXz: {},
        numberZy: [],
        numberXz: [],
        numberCz: [],
        numberBf: [],
        cityData: [],
        cityDataAll: []
      }
    },
    created() {
      this.getTopTenData()
      this.getOtherTopTenBf('损毁及待报废')
      this.getOtherTopTenCz('出租出借')
      this.getOtherTopTenZy('正在使用')
      this.getOtherTopTenXz('闲置可用')
      this.$baseEventBus.$on('yqsbTopClick', (params) => {
        if (params.title == '总数量') {
          // this.$router.push({
          //   path: '/views/cockpit/assetInstrument/components/InstrumentOverallT',
          //   query: {
          //     index: params.dataIndex,
          //     value: JSON.stringify(this.cityDataAll),
          //     seriesName: params.title,
          //     cityName: params.name,
          //   },
          // })
        }
        if (params.title == '损毁及待报废') {
          // this.$router.push({
          //   path: '/views/cockpit/assetInstrument/components/InstrumentOverallT',
          //   query: {
          //     value: JSON.stringify(this.numberBf),
          //     seriesName: params.title,
          //     cityName: params.name,
          //     index: params.dataIndex
          //   },
          // })
        }
        if (params.title == '自用数量') {
          // this.$router.push({
          //   path: '/views/cockpit/assetInstrument/components/InstrumentOverallT',
          //   query: {
          //     value: JSON.stringify(this.numberZy),
          //     seriesName: params.title,
          //     cityName: params.name,
          //     index: params.dataIndex
          //   },
          // })
        }
        if (params.title == '出租数量') {
          // this.$router.push({
          //   path: '/views/cockpit/assetInstrument/components/InstrumentOverallT',
          //   query: {
          //     value: JSON.stringify(this.numberCz),
          //     seriesName: params.title,
          //     cityName: params.name,
          //     index: params.dataIndex
          //   },
          // })
        }
        if (params.title == '闲置数量') {
          // this.$router.push({
          //   path: '/views/cockpit/assetInstrument/components/InstrumentOverallT',
          //   query: {
          //     value: JSON.stringify(this.numberXz),
          //     seriesName: params.title,
          //     cityName: params.name,
          //     index: params.dataIndex
          //   },
          // })
        }
      })
    },
    methods: {
      async getDataAll() {
        let params = {
          number : 10
        }
        // let { data } = await provincialLevelDistribution(params)
        // this.cityDataAll = data
      },
      async getTopTenData() {
        let params = {
          number: 10
        }
        let xData = []
        let yData1 = []
        let yData2 = []
        let yData3 = []
        let yData4 = []
        let yData5 = []
        // let { data } = await provincialLevelDistribution(params)
        // this.cityData = data
        // data.forEach((item) => {
        //   xData.push(item.zcaiCityName)
        //   yData1.push(item.total)
        //   yData2.push(item.totalBF)
        //   yData3.push(item.totalCZ)
        //   yData4.push(item.totalXZ)
        //   yData5.push(item.totalZY)
        // })

        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //     },
        //   },
        //   legend: {},
        //   grid: {
        //     left: '3%',
        //     right: '4%',
        //     bottom: '10%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       type: 'value',
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '损毁及待报废',
        //       type: 'bar',
        //       barWidth: 36,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData2,
        //     },
        //     {
        //       name: '自用数量',
        //       type: 'bar',
        //       barWidth: 36,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData3,
        //     },
        //     {
        //       name: '出租数量',
        //       type: 'bar',
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData4,
        //     },
        //     {
        //       name: '闲置数量',
        //       type: 'bar',
        //       barWidth: 10,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData5,
        //     },
        //   ],
        // }
      },
      async getOtherTopTenBf(name) {
        let parmas = {
          number: 10,
          name,
        }
        // let { data } = await provincialStatus(parmas)
        // this.numberBf = data
        // let xData = []
        // let yData = []
        // data.forEach((item) => {
        //   xData.push(item.zcaiCityName)
        //   yData.push(item.total)
        // })

        // this.optionBf = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //     },
        //   },
        //   legend: {},
        //   grid: {
        //     left: '3%',
        //     right: '4%',
        //     bottom: '10%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       type: 'value',
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '损毁及待报废',
        //       type: 'bar',
        //       barWidth: 20,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData,
        //     },
        //   ],
        // }
      },
      async getOtherTopTenCz(name) {
        let params = {
          number: 10,
          name,
        }
        // let { data } = await provincialStatus(params)
        // this.numberCz = data
        // let xData = []
        // let yData = []
        // data.forEach((item) => {
        //   xData.push(item.zcaiCityName)
        //   yData.push(item.total)
        // })

        // this.optionCz = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //     },
        //   },
        //   legend: {},
        //   grid: {
        //     left: '3%',
        //     right: '4%',
        //     bottom: '10%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       type: 'value',
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '出租',
        //       type: 'bar',
        //       barWidth: 20,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData,
        //     },
        //   ],
        // }
      },
      async getOtherTopTenZy(name) {
        let parmas = {
          number: 10,
          name,
        }
        // let { data } = await provincialStatus(parmas)
        // this.numberZy = data
        // let xData = []
        // let yData = []
        // data.forEach((item) => {
        //   xData.push(item.zcaiCityName)
        //   yData.push(item.total)
        // })

        // this.optionZy = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //     },
        //   },
        //   legend: {},
        //   grid: {
        //     left: '3%',
        //     right: '4%',
        //     bottom: '10%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       type: 'value',
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '自用',
        //       type: 'bar',
        //       barWidth: 20,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData,
        //     },
        //   ],
        // }
      },
      async getOtherTopTenXz(name) {
        let parmas = {
          number: 10,
          name,
        }
        // let { data } = await provincialStatus(parmas)
        // this.numberXz = data
        // let xData = []
        // let yData = []
        // data.forEach((item) => {
        //   xData.push(item.zcaiCityName)
        //   yData.push(item.total)
        // })

        // this.optionXz = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //     },
        //   },
        //   legend: {},
        //   grid: {
        //     left: '3%',
        //     right: '4%',
        //     bottom: '10%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       type: 'value',
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '闲置',
        //       type: 'bar',
        //       barWidth: 20,
        //       stack: 'Search Engine',
        //       emphasis: {
        //         focus: 'series',
        //       },
        //       data: yData,
        //     },
        //   ],
        // }
      },
    },
  }
</script>

<style lang="scss" scoped>
  :deep() {
    .echarts {
      width: 100%;
      height: 252px;
    }
    .emptyImg {
      height: 256px !important;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
