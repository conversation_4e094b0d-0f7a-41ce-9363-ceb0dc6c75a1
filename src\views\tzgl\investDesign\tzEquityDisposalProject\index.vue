<template>
  <CardBox>
    <tzEquityDisposalProjectSearch
      slot="leftCont"
      :checkList="checkList"
      :columns="columns"
      :queryForm="searchForm"
      ref="tzEquityDisposalProjectTs"
      @handleAdd="handleAdd"
      @handleSearch="handleSearch"
    />

    <el-table
      ref="tzEquityDisposalProjectTable"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      stripe
      @cell-dblclick="cellDblClick"
      id="TzEquityDisposalProject"
      row-key="tedpId"
      highlight-current-row 
      @current-change="currentSelectRow"
    >

      <el-table-column
        align="center"
        label="序号"
        show-overflow-tooltip
        width="55"
        :index="count"
        type=index
        label-class-name="number"
      />

      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        align="center"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable"
        :width="item.width"
        show-overflow-tooltip
         :label-class-name="item.prop"
      >
        <template slot="header" slot-scope="scope" >
            <template v-if="item.label =='是否纳入储备库' ">
               <el-tooltip content="是否纳入三年滚动计划" placement="top">
              <span>{{item.label}} <span class="isIcon">?</span></span>
            </el-tooltip>
            </template>
            <template  v-else >
            {{ item.label }}
          </template>
         </template>
        <template #default="{ row }">
          {{ row[item.prop] }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="操作"
        show-overflow-tooltip
        width="100"
        fixed="right"
        label-class-name="_lesoper"
      >
        <template #default="{ row }">
          <el-button type="text" v-if="row.tedpIfReserve == '否'"  @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" v-if="row.tedpIfReserve == '否'"  @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>

    </el-table>

    <el-pagination
      background
      class="el-pagination-a"
      :current-page="searchForm.pageNo"
      :layout="layout"
      :page-size="searchForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <DialogCard
      ref="dialogCard"
      :dialogTableVisible="dialogVisible"
      destroy-on-close
      :close="closeDialog"
      v-if="dialogVisible"
      :title="title"
      top="15vh"
      width="70%"
      height="530px"
    >
      <tzEquityDisposalProjectForm
        ref="tzEquityDisposalProjectForm"
        slot="content"
        :type="editType"
        :rules="rules"
        :form="form"
        :formConfig="formConfig"
        :tedpCzCompanyNameData="tedpCzCompanyNameData"
      />
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">保存</el-button>
      </template>
    </DialogCard>
  </CardBox>
</template>

<script>
  import { 
    tzEquityDisposalProjectDoDeleteELog,
    tzEquityDisposalProjectGetList,
    tzEquityDisposalProjectDoSaveOrUpdLog,
    tzEquityDisposalProjectDoExport 
  } from '@/api/tzgl/investDesign/tzEquityDisposalProject'
  import { searchSecoundCompanyInfo } from 'api/common'
  import CardBox from 'common/CardBox'
  import DialogCard from 'common/DialogCard'
  import tzEquityDisposalProjectSearch from './components/Search.vue'
  import tzEquityDisposalProjectForm from './components/Form.vue'
  import { mapGetters } from 'vuex'

  export default {
    name: 'tzEquityDisposalProject',
    components: {
      CardBox,
      DialogCard,
      tzEquityDisposalProjectSearch,
      tzEquityDisposalProjectForm,
    },
    data() {
      return {
        fullscreenLoading: false,
        editType:'',
        queryFormDf: {},
        queryForm: {},
        form: {},
        rules: {
          tedpAddress: [
            { required: true, message: '请输入项目地点', trigger: 'blur' }
          ],
          tedpAnalysis: [
            { required: true, message: '请输入必要性分析', trigger: 'blur' }
          ],
          tedpCompanyName: [
            { required: true, message: '请输入二级成员单位', trigger: 'blur' }
          ],
          tedpContacts: [
            { required: true, message: '请输入联系人', trigger: 'blur' }
          ],
          tedpContactsWay: [
            { required: true, message: '请输入联系方式', trigger: 'blur' }
          ],
          tedpContent: [
            { required: true, message: '请输入项目内容', trigger: 'blur' }
          ],
          tedpCzCompanyName: [
            { required: true, message: '请输入实施主体', trigger: 'blur' }
          ],
          tedpEffectAmount: [
            { required: true, message: '请输入处置对当年损益影响金额', trigger: 'blur' }
          ],
          // tedpIsplan: [
          //   { required: true, message: '请输入是否计划内投资', trigger: 'blur' }
          // ],
          tedpMode: [
            { required: true, message: '请输入调整方式', trigger: 'blur' }
          ],
          tedpName: [
            { required: true, message: '请输入项目名称', trigger: 'blur' }
          ],
          tedpProceeds: [
            { required: true, message: '请输入预计处置收益', trigger: 'blur' }
          ],
          tedpRecoveryFund: [
            { required: true, message: '请输入预计回收资金', trigger: 'blur' }
          ],
          tedpRecoveryTime: [
            { required: true, message: '请输入回收资金时间（X月）', trigger: 'blur' }
          ]
        },
        formConfig: {labelPosition: 'right',labelWidth: '80px',size: 'small'},
        title: '添加',
        dialogVisible: false,
        isFullscreen: false,
        height: this.$baseTableHeight(1,1) - 72,
        checkList: ['项目名称','二级成员单位','是否纳入储备库','实施主体','调整方式','项目内容','必要性分析','项目地点','预计回收资金','回收资金时间（X月）','预计处置收益','处置对当年损益影响金额','联系人','联系方式','备注'],//'是否计划内投资',
        columns: [
          // { prop:'tedpIsplan', label:'是否计划内投资', width:'200' , sortable:false  },
          { prop:'tedpName', label:'项目名称', width:'200' , sortable:false  },
          { prop:'tedpCompanyName', label:'二级成员单位', width:'200' , sortable:false  },
          { prop:'tedpIfReserve', label:'是否纳入储备库', width:'180' , sortable:false},
          { prop:'tedpCzCompanyName', label:'实施主体', width:'200' , sortable:false  },
          { prop:'tedpMode', label:'调整方式', width:'200' , sortable:false  },
          { prop:'tedpContent', label:'项目内容', width:'200' , sortable:false  },
          { prop:'tedpAnalysis', label:'必要性分析', width:'200' , sortable:false  },
          { prop:'tedpAddress', label:'项目地点', width:'200' , sortable:false  },
          { prop:'tedpRecoveryFund', label:'预计回收资金', width:'200' , sortable:false  },
          { prop:'tedpRecoveryTime', label:'回收资金时间（X月）', width:'200' , sortable:false  },
          { prop:'tedpProceeds', label:'预计处置收益', width:'200' , sortable:false  },
          { prop:'tedpEffectAmount', label:'处置对当年损益影响金额', width:'200' , sortable:false  },
          { prop:'tedpContacts', label:'联系人', width:'200' , sortable:false  },
          { prop:'tedpContactsWay', label:'联系方式', width:'200' , sortable:false  },
          { prop:'tedpDesc', label:'备注', width:'200' , sortable:false  },
        ],
        list: [],
        imageList: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        row: '',
        searchForm: {
          pageNo: 1,
          pageSize: 20,
          sortField:'',
          sortOrder:''
        },
        tedpCzCompanyNameData: []
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        loginUser: 'user/loginUser'
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.handleSearchCompany()
      this.fetchData()
    },
    methods: {
      async handleSearchCompany() {
        const { data: {
          list
        }} = await searchSecoundCompanyInfo({
          odPathid: this.loginUser.zcbiId
        })
        this.tedpCzCompanyNameData = list
      },
      count(index) {
        return (this.searchForm.pageNo - 1) * this.searchForm.pageSize + index + 1
      },
      // 弹窗保存确认按钮
      save() {
        this.$refs.tzEquityDisposalProjectForm.$refs.form.validate(async (valid) => {
          if (valid) {
            const regex = /^[^-]+-[^-]+(-[^-]+)?$/;
            let isValid= regex.test(this.form.tedpAddress);
            if(!isValid){
              this.$message({
              type: 'warning',
              message: '地址格式不正确，请使用"省-市-区"或"直辖市-区"格式',
            })
            return false
            }
            const  msg  = await tzEquityDisposalProjectDoSaveOrUpdLog( this.form )
            if(msg.code == 200) {
              this.$message({message:'保存操作成功!',type:'success'})
              this.fetchData()
              this.closeDialog()
            }else{
              this.$message({message:'保存操作失败!',type:'warning'})
            }
          }
        })
      },
      // 弹窗编辑取消按钮
      closeDialog() {
        this.dialogVisible = false
      },
      // 行点击切换事件
      currentSelectRow(val) {
        this.row = val
      },
      // 添加按钮事件
      handleAdd() {
        this.form={}
        this.editType = 'add'
        this.$set(this.form, 'tedpCompanyName', this.loginUser.zcbiName)
        this.$set(this.form, 'tedpCompanyId', this.loginUser.zcbiId)
        this.dialogVisible = true
      },
      // 双击行编辑事件
      cellDblClick(row) {
        this.handleEdit(row)
      },
      // 编辑行数据
      handleEdit(row) {
        this.row = row 
        this.editType = 'update'
        this.title = '编辑'
        this.form = Object.assign({},row)
        this.dialogVisible = true
      },
      // 删除行数据
      handleDelete(row) {
        if (row.tedpId) {
          this.$baseConfirm('确定删除吗', null, async () => {
            const msg = await tzEquityDisposalProjectDoDeleteELog(row)
            if(msg.code == 200) {
              this.$message({message:'删除操作成功!',type:'success'})
              await this.fetchData()
            }else{
              this.$message({message:'删除操作失败!',type:'warning'})
            }
          })
        }
      },
      // 分页每页条数改变
      handleSizeChange(val) {
        this.searchForm.pageSize = val
        this.fetchData()
      },
      // 分页当前页改变
      handleCurrentChange(val) {
        this.searchForm.pageNo = val
        this.fetchData()
      },
      // 快速查询
      handleSearch($event) {
        this.searchForm = $event
        this.searchForm.pageNo = 1
        this.fetchData()
      },
      // 获取表格数据
      async fetchData() {
        this.listLoading = true
        this.searchForm.tedpCompanyId= this.loginUser.zcbiId
        const {
          data: { list, total },
        } = await tzEquityDisposalProjectGetList(this.searchForm)
        this.list = list
        this.total = total
        this.listLoading = false
      },
    },
  }
</script>

<style lang="scss" scoped>
.isIcon{
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #aeafb0;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  cursor: help;
  margin-left: 4px;
  user-select: none;
}
</style>