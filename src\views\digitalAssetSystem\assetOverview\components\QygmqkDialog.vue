<!--添加新分组弹窗-->
<template>
  <DialogCard :dialogTableVisible="dialogVisible" :title="title" :close="handleCancel" :flag="true" width="1700px" height="700px" top="5vh">
    <div slot="content" class="add-group-content">

      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="企业名称:">
              <el-input v-model="searchForm.name" placeholder="请输入企业名称" class="inputW" />
            </el-form-item>
            <el-form-item label="统一社会信用代码:">
              <el-input v-model="searchForm.tyxydm" placeholder="请输入统一社会信用代码" class="inputW" />
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
          </el-form-item>
        </div>
      </el-form>

      <el-table :data="tableData" style="width: 100%;margin-bottom: 50px;" height="500px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="ZCBI_NAME" label="企业名称" width="250" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SHORT_NAME" label="企业简称" align="center" width="150" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ENGLISH_NAME" label="企业英文名称" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="TOTAL_ZCZE" label="资产总额" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="TOTAL_FZZE" label="负债总额" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="TOTAL_LRZE" label="利润总额" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_TYSHXYDM" label="统一社会信用代码" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZCD" label="注册地" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_FDDBR" label="法定代表人" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_JYZT" label="经营状态" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_LEVEL" label="企业管理级次" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_CLEVEL" label="企业产权级次" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_QYLB" label="企业类别" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZZXS" label="组织形式" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZYHY1" label="主要行业" align="center" width="250" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZYHY2" label="主要行业2" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZYHY3" label="主要行业3" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZCZB_XX" label="注册资本(万元)" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZCZB_BZ" label="币种" align="center" width="80" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZCRQ" label="注册日期" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SLZCRQ" label="设立注册日期" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_GZJGJG" label="国资监管机构" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_GJCZQY" label="国家出资企业" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_YGJCZQYGX" label="与国家出资企业关系" align="center" width="150" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFGJCZQYZY" label="是否国家出资企业主业" align="center" width="150" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZYCZRMC" label="主要出资人名称" align="center" width="150" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFBB" label="是否并表" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFSSGS" label="是否上市公司" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_GPDM" label="股票代码" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZJJC" label="证券简称" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SSRQ" label="上市日期" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFXM" label="是否休眠停业" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFDGTG" label="是否代管托管" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFKGS" label="是否空壳公司" align="center" width="120" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFSPV" label="是否特殊目的公司" align="center" width="140" show-overflow-tooltip />
        <el-table-column prop="ZCBI_JNJW" label="境内/境外" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_FGDD" label="分工定点" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_TZFX" label="投资方向" align="center" width="100" show-overflow-tooltip />
        <el-table-column prop="ZCBI_SFZLXXCY" label="是否战略性新兴产业" align="center" width="150" show-overflow-tooltip />
        <el-table-column prop="ZCBI_ZLXXCY" label="战略性新兴产业" align="center" width="200" show-overflow-tooltip />
        <el-table-column prop="ZCBI_GYGDHJCGBL" label="国有股东合计持股比例" align="center" width="160" show-overflow-tooltip />
        <el-table-column prop="ZCBI_JTHJCGBL" label="集团合计持股比例" align="center" width="140" show-overflow-tooltip />
        <el-table-column prop="ZCBI_QYZZ" label="企业资质" align="center" width="150" show-overflow-tooltip />
        <el-table-column prop="ZCBI_JYFW" label="经营范围" align="center" width="300" show-overflow-tooltip />
        <el-table-column prop="ZCBI_IF_ZDJJ" label="是否主导基金" align="center" width="120" show-overflow-tooltip />
      </el-table>

      <el-pagination background @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />

    </div>
  </DialogCard>
</template>

<script>
import DialogCard from '@/views/common/DialogCard.vue'
import { getIndicatorValueWithParamsPage } from '@/api/digitalAssetSystem/assetOverview.js'

export default {
  components: { DialogCard },

  data () {
    return {
      dialogVisible: false,
      tableData: [],
      tzfx: {},
      zcbiId: '',
      searchForm: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        tyxydm: ''
      },
      total: 0,
      title: ''
    }
  },

  methods: {

    handleShow (tzfx, zcbiId, title) {
      this.title = title
      this.dialogVisible = true
      this.tzfx = tzfx
      this.zcbiId = zcbiId
      this.getList()
    },

    getList () {
      getIndicatorValueWithParamsPage({
        indicatorCode: 'COMPANY_FINANCE_SUMMARY',
        pageNo: this.searchForm.pageNo,
        pageSize: this.searchForm.pageSize,
        params: {
          zcbiId: this.zcbiId,
          tzfx: this.tzfx,
          name: this.searchForm.name,
          tyxydm: this.searchForm.tyxydm
        },
        // additionalConditions: {
        //   name: this.searchForm.name ? `AND zcbi_name LIKE '%${this.searchForm.name}%'` : '',
        //   tyxydm: this.searchForm.tyxydm ? `AND ZCBI_TYSHXYDM LIKE '%${this.searchForm.tyxydm}%'` : ''
        // }
      }).then(res => {
        console.log("🚀🚀 ~ 企业规模情况-二级页面 ~ 🚀🚀", res.data.list)
        this.tableData = res.data.list
        this.total = res.data.total
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.getList()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.getList()
    },

    handleCancel () {
      this.dialogVisible = false
      this.tableData = []
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.getList()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = val
      this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 100%;
}

.leftRight {
  display: flex;
  // justify-content: space-between;
}
</style>
