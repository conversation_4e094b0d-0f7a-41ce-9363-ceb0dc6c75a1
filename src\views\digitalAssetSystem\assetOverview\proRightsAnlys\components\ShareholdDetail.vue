<template>
  <div class="shareholdDetail-container">
    <VabChartPie v-show="radio == '图表'" :option="option" :title="'持股比例情况'"/>
    <el-table
      border
      v-show="radio == '表格'"
      :data="tableDataCg"
      height="348px"
    >
      <el-table-column
        align="center"
        label="持股比例"
        min-width="35%"
        prop="stockBetween"
      />
      <el-table-column
        align="center"
        label="户数"
        min-width="35%"
        prop="stockBetweenCount"
      />
      <el-table-column
        align="center"
        label="占比"
        min-width="30%"
        prop="stockBetweenPer"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.stockBetweenPer
                ? scope.row.stockBetweenPer + '%'
                : '—'
            }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { searchStockPercent } from '@api/sam/property.js'
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  export default {
    name: "ShareholdDetail",
    props: ['radio'],
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {},
        tableDataCg: []
      }
    },
    created() {
      this.getsearchStockPercent()
      this.$baseEventBus.$on('echartClick',(params,title) => {
        if(title == '持股比例情况') {
          // let companyShareholding = this.tableDataCg[params.dataIndex].COMPANY_SHAREHOLDING
          this.$router.push({
            path: '/views/cockpit/assetCqAssay/components/AssetCaAssayDetail',
            query: {
              data: JSON.stringify(this.tableDataCg),
              title,
              currentName: null,
              tableIndex: params.dataIndex,
              name:params.name
            }
          })
        }
      })
    },  
    methods: {
      async getsearchStockPercent() {
        // let { data } = await searchStockPercent()
        // this.tableDataCg = data.reverse()
        // const data1 = []
        // const data2 = []
        // const xData = []
        // data.forEach(item => {
        //   data1.push(item.stockBetweenCount)
        //   data2.push(item.stockBetweenPer)
        //   xData.push(item.stockBetween)
        // });
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow',
        //       textStyle: {
        //         color: '#fff',
        //       },
        //     },
        //   },
        //   grid: {
        //     left: '5%',
        //     right: '5%',
        //     bottom: '15%',
        //     top: '10%',
        //     containLabel: true,
        //   },
        //   xAxis: [
        //     {
        //       type: 'category',
        //       axisLine: {
        //         lineStyle: {
        //           color: '#90979c',
        //         },
        //       },
        //       splitLine: {
        //         show: false,
        //       },
        //       axisTick: {
        //         show: false,
        //       },
        //       splitArea: {
        //         show: false,
        //       },
        //       axisLabel: {
        //         interval: 0,
        //       },
        //       data: xData,
        //     },
        //   ],
        //   yAxis: [
        //     {
        //       name: '户数',
        //       type: 'value',
        //       splitLine: {
        //         show: false,
        //       },
        //       axisLine: {
        //         show: false,
        //       },
        //       axisTick: {
        //         show: false,
        //       },
        //       axisLabel: {
        //         interval: 0,
        //       },
        //       splitArea: {
        //         show: false,
        //       },
        //     },
        //     {
        //       name: '占比',
        //       type: 'value',
        //       axisLabel: {
        //         formatter: '{value} %',
        //       },
        //       max: 100
        //     },
        //   ],
        //   series: [
        //     {
        //       name: '户数',
        //       type: 'bar',
        //       stack: '户数',
        //       zoom: 1.1,
        //       barWidth: '30',
        //       itemStyle: {
        //         normal: {
        //           color: '#2496FF',
        //           barBorderRadius: 0,
        //         },
        //       },
        //       data: data1,
        //     },
        //     {
        //       name: '占比',
        //       type: 'line',
        //       symbolSize: 10,
        //       yAxisIndex: 1,
        //       symbol: 'circle',
        //       itemStyle: {
        //         normal: {
        //           color: 'rgba(252,230,48,1)',
        //           barBorderRadius: 0,
        //         },
        //       },
        //       data: data2,
        //     },
        //   ],
        // }
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>