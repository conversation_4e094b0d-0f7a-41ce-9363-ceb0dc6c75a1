<!--实物资产档案-->
<template>
  <div class="custom-table-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards" v-if="flag">
      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-office-building" />
        </div>
        <div class="stat-info">
          <div class="stat-title">房屋总数量</div>
          <div class="stat-value red-text">{{ statistics.totalCount || 0 }}<span>个</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">房屋总原值</div>
          <div class="stat-value orange-text">{{ statistics.totalValue || 0 }}<span>亿元</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-data-line" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度处置项目数</div>
          <div class="stat-value blue-text">{{ statistics.disposalCount || 0 }}<span>项</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper purple-bg">
          <i class="el-icon-key" />
        </div>
        <div class="stat-info">
          <div class="stat-title">本年度租赁项目数</div>
          <div class="stat-value purple-text">{{ statistics.leaseCount || 0 }}<span>项</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form" v-if="flag">
      <el-form :inline="true" :model="searchForm" class="form-inline">
        <div class="leftRight">
          <div class="leftBar">
            <el-form-item label="资产名称:">
              <el-input v-model="searchForm.zchiAssetsNo" placeholder="请输入房屋名称" class="inputW" />
            </el-form-item>
            <el-form-item label="产权证号:">
              <el-input v-model="searchForm.zchiCertificateCode" placeholder="请输入产权证号" class="inputW" />
            </el-form-item>
            <el-form-item label="二级成员单位:">
              <el-select v-model="searchForm.companyTname" placeholder="请选择二级成员单位" class="inputW" clearable filterable>
                <el-option v-for="(item, idx) in ejOptions" :key="idx" :label="item.zcbiName" :value="item.zcbiName" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button type="primary" @click="handleAdvancedSearch" icon="el-icon-search">综合查询</el-button>
            <el-button @click="resetQuery" style="margin-right:10px">重置</el-button>
            <ExportButton :export-api="exportBuildings" table-selector="#buildingsTable" :query-form="searchForm" file-name="实物资产档案-房屋建筑物.xls" excel-title="实物资产档案-房屋建筑物" :date-fields="exportDateFields" :show-dropdown="true" :all-data-page-size="10000" button-type="primary" :auto-exclude-operations="true" :exclude-columns="[]" :table-columns="tableColumns" @export-success="handleExportSuccess" @export-error="handleExportError" @export-all-success="handleExportAllSuccess" @export-all-error="handleExportAllError" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table id="buildingsTable" :data="tableData" :height="flag?height:'450px'" border stripe highlight-current-row style="width: 100%;" v-loading="loading" row-key="zchiId">
        <!-- 列配置严格按《各模块列表展示字段(1).md》房屋模块字段顺序与数量配置 -->
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="zchiYear" label="年份" show-overflow-tooltip align="center" label-class-name="zchiYear" />
        <el-table-column prop="zchiMj" label="密级" show-overflow-tooltip align="center" label-class-name="zchiMj" />
        <el-table-column prop="companyTname" label="二级单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyTname" />
        <el-table-column prop="companyName" label="本单位名称" width="150" show-overflow-tooltip align="center" label-class-name="companyName" />
<!--        <el-table-column prop="zchiCreditCode" label="本单位统一社会信用代码" width="180" show-overflow-tooltip align="center" label-class-name="zchiCreditCode" />-->
        <el-table-column prop="zchiIfExist" label="是否取得房屋所有权证" width="180" show-overflow-tooltip align="center" label-class-name="zchiIfExist" />
        <el-table-column prop="zchiAssetsNo" label="资产编号" width="150" show-overflow-tooltip align="center" label-class-name="zchiAssetsNo" />
        <el-table-column prop="zchiAssetsName" label="资产名称" width="150" show-overflow-tooltip align="center" label-class-name="zchiAssetsName" />
        <el-table-column prop="zchiAsssetsmType" label="资产类型" width="150" show-overflow-tooltip align="center" label-class-name="zchiAsssetsmType" />
        <el-table-column prop="zchiCertificateCode" label="房屋所有权证号" width="150" show-overflow-tooltip align="center" label-class-name="zchiCertificateCode" />
        <el-table-column prop="zchiNocertificateReason" label="未取得房屋权属证明原因" width="180" show-overflow-tooltip align="center" label-class-name="zchiNocertificateReason" />
        <el-table-column prop="zchiUseDescribe" label="现状用途描述" width="150" show-overflow-tooltip align="center" label-class-name="zchiUseDescribe" />
        <el-table-column prop="zchiStrategyDescribe" label="是否有特别战略安排" width="150" show-overflow-tooltip align="center" label-class-name="zchiStrategyDescribe" />
        <el-table-column prop="zchiRange" label="境内/境外" width="150" show-overflow-tooltip align="center" label-class-name="zchiRange" />
        <el-table-column prop="zchiCountry" label="国家或地区" width="150" show-overflow-tooltip align="center" label-class-name="zchiCountry" />
        <el-table-column prop="zchiProvince" label="坐落位置（省）" width="150" show-overflow-tooltip align="center" label-class-name="zchiProvince" />
        <el-table-column prop="zchiCity" label="坐落位置（市）" width="150" show-overflow-tooltip align="center" label-class-name="zchiCity" />
        <el-table-column prop="zchiDistrict" label="坐落位置（区）" width="150" show-overflow-tooltip align="center" label-class-name="zchiDistrict" />
        <el-table-column prop="zchiAddress" label="具体地理位置" width="150" show-overflow-tooltip align="center" label-class-name="zchiAddress" />
        <el-table-column prop="zcliCertificateCode" label="对应土地权属证明编号" width="180" show-overflow-tooltip align="center" label-class-name="zcliCertificateCode" />
        <el-table-column prop="zchiHouseSource" label="取得方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiHouseSource" />
        <el-table-column prop="zchiDate" label="取得时间" width="150" show-overflow-tooltip align="center" label-class-name="zchiDate" />
        <el-table-column prop="zchiServiceLife" label="使用年限（年）" width="150" show-overflow-tooltip align="center" label-class-name="zchiServiceLife" />
        <el-table-column prop="zchiOriginalValue" label="原值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zchiOriginalValue" />
        <el-table-column prop="zchiNetValue" label="净值(万元)" width="150" show-overflow-tooltip align="center" label-class-name="zchiNetValue" />
        <el-table-column prop="zchiIfAssets" label="是否两非资产" width="150" align="center" label-class-name="zchiIfAssets" />
        <el-table-column prop="zchiDepreciableYear" label="折旧年限" width="150" show-overflow-tooltip align="center" label-class-name="zchiDepreciableYear" />
        <el-table-column prop="zchiTotalDepreciation" label="本年计提折旧总额（万元）" width="200" align="center" label-class-name="zchiTotalDepreciation" />
        <el-table-column prop="zchiEvaluateDate" label="最近评估日期" width="150" show-overflow-tooltip align="center" label-class-name="zchiEvaluateDate" />
        <el-table-column prop="zchiEvaluateValue" label="最近评估价值(万元）" width="180" show-overflow-tooltip align="center" label-class-name="zchiEvaluateValue" />
        <el-table-column prop="zchiArea" label="房屋总面积(平方米)" width="150" align="center" label-class-name="zchiArea" />
        <el-table-column prop="zchiOfficeArea" label="其中：科研办公面积（平方米）" width="220" align="center" label-class-name="zchiOfficeArea" />
        <el-table-column prop="zchiIndustrialArea" label="其中：工业面积（平方米）" width="220" align="center" label-class-name="zchiIndustrialArea" />
        <el-table-column prop="zchiCommercialArea" label="其中：商业面积（平方米）" width="220" align="center" label-class-name="zchiCommercialArea" />
        <el-table-column prop="zchiResidentialArea" label="其中：住宅面积（平方米）" width="220" align="center" label-class-name="zchiResidentialArea" />
        <el-table-column prop="zchiOtherArea" label="其中：其他面积（平方米）" width="220" align="center" label-class-name="zchiOtherArea" />
        <el-table-column prop="zchiUndergroundArea" label="地下总建筑面积（平方米）" width="220" align="center" label-class-name="zchiUndergroundArea" />
        <el-table-column prop="zchiOnroundAreaScientific" label="科研、办公、联试等面积（平方米）" width="250" align="center" label-class-name="zchiOnroundAreaScientific" />
        <el-table-column prop="zchiOnroundAreaHotel" label="住宅酒店等" width="150" show-overflow-tooltip align="center" label-class-name="zchiOnroundAreaHotel" />
        <el-table-column prop="zchiOnroundAreaFactory" label="生产厂房面积（平方米）" width="220" align="center" label-class-name="zchiOnroundAreaFactory" />
        <el-table-column prop="zchiOnroundAreaOutfield" label="试验外场面积（平方米）" width="220" align="center" label-class-name="zchiOnroundAreaOutfield" />
        <el-table-column prop="zchiBusinessDirection" label="房屋主要经营方向" width="150" show-overflow-tooltip align="center" label-class-name="zchiBusinessDirection" />
        <el-table-column prop="zchiAreaZy" label="其中：自用面积（平方米）" width="220" align="center" label-class-name="zchiAreaZy" />
        <el-table-column prop="zchiAreaCz" label="其中：出租面积（平方米）" width="220" align="center" label-class-name="zchiAreaCz" />
        <el-table-column prop="zchiAreaXz" label="其中：闲置面积（平方米）" width="220" align="center" label-class-name="zchiAreaXz" />
        <el-table-column prop="zchiIdleStartTime" label="闲置起始时间" width="150" show-overflow-tooltip align="center" label-class-name="zchiIdleStartTime" />
        <el-table-column prop="zchiIdleTime" label="空置时间" width="150" show-overflow-tooltip align="center" label-class-name="zchiIdleTime" />
        <el-table-column prop="zchiVacancyReasons" label="空置原因" width="150" show-overflow-tooltip align="center" label-class-name="zchiVacancyReasons" />
        <el-table-column prop="zchiMethods" label="建议盘活/处置方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiMethods" />
        <el-table-column prop="zchiRentalPriceLastyear" label="上年租赁单价（元/平米/天）" width="220" align="center" label-class-name="zchiRentalPriceLastyear" />
        <el-table-column prop="zchiRentalIncomeLastyear" label="上年租金收入（万元）" width="180" align="center" label-class-name="zchiRentalIncomeLastyear" />
        <el-table-column prop="zchiRentalPrice" label="本年租赁单价（元/平米/天）" width="220" align="center" label-class-name="zchiRentalPrice" />
        <el-table-column prop="zchiRentalIncomeThisyear" label="预计本年租金收入（万元）" width="220" align="center" label-class-name="zchiRentalIncomeThisyear" />
        <el-table-column prop="zchiSurroundingSalePrice" label="上年周边可比房产出售单价（元/平方米/月）" width="300" align="center" label-class-name="zchiSurroundingSalePrice" />
        <el-table-column prop="zchiSurroundingRentPrice" label="上年周边可比房产出租单价（元/平方米/月）" width="300" align="center" label-class-name="zchiSurroundingRentPrice" />
        <el-table-column prop="zchiBusinessDzzb" label="电子装备(平方米)" width="150" align="center" label-class-name="zchiBusinessDzzb" />
        <el-table-column prop="zchiBusinessWxtx" label="网信体系(平方米)" width="150" align="center" label-class-name="zchiBusinessWxtx" />
        <el-table-column prop="zchiBusinessCyjc" label="产业基础(平方米)" width="150" align="center" label-class-name="zchiBusinessCyjc" />
        <el-table-column prop="zchiBusinessWlaq" label="网络安全(平方米)" width="150" align="center" label-class-name="zchiBusinessWlaq" />
        <el-table-column prop="zchiBusinessOther" label="其他(平方米)" width="150" align="center" label-class-name="zchiBusinessOther" />
        <el-table-column prop="zchiIfDispute" label="是否存在纠纷" width="150" show-overflow-tooltip align="center" label-class-name="zchiIfDispute" />
        <el-table-column prop="zchiIfMortgage" label="是否存在抵押" width="150" show-overflow-tooltip align="center" label-class-name="zchiIfMortgage" />
        <el-table-column prop="zchiMortgage" label="其中:已抵押面积（平方米）" width="220" align="center" label-class-name="zchiMortgage" />
        <el-table-column prop="zchiIfDispose" label="是否可处置" width="150" align="center" label-class-name="zchiIfDispose" />
        <el-table-column prop="zchiReasonDisposal" label="不可处置原因" width="150" show-overflow-tooltip align="center" label-class-name="zchiReasonDisposal" />
        <el-table-column prop="zchiDeptName" label="业务主管部门名称" width="150" show-overflow-tooltip align="center" label-class-name="zchiDeptName" />
        <el-table-column prop="zchiOperator" label="经办人" width="150" show-overflow-tooltip align="center" label-class-name="zchiOperator" />
        <el-table-column prop="zchiOperatorTel" label="经办人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiOperatorTel" />
        <el-table-column prop="zchiDepartmentLeader" label="部门负责人" width="150" show-overflow-tooltip align="center" label-class-name="zchiDepartmentLeader" />
        <el-table-column prop="zchiDepartmentTel" label="部门负责人联系方式" width="150" show-overflow-tooltip align="center" label-class-name="zchiDepartmentTel" />
        <el-table-column prop="zchiCompanyLeader" label="分管所(公司)领导" width="150" show-overflow-tooltip align="center" label-class-name="zchiCompanyLeader" />
        <el-table-column prop="zchiCompanyTel" label="分管所(公司)领导联系方式" width="220" show-overflow-tooltip align="center" label-class-name="zchiCompanyTel" />
        <el-table-column prop="zchiRemark" label="备注" width="150" show-overflow-tooltip align="center" label-class-name="zchiRemark" />
        <el-table-column prop="zchiLongitude" label="经度" width="150" show-overflow-tooltip align="center" label-class-name="zchiLongitude" />
        <el-table-column prop="zchiLatitude" label="纬度" width="150" show-overflow-tooltip align="center" label-class-name="zchiLatitude" />
        <el-table-column label="操作" width="80" fixed="right" align="center" v-if="flag">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination background class="el-pagination-a b-none" @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="searchForm.pageNo" :page-size="searchForm.pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>

    <!-- 详情弹窗组件 -->
    <building-detail-dialog ref="buildingDetail" />

    <!-- 高级查询弹窗组件 -->
    <building-advanced-search-dialog ref="buildingAdvancedSearch" :external-search-form="searchForm" @search="handleAdvancedSearchSubmit" />
  </div>
</template>

<script>
import { getBuildingsList, exportBuildings, getBuildingsStats } from '@/api/digitalAssetSystem/buildings'
import BuildingDetailDialog from './components/BuildingDetailDialog.vue'
import BuildingAdvancedSearchDialog from './components/BuildingAdvancedSearchDialog.vue'
import ExportButton from '@/components/ExportButton/index.vue'
import { searchSecoundOrgInfo } from '@/api/common'

export default {
  name: "index",
  components: {
    BuildingDetailDialog,
    BuildingAdvancedSearchDialog,
    ExportButton
  },
  props: {
    flag: {
      type: Boolean,
      default: true
    },
    propCompanyId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      height: this.$baseTableHeight(1, 3.5),
      searchForm: {
        zchiAssetsNo: '',
        zchiCertificateCode: '',
        companyName: '',
        companyTname: '',
        zchiCity: '',
        zchiBusinessDirection: '',
        zchiUseDescribe: '',
        zchiIfDispose: '',
        // 高级查询字段
        zchiAssetsName: '',
        zchiAddress: '',
        zchiHouseSource: '',
        zchiDate: '',
        zchiArea: '',
        zchiOriginalValue: '',
        zchiNetValue: '',
        zchiTotalDepreciation: '',
        zchiCountry: '',
        zchiIfAssets: '',
        zchiIfExist: '',
        zchiIfDispute: '',
        zchiIfMortgage: '',
        zchiProvince: '',
        zchiDeptName: '',
        zchiDepartmentLeader: '',
        zchiDepartmentTel: '',
        zchiCompanyLeader: '',
        zchiCompanyTel: '',
        zchiEvaluateValue: '',
        zchiEvaluateDate: '',
        zchiServiceLife: '',
        zchiDepreciableYear: '',
        zchiOfficeArea: '',
        zchiCommercialArea: '',
        zchiResidentialArea: '',
        zchiIndustrialArea: '',
        zchiUndergroundArea: '',
        zcliCertificateCode: '',
        zcfaAbility: '',
        zchiOtherArea: '',
        zchiRemark: '',
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      ejOptions: [],
      statistics: {
        totalCount: 0,
        totalValue: 0,
        disposalCount: 0,
        leaseCount: 0
      },

      // 表格列配置，用于导出 - 严格 76 字段，按接口文档顺序
      tableColumns: [
        { prop: 'zchiYear', label: '年份' },
        { prop: 'zchiMj', label: '密级' },
        { prop: 'companyTname', label: '二级单位名称' },
        { prop: 'companyName', label: '本单位名称' },
        { prop: 'zchiIfExist', label: '是否取得房屋所有权证' },
        { prop: 'zchiAssetsNo', label: '资产编号' },
        { prop: 'zchiAsssetsmType', label: '资产类型' },
        { prop: 'zchiAssetsName', label: '资产名称' },
        { prop: 'zchiCertificateCode', label: '房屋所有权证号' },
        { prop: 'zchiNocertificateReason', label: '未取得房屋所有权证号原因' },
        { prop: 'zchiLandCode', label: '对应土地证编号' },
        { prop: 'zcliCertificateCode', label: '对应土地权属证明编号' },
        { prop: 'zchiUseDescribe', label: '现状用途描述' },
        { prop: 'zchiBusinessDirection', label: '房屋主要经营方向' },
        { prop: 'zchiStrategyDescribe', label: '是否有战略安排' },
        { prop: 'zchiRange', label: '境内境外' },
        { prop: 'zchiCountry', label: '国家或地区' },
        { prop: 'zchiCreditCode', label: '统一社会信用代码' },
        { prop: 'zchiProvince', label: '土地所在省' },
        { prop: 'zchiCity', label: '房屋所在市' },
        { prop: 'zchiDistrict', label: '房屋所在区/县' },
        { prop: 'zchiAddress', label: '具体土地位置' },
        { prop: 'zchiHouseSource', label: '取得方式' },
        { prop: 'zchiDate', label: '取得时间' },
        { prop: 'zchiServiceLife', label: '使用年限(年)' },
        { prop: 'zchiOriginalValue', label: '原值（万元）' },
        { prop: 'zchiNetValue', label: '净值（万元）' },
        { prop: 'zchiIfAssets', label: '是否两非资产' },
        { prop: 'zchiDepreciableYear', label: '折旧年限' },
        { prop: 'zchiTotalDepreciation', label: '本年计提折旧总额（万元）' },
        { prop: 'zchiEvaluateDate', label: '最近评估日期' },
        { prop: 'zchiEvaluateValue', label: '最近评估价值（万元）' },
        { prop: 'zchiArea', label: '房屋总面积' },
        { prop: 'zchiAreaZy', label: '自用面积' },
        { prop: 'zchiAreaCz', label: '出租面积' },
        { prop: 'zchiAreaXz', label: '闲置面积' },
        { prop: 'zchiIdleStartTime', label: '闲置起始时间' },
        { prop: 'zchiIdleTime', label: '空置时间' },
        { prop: 'zchiVacancyReasons', label: '空置原因' },
        { prop: 'zchiMethods', label: '建议盘活/处置方式' },
        { prop: 'zchiRentalIncomeLastyear', label: '上年租金收入（万元）' },
        { prop: 'zchiRentalIncomeThisyear', label: '预计本年租金收入（万元）' },
        { prop: 'zchiRentalPrice', label: '本年租赁单价（元/平米/天）' },
        { prop: 'zchiSurroundingSalePrice', label: '上年周边可比土地出售单价（元/平方米/月）' },
        { prop: 'zchiSurroundingRentPrice', label: '上年周边可比土地出租单价（元/平方米/月）' },
        { prop: 'zchiBusinessDzzb', label: '电子装备面积' },
        { prop: 'zchiBusinessWxtx', label: '网信体系面积' },
        { prop: 'zchiBusinessCyjc', label: '产业基础面积' },
        { prop: 'zchiBusinessWlaq', label: '网络安全面积' },
        { prop: 'zchiBusinessOther', label: '其它面积面积' },
        { prop: 'zchiCommercialArea', label: '商业面积' },
        { prop: 'zchiResidentialArea', label: '住宅面积' },
        { prop: 'zchiOfficeArea', label: '科研办公面积' },
        { prop: 'zchiIndustrialArea', label: '工业面积' },
        { prop: 'zchiOtherArea', label: '其他面积' },
        { prop: 'zchiOnroundAreaScientific', label: '科研、办公、联试等面积（平方米）' },
        { prop: 'zchiOnroundAreaHotel', label: '住宅酒店等' },
        { prop: 'zchiOnroundAreaFactory', label: '生产厂房面积（平方米）' },
        { prop: 'zchiOnroundAreaOutfield', label: '试验外场面积（平方米）' },
        { prop: 'zchiUndergroundArea', label: '地下总建筑面积（平方米）' },
        { prop: 'zchiIfDispute', label: '是否存在纠纷' },
        { prop: 'zchiIfMortgage', label: '是否存在抵押' },
        { prop: 'zchiMortgage', label: '已抵押面积（平方米）' },
        { prop: 'zchiIfDispose', label: '是否可处置' },
        { prop: 'zchiReasonDisposal', label: '不可处置原因' },
        { prop: 'zchiDeptName', label: '业务主管部门名称' },
        { prop: 'zchiDepartmentLeader', label: '部门负责人' },
        { prop: 'zchiDepartmentTel', label: '部门负责人联系方式' },
        { prop: 'zchiCompanyLeader', label: '分管所(公司)领导' },
        { prop: 'zchiCompanyTel', label: '分管所(公司)领导联系方式' },
        { prop: 'zchiLongitude', label: '经度' },
        { prop: 'zchiLatitude', label: '纬度' },
        { prop: 'zcfaAbility', label: '资产能力' },
        { prop: 'zchiInventoryStatus', label: '盘点状态' }
      ],

      // 导出日期字段配置
      exportDateFields: {
        zchiDate: { celltype: "text" },
        zchiEvaluateDate: { celltype: "text" },
        createdTime: { celltype: "text" },
        updatedTime: { celltype: "text" }
      },

      // 导出API函数
      exportBuildings
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
    this.fetchEjOptions()
  },
  methods: {
    // 加载二级成员单位选项
    fetchEjOptions () {
      searchSecoundOrgInfo().then(({ data }) => {
        this.ejOptions = (data && data.children) || []
      }).catch(() => {
        this.ejOptions = []
      })
    },
    fetchData () {
      this.loading = true
      const query = {
        ...this.searchForm,
        companyId: this.propCompanyId ? this.propCompanyId : '',
        zchi_year: this.propCompanyId ? '2025' : '',
      }

      getBuildingsList(query).then(response => {
        if (response && response.data) {
          this.tableData = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getBuildingsStats().then(response => {
        if (response && response.data) {
          this.statistics = {
            totalCount: response.data.total_house_count || 0,
            totalValue: response.data.total_net_value || 0,
            disposalCount: response.data.disposalCount || 0,
            leaseCount: response.data.leaseCount || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    resetQuery () {
      // 重置所有查询字段
      Object.keys(this.searchForm).forEach(key => {
        this.searchForm[key] = ''
      })
      this.searchForm.pageNo = 1
      this.searchForm.pageSize = 10
      this.fetchData()
    },

    // 导出成功回调
    handleExportSuccess (response) {
      console.log('当前数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 导出失败回调
    handleExportError (error) {
      console.error('当前数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    // 全部数据导出成功回调
    handleExportAllSuccess (response) {
      console.log('全部数据导出成功:', response)
      // 可以在这里添加额外的成功处理逻辑
    },

    // 全部数据导出失败回调
    handleExportAllError (error) {
      console.error('全部数据导出失败:', error)
      // 可以在这里添加额外的错误处理逻辑
    },

    handleDetail (row) {
      this.$refs.buildingDetail.showDialog(row)
    },

    handleAdvancedSearch () {
      this.$refs.buildingAdvancedSearch.showDialog()
    },

    handleAdvancedSearchSubmit (searchParams) {
      // 直接使用高级查询的完整参数替换当前搜索表单
      this.searchForm = { ...searchParams }
      this.searchForm.pageNo = 1
      this.fetchData()
    },

    handleSizeChange (val) {
      this.searchForm.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.searchForm.pageNo = 1
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.leftRight {
  display: flex;
  // justify-content: space-between;
}
.inputW {
  width: 250px;
}

.custom-table-container {
  padding: 16px;
  background-color: #ffffff;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.purple-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.purple-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px 20px 0 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.table-section {
  background: white;
  border-radius: 4px;
}
</style>
