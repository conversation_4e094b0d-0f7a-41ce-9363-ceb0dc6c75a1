<template>
  <div class="thisApprovalPro-container">
    <div class="bottom">
      <div 
        class="bottomItem"
      >
        <div class="itemUp">
          <p class="title">外部股权融资批复金额</p>
          <p>年初<span class="nums">{{ NC }}</span>万元，年中<span class="nums">{{ NZ }}</span>万元</p>
        </div>
        <div class="itemDown">
          <div class="item_left">
            <p class="title">截至{{ month }}月累计外部股权融资总额</p>
            <p><span class="nums">{{ LTotal }}</span>万元</p>
          </div>
          <div class="item_left">
            <p class="title">外部股权融资完成率</p>
            <p><span class="nums">{{ RTotal * 100 }}</span>%</p>
          </div>
        </div>
      </div>
    </div>
    
    <VabChartPie style="width:50%" :option="option" :title="'本年融资计划情况'"/>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { getInvestDtatisticsBottom } from '@/api/sam/assetInvestment'
  // import { monthlyFinancingSituation } from '@/api/sam/assetFx'
  export default {
    name: "ThisYearStockPlan",
    props: ['year'],
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {},
        NC: 0,
        NZ: 0,
        LTotal: 0,
        RTotal: 0,
        month: new Date().getMonth() + 1
      }
    },
    mounted() {
      this.getData()
      this.getEchartData()
    },
    methods: {
      async getData() {
        let params = {
          zcsfpYear: this.year,
          zcType: 'stock'
        }
        // let { data } = await getInvestDtatisticsBottom(params)
        // this.NC = data[0].zcsfpExtrnalPf
        // this.NZ = data[0].zcsfpExtrnalMidpf
        // this.LTotal = data[0].totalFunds2
        // this.RTotal = data[0].rate2
      },
      async getEchartData() {
        let params = {
          year: this.year
        }
        // let { data } = await monthlyFinancingSituation(params)
        // let xData = []
        // let yData1 = []
        // let yData2 = []
        // data.forEach(item => {
        //   xData.push(item.month)
        //   yData1.push(item.rnowym)
        //   yData2.push(item.rnowyms)
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow'
        //     }
        //   },
        //   legend: {},
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)'],
        //   grid: {
        //     left: '5%',
        //     right: '10%',
        //     bottom: '5%',
        //     top:'15%',
        //     containLabel: true
        //   },
        //   xAxis: {
        //     name: '月份',
        //     type: 'category',
        //     data: xData
        //   },
        //   yAxis: [{
        //     type: 'value',
        //     name: '亿元'
        //   }],
        //   series: [
        //     {
        //       name: '本月完成额',
        //       data: yData1,
        //       type: 'bar'
        //     },
        //     {
        //       name: '截止本月累计完成额',
        //       data: yData2,
        //       type: 'bar'
        //     }
        //   ]
        // }
      }
    },
  }
</script>

<style lang="scss" scoped>
  .thisApprovalPro-container {
    width: 100%;
    height: 100%;
    display: flex;
    .bottom {
      width: 50%;
      height: 204px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .bottomItem {
        width: 100%;
        height: 100%;
        background: rgba(204,18,20,0.04);
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E4E7ED;
        .itemUp {
          width: 100%;
          height: 50%;
          border-bottom: 1px solid #E4E7ED;
          text-align: center;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000000;
          line-height: 22px;
          padding-top: 22px;
          p:first-child {
            margin-bottom: 8px;
          }
          .nums {
            font-size: 24px;
            font-weight: 500;
            margin: 0 4px;
          }
        }
        .itemDown {
          display: flex;
          width: 100%;
          height: 50%;
          .item_left {
            width: 50%;
            height: 100%;
            text-align: center;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #000000;
            line-height: 22px;
            padding-top: 22px;
            p:first-child {
              margin-bottom: 8px;
            }
            .nums {
              font-size: 24px;
              font-weight: 500;
              margin: 0 4px;
            }
          }
          .item_left:first-child {
            border-right: 1px solid #E4E7ED;
          }
        }
        p {
          margin: 0px;
        }
      }
    }
  }
  :deep() {
    .echarts {
      width: 100%;
      height: 204px;
    }
  }
</style>