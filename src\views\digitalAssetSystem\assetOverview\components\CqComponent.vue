<template>
  <div class="cq-component">
    <div class="section-title"><span class="bar"></span>企业产权管理层级分布</div>
    <!-- 企业产权管理层级分布图表 -->
    <div class="chart-container">
      <CqBarChart :echartsData="echartsData" />
    </div>
    <!-- 统计卡片区域 -->
    <div class="stats-container">
      <div class="stats-grid">
        <div class="stat-card" @click="showQy({ sfbb: `是`, level: '', sfssgs: '', zzxs: '' },'并表企业')">
          <div class="stat-label">并表单位</div>
          <div class="stat-value">{{ allData.bingb }}<span class="unit">户</span></div>
        </div>
        <div class="stat-card" @click="showQy({ sfbb: '', level: `2`, sfssgs: '', zzxs: '' },'二级单位')">
          <div class="stat-label">二级单位(管理层级)</div>
          <div class="stat-value">{{ allData.ejdw }}<span class="unit">户</span></div>
        </div>
        <div class="stat-card" @click="showQy({ sfbb: '', level: '', sfssgs: '', zzxs: `事业单位` },'事业单位')">
          <div class="stat-label">事业单位</div>
          <div class="stat-value">{{ allData.sydw }}<span class="unit">户</span></div>
        </div>
        <div class="stat-card" @click="showQy({ sfbb: '是', level: '', sfssgs: `是`, zzxs:'' },'上市公司')">
          <div class="stat-label">上市公司</div>
          <div class="stat-value">{{ allData.shangs }}<span class="unit">户</span></div>
        </div>
      </div>
    </div>

    <NlSecondDialog ref="nlSecondDialog" />
  </div>
</template>

<script>
import CqBarChart from './CqBarChart.vue'
import NlSecondDialog from '@/views/digitalAssetSystem/assetOverview/components/NlSecondDialog'

export default {
  props: {
    echartsData: {
      type: Array,
      default: () => []
    },
    allData: {
      type: Object,
      default: () => { }
    },
    propDeptId: {
      type: String,
      default: ''
    }
  },
  components: {
    CqBarChart,
    NlSecondDialog
  },
  data () {
    return {
      // 这里可以放置其他数据
    }
  },
  methods: {
    showQy (obj, titType) {
      this.$refs.nlSecondDialog.handleShow(obj, this.propDeptId, titType)
    },
  }
}
</script>

<style lang="scss" scoped>
.cq-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  .chart-container {
    width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
  }

  .stats-container {
    flex: 1;
    width: 100%;
    height: 100%;
    .stats-grid {
      width: 100%;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 8px;

      .stat-card {
        border-radius: 4px;
        text-align: center;
        border: 1px solid #e4e7ed;
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;

        // 奇数位置的卡片背景色
        &:nth-child(1) {
          background: #f9fdfd;
        }

        // 偶数位置的卡片背景色
        &:nth-child(2) {
          background: #fdfdf9;
        }

        &:nth-child(3) {
          background: #fdfdf9;
        }

        &:nth-child(4) {
          background: #f9fdfd;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          font-weight: 400;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;

          .unit {
            font-size: 14px;
            font-weight: normal;
            color: #909399;
            margin-left: 2px;
          }
        }
      }
    }
  }
}

.section-title {
  display: flex;
  align-items: center;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
  .bar {
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #cc1214;
    border-radius: 2px;
    margin-right: 6px;
  }
}
</style>