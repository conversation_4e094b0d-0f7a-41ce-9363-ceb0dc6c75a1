<template>
  <div class="businessClass-container">
    <VabChartPie :option="option" :title="'企业类别分析'"/>
    <el-table
      border
      :data="tableDataAnalysies"
      height="241px"
      ref="table"
      show-summary
    >
      <el-table-column
        align="center"
        label="企业类别"
        min-width="40"
        prop="zcciEnterpriseType"
      />
      <el-table-column
        align="center"
        label="户数"
        min-width="30"
        prop="zcciEnterpriseTypeCount"
      />
      <el-table-column
        align="center"
        label="占比"
        min-width="30"
        prop="zcciEnterpriseTypePer"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.zcciEnterpriseTypePer + '%' }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  // import { searchEnterpriseType } from '@api/sam/property.js'
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  export default {
    name: "BusinessClass",
    components: {
      VabChartPie
    },
    data() {
      return {
        option: {},
        tableDataAnalysies: [],
      }
    },
    created() {
      this.getsearchEnterpriseType()
      this.$baseEventBus.$on('echartClick',(params,title) => {
        if(title == '企业类别分析') {
          this.$router.push({
            name: 'AssetCaAssayDetail',
            query: {
              data: JSON.stringify(this.tableDataAnalysies),
              title,
              currentName: params.name,
              tableIndex: params.dataIndex
            }
          })
        }
      })
    },
    methods: {
      async getsearchEnterpriseType() {
        // let { data } = await searchEnterpriseType()
        // this.tableDataAnalysies = data
        // // for (let i = 0; i < data.length; i++) {
        // //   const params = {
        // //     value: res.data[i].ZCCI_ENTERPRISE_TYPE_COUNT,
        // //     name: res.data[i].ZCCI_ENTERPRISE_TYPE,
        // //   }
        // // }
        // let echartData = []
        // data.forEach(item => {
        //   echartData.push({
        //     name: item.zcciEnterpriseType,
        //     value: item.zcciEnterpriseTypeCount,
        //     data: item.zcciEnterpriseTypePer
        //   })
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'item'
        //   },
        //   legend: {
        //     left: '45%',
        //     top: '25%',
        //     orient: 'vertical',
        //     icon:'circle',
        //     textStyle: {
        //       color:'#303133',
        //       fontSize: 16
        //     },
        //     formatter: (name) => {
        //       let str = ''
        //       for (let i = 0; i < echartData.length; i++) {
        //         if (echartData[i].name == name) {
        //             str = `${echartData[i].name} \t ${echartData[i].value}户  |  ${echartData[i].data}%`
        //         }
        //       }
        //       return str
        //     },
        //   },
        //   grid: {
        //     top:'1%',
        //     containLabel: true
        //   },
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)','rgba(93,112,146,0.85)'],
        //   series: [
        //     {
        //       name: '企业类别分析',
        //       type: 'pie',
        //       radius: ['50%', '70%'],
        //       center: ['25%','40%'],
        //       avoidLabelOverlap: false,
        //       label: {
        //         show: false,
        //         position: 'center'
        //       },
        //       emphasis: {
        //         label: {
        //           show: true,
        //           fontSize: 20,
        //           fontWeight: 'bold'
        //         }
        //       },
        //       labelLine: {
        //         show: false
        //       },
        //       data: echartData
        //     }
        //   ]
        // }
      },
    },
    updated() {
      this.$nextTick(() => {
        this.$refs.table.doLayout()
      })
    }
  }
</script>

<style lang="scss" scoped>
  .businessClass-container {
    width: 100%;
    height: 100%;
  }
  :deep() {
    .echarts {
      width: 100%;
      height: 252px;
    }
  }
</style>