<template>
  <SmallCard title="项目查询">
    <div slot="rightTitle" style="margin-top: 16px;">
      <el-radio-group v-model="radioValue1">
        <el-radio label="在批项目">在批项目</el-radio>
        <el-radio label="在建项目">在建项目</el-radio>
        <el-radio label="已完成项目">已完成项目</el-radio>
      </el-radio-group>
      <el-popover popper-class="custom-table-checkbox" trigger="hover">
        <div style="height: 280px;overflow-y: scroll;">
          <el-checkbox-group v-model="taleCheckList">
            <vab-draggable v-bind="dragOptions" :list="tableHeader">
              <div v-for="(item, index) in tableHeader" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox :disabled="item.disableCheck === true" :label="item.label">
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
        </div>
        <el-button @click="handleExportRear">导出</el-button>
        <template #reference>
          <el-button icon="el-icon-setting" style="margin: 0 0 10px 16px !important" title="自定义列导出">
            自定义列导出
          </el-button>
        </template>
      </el-popover>
    </div>
    <el-tabs v-model="radioValue" type="card" class="tabs" >
      <el-tab-pane v-for="(item,index) in typeOptions" :key="index" :label="item" :name="item" class="tab-pane">
      </el-tab-pane>
    </el-tabs>
     <VabForm :formModel="searchForm" :formItem="formItem" :formConfig="{ inline: true }" width="100%" />
    <VabTable
      :key="tableHeaderKey" 
      :tableHeader="tableHeader" 
      :tableData="tableData" 
      :tableHeight="tableHeight" 
      :pageNo="tablePage.pageNo"
      :pageSize="tablePage.pageSize" 
      :total="tablePage.total" 
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange" 
    />
  </SmallCard>
</template>

<script>
import VabForm from 'components/VabForm'
  import SmallCard from 'common/SmallCard';
  import VabTable from 'components/VabTable';
  import VabDraggable from 'vuedraggable'
  import { getProjectList } from 'api/tzgl/project/workbench';
  import { exportRearEnd } from 'api/exportExcel'
  import { cloneDeep } from 'lodash';
  import { getsecondaryunitsList } from 'api/tzgl/investPlan/searchList'

  export default {
    name: 'BoostProject',
    components: {
      SmallCard,
      VabTable,
      VabForm,
      VabDraggable
    },
    data() {
      return {
        searchForm:{},
        formItem:[
            {
              name: 'button',
              prop: 'button',
              label: "查询", // 动态 label
              type: 'primary',
              click: this.searchTableDate,
            },
            {
              name: 'button',
              prop: 'reset',
              label: '重置',
              click: this.resTable,
            },],
        radioValue:'',
        radioValue1:'在批项目',
        radioValue1Options:['在批项目','在建项目','已完成项目'],
        typeOptions:[
          '自筹类固定资产投资',
          '股权投融资',
          '军工类固定资产投资',
          '资产经营'
        ],
        tableHeaderList: {
          '军工类固定资产投资':[
            { type: 'index', width:'55',showOverflowTooltip: true, label: "序号",align:'center' }, 
            { prop: 'tfjpPeriod',showOverflowTooltip: true, label:'时期',align:'center', width:'120' },
            { prop: 'tfjpProjectNo',showOverflowTooltip: true, label:'项目编号',align:'center', width:'120' },
            { prop: 'tfjpProjectName',showOverflowTooltip: true, label:'项目名称',align:'center', width:'120' },
            { prop: 'tfjpProjectMj',showOverflowTooltip: true, label:'项目密级',align:'center', width:'120' },
            { prop: 'tfjpCzCompanyName',showOverflowTooltip: true, label:'建设单位',align:'center', width:'120' },
            { prop: 'tfjpProject',showOverflowTooltip: true, label:'项目类型',align:'center', width:'120' },
            { type: 'action', prop: 'tfpProvince',showOverflowTooltip: true, label:'项目建设地点（省-市）',align:'center', width:'130',render:(h,scope)=>{
              return <div>{scope.row.tfjpProvince + scope.row.tfjpCity}</div>
            } },
            { prop: 'tfjpPhaseOrder',showOverflowTooltip: true, label:'项目所处阶段',align:'center', width:'130' },
            { prop: 'tfjpInvestmentPlanTime',showOverflowTooltip: true, label:'首批投资计划下达日期',align:'center', width:'170' },
            { prop: 'tfjpBuildCycle',showOverflowTooltip: true, label:'建设周期（月）',align:'center', width:'130' },
            { prop: 'tfjpConstructTarget',showOverflowTooltip: true, label:'建设目标',align:'center', width:'120' },
            { prop: 'tfjpContent',showOverflowTooltip: true, label:'主要建设内容',align:'center', width:'130' },
            { prop: 'tfjpIfStartEarly',showOverflowTooltip: true, label:'是否提前启动',align:'center', width:'130' },
            { prop: 'tfjpIfPostSubsidy',showOverflowTooltip: true, label:'是否事后补助',align:'center', width:'130' },
            { prop: 'tfjpIfLandAcquisition',showOverflowTooltip: true, label:'是否征地',align:'center', width:'120' },
            { prop: 'tfjpLandAcquisitionArea',showOverflowTooltip: true, label:'征地面积（亩）',align:'center', width:'130' },
            { prop: 'tfjpBuildingAreaAdd',showOverflowTooltip: true, label:'新建面积（平方米）',align:'center', width:'150' },
            { prop: 'tfjpExpansionArea',showOverflowTooltip: true, label:'改扩建面积（平方米）',align:'center', width:'110' },
            { prop: 'tfjpProcessEquipmentAdd',showOverflowTooltip: true, label:'新增/改造工艺设备仪器和软件（台/套）',align:'center', width:'210' },
            { prop: 'tfjpDeclaredAmount',showOverflowTooltip: true, label:'申报金额',align:'center', width:'120' },
            { prop: 'tfjpEvaluationReportNumber',showOverflowTooltip: true, label:'评估报告文号',align:'center', width:'130' },
            { prop: 'tfjpEvaluationReportDate',showOverflowTooltip: true, label:'评估报告日期',align:'center', width:'130' },
            { prop: 'tfjpEvaluationInvest',showOverflowTooltip: true, label:'评估保留总投资',align:'center', width:'130' },
            { prop: 'tfjpProposalPfcode',showOverflowTooltip: true, label:'立项批复文号',align:'center', width:'130' },
            { prop: 'tfjpProposalTime',showOverflowTooltip: true, label:'立项批复日期',align:'center', width:'130' },
            { prop: 'tfjpGbFundYearCs',showOverflowTooltip: true, label:'国拨投资',align:'center', width:'120' },
            { prop: 'tfjpSelfFinancingJys',showOverflowTooltip: true, label:'自筹资金',align:'center', width:'120' },
            { prop: 'tfjpPfInvestmentJys',showOverflowTooltip: true, label:'项目总投资',align:'center', width:'130' },
            { prop: 'tfjpKyPfcode',showOverflowTooltip: true, label:'可研批复文号',align:'center', width:'130' },
            { prop: 'tfjpKyTime',showOverflowTooltip: true, label:'可研批复日期',align:'center', width:'130' },
            { prop: 'tfjpBuildCycleKy',showOverflowTooltip: true, label:'可研建设周期（月）',align:'center', width:'140' },
            { prop: 'tfjpGbFundYearKy',showOverflowTooltip: true, label:'国拨投资',align:'center', width:'120' },
            { prop: 'tfjpSelfFinancingKy',showOverflowTooltip: true, label:'自筹资金',align:'center', width:'120' },
            { prop: 'tfjpPfInvestmentKy',showOverflowTooltip: true, label:'项目总投资',align:'center', width:'120' },
            { prop: 'tfjpDesignPfcode',showOverflowTooltip: true, label:'初设批复文号',align:'center', width:'130' },
            { prop: 'tfjpDesignTime',showOverflowTooltip: true, label:'初设批复日期',align:'center', width:'130' },
            { prop: 'tfjpInitialDesignCycleis',showOverflowTooltip: true, label:'初设批复周期（月）',align:'center', width:'150' },
            { prop: 'tfjpGbFundYearCs',showOverflowTooltip: true, label:'国拨投资',align:'center', width:'120' },
            { prop: 'tfjpSelfFinancingCs',showOverflowTooltip: true, label:'自筹资金',align:'center', width:'120' },
            { prop: 'tfjpPfInvestmentCs',showOverflowTooltip: true, label:'项目总投资',align:'center', width:'130' },
            { prop: 'tfjpAdjustCode',showOverflowTooltip: true, label:'调整批复文号',align:'center', width:'130' },
            { prop: 'tfjpAdjustTime',showOverflowTooltip: true, label:'调整批复日期',align:'center', width:'130' },
            { prop: 'tfjpAdjustCycle',showOverflowTooltip: true, label:'调整批复周期（月）',align:'center', width:'150' },
            { prop: 'tfjpGbFundYearTz',showOverflowTooltip: true, label:'国拨投资',align:'center', width:'120' },
            { prop: 'tfjpSelfFinancingTz',showOverflowTooltip: true, label:'自筹资金',align:'center', width:'120' },
            { prop: 'tfjpPfInvestmentTz',showOverflowTooltip: true, label:'项目总投资',align:'center', width:'130' },
            { prop: 'tfjpEndCode',showOverflowTooltip: true, label:'竣工验收批复文号',align:'center', width:'150' },
            { prop: 'tfjpEndTime',showOverflowTooltip: true, label:'竣工验收批复日期',align:'center', width:'150' },
            { prop: 'tfjpActualCycle',showOverflowTooltip: true, label:'实际建设周期（月）',align:'center', width:'150' },
            { prop: 'tfjpGbFundYearJg',showOverflowTooltip: true, label:'国拨投资',align:'center', width:'120' },
            { prop: 'tfjpSelfFinancingJg',showOverflowTooltip: true, label:'自筹资金',align:'center', width:'120' },
            { prop: 'tfjpPfInvestmentJg',showOverflowTooltip: true, label:'项目总投资',align:'center', width:'130' },
            { prop: 'tfjpRemark',showOverflowTooltip: true, label:'备注',align:'center', width:'120' },
          ],
          '自筹类固定资产投资':[
            { type: 'index', width:'55',showOverflowTooltip: true, label: "序号",align:'center' }, 
            // { prop: 'tfpCompanyNameNew',showOverflowTooltip: true, label:'项目所属二级单位最新名称',align:'center', width:'200' },
            { prop: 'tfpCompanyName',showOverflowTooltip: true, label:'二级单位',align:'center', width:'120' },
            { prop: 'tfpCzCompanyName',showOverflowTooltip: true, label:'投资主体',align:'center', width:'120' },
            { prop: 'tfpProjectName',showOverflowTooltip: true, label:'项目名称',align:'center', width:'120' },
            { prop: 'tfpMj',showOverflowTooltip: true, label:'项目密级',align:'center', width:'120' },
            { prop: 'tfpFileName',showOverflowTooltip: true, label:'文件名称',align:'center', width:'120' },
            { prop: 'tfpPhaseOrder',showOverflowTooltip: true, label:'项目所处阶段',align:'center', width:'120' },
            { prop: 'tfpApprovalCode',showOverflowTooltip: true, label:'批复/备案文号',align:'center', width:'150' },
            { prop: 'tfpApprovalDate',showOverflowTooltip: true, label:'批复/备案时间',align:'center', width:'120' },
            { prop: 'tfpPfInvestment',showOverflowTooltip: true, label:'批复/备案总投资额（万元）',align:'center', width:'150' },
            { prop: 'tfpConstructTarget',showOverflowTooltip: true, label:'批复/备案建设目标',align:'center', width:'120' },
            { prop: 'tfpContent',showOverflowTooltip: true, label:'批复/备案建设内容',align:'center', width:'120' },
            { prop: 'tfpBuildCycle',showOverflowTooltip: true, label:'批复/备案建设周期',align:'center', width:'120' },
            { prop: 'tfpProjectAcceptance',showOverflowTooltip: true, label:'项目验收形成资产总额',align:'center', width:'140' },
            { prop: 'tfpLandAreaAdd',showOverflowTooltip: true, label:'购置土地（万平方米）',align:'center', width:'120' },
            { prop: 'tfpBuildingAreaAdd',showOverflowTooltip: true, label:'新增建筑面积（万平方米）',align:'center', width:'120' },
            { prop: 'tfpGroundFloorAddArea',showOverflowTooltip: true, label:'新增地上建筑面积',align:'center', width:'120' },
            { prop: 'tfpAddress',showOverflowTooltip: true, label:'建设地址',align:'center', width:'120' },
            { type: 'action', prop: 'tfpProvince',showOverflowTooltip: true, label:'所在省市',align:'center', width:'120',render:(h,scope)=>{
              return <div>{scope.row.tfpProvince + scope.row.tfpCity}</div>
            } },
            { prop: 'tfpBuildCycleCurrent',showOverflowTooltip: true, label:'建设周期（月）',align:'center', width:'150' },
            { prop: 'tfpHandsFundYear',showOverflowTooltip: true, label:'自有资金',align:'center', width:'150' },
            { prop: 'tfpGbFundYear',showOverflowTooltip: true, label:'国拨资金',align:'center', width:'150' },
            { prop: 'tfpDebtFinancingYear',showOverflowTooltip: true, label:'债务融资',align:'center', width:'150' },
            { prop: 'tfpQyxrzYear',showOverflowTooltip: true, label:'权益性融资',align:'center', width:'150' },
            { prop: 'tfpOtherYear',showOverflowTooltip: true, label:'其他',align:'center', width:'150' },
            { prop: 'tfpDirectionGztd',showOverflowTooltip: true, label:'购置土地',align:'center', width:'150' },
            { prop: 'tfpDirectionXzjzw',showOverflowTooltip: true, label:'建安工程',align:'center', width:'150' },
            { prop: 'tfpDirectionGzsb',showOverflowTooltip: true, label:'新增工艺设备',align:'center', width:'150' },
            { prop: 'tfpInvestmentRecovery',showOverflowTooltip: true, label:'投资回收期',align:'center', width:'120' },
            { prop: 'tfpInternalRateReturn',showOverflowTooltip: true, label:'内涵报酬率',align:'center', width:'120' },
            { prop: 'tfpProjectConstruction',showOverflowTooltip: true, label:'项目建成年净利率',align:'center', width:'150' },
            { prop: 'tfpTurnoverRate',showOverflowTooltip: true, label:'项目建成年总资产周转率',align:'center', width:'160' },
            { prop: 'tfpBusinessPlan',showOverflowTooltip: true, label:'商业计划主要目标（动态年份表）',align:'center', width:'160' },
            { prop: 'tfpRequirement',showOverflowTooltip: true, label:'批复要求',align:'center', width:'120' },
            { prop: 'tfpIfMajor',showOverflowTooltip: true, label:'是否集团公司主业',align:'center', width:'160' },
            { prop: 'tfpIfCydwMajor',showOverflowTooltip: true, label:'是否本单位主业',align:'center', width:'160' },
            { prop: 'tfpIfAbroad',showOverflowTooltip: true, label:'是否境外投资',align:'center', width:'150' },
            { prop: 'tfpTypeOperation',showOverflowTooltip: true, label:'项目所属业态',align:'center', width:'150' },
            { prop: 'tfpInvestProperties',showOverflowTooltip: true, label:'投资性质',align:'center', width:'120' },
            { prop: 'tfpConsolidateStrengthen',showOverflowTooltip: true, label:'一巩固三做强类型',align:'center', width:'160' },
            { prop: 'tfpInvestType',showOverflowTooltip: true, label:'投资类型',align:'center', width:'120' },
            { prop: 'tfpBusinessType',showOverflowTooltip: true, label:'被投资企业所属行业',align:'center', width:'160' },
            { prop: 'tfpApprovalLevel',showOverflowTooltip: true, label:'审批级次',align:'center', width:'120' },
            { prop: 'tfpStartTime',showOverflowTooltip: true, label:'项目计划开始时间',align:'center', width:'160' },
            { prop: 'tfpEndTime',showOverflowTooltip: true, label:'项目计划竣工时间',align:'center', width:'160' },
            { prop: 'tfpXmpercpl',showOverflowTooltip: true, label:'行政总指挥',align:'center', width:'160' },
            { prop: 'tfpPrincipal',showOverflowTooltip: true, label:'技术总师',align:'center', width:'160' },
            { prop: 'tfpContactPerson',showOverflowTooltip: true, label:'项目经理',align:'center', width:'160' },
            { prop: 'tfpContactTel',showOverflowTooltip: true, label:'项目经理联系方式',align:'center', width:'160' },
            { prop: 'tfpRemark',showOverflowTooltip: true, label:'备注',align:'center', width:'120' },
          ],
          '股权投融资':[
            { type: 'index', width:'55',showOverflowTooltip: true, label: "序号",align:'center' }, 
            { prop: 'tspName',showOverflowTooltip: true, label:'项目名称',align:'center', width:'120' },
            { prop: 'tspCompanyName',showOverflowTooltip: true, label:'二级单位',align:'center', width:'120' },
            { prop: 'tspCzCompanyName',showOverflowTooltip: true, label:'投资主体',align:'center', width:'120' },
            { prop: 'tspImportance',showOverflowTooltip: true, label:'经济行为类型',align:'center', width:'150' },
            { prop: 'tspConsolidateStrengthen',showOverflowTooltip: true, label:'一巩固三做强类型',align:'center', width:'150' },
            { prop: 'tspCurrentStage',showOverflowTooltip: true, label:'所处阶段',align:'center', width:'120' },
            { prop: 'tspStageProcessingTime',showOverflowTooltip: true, label:'阶段办理时间',align:'center', width:'150' },
            { prop: 'tspProvince',showOverflowTooltip: true, label:'项目地点',align:'center', width:'120' },
            { prop: 'tspInvestmentPurpose',showOverflowTooltip: true, label:'投资目的',align:'center', width:'120' },
            { prop: 'tspBudgetThisYear',showOverflowTooltip: true, label:'项目金额（万元）',align:'center', width:'140' },
            { prop: 'tspProjectContent',showOverflowTooltip: true, label:'项目内容',align:'center' },
          ],
          '资产经营':[
            { type: 'index', width:'55',showOverflowTooltip: true, label: "序号",align:'center' }, 
            { prop: 'tztpIfPlan',showOverflowTooltip: true, label:'是否计划内',align:'center', width:'120' },
            { prop: 'tztpCompanyName',showOverflowTooltip: true, label:'二级成员单位',align:'center', width:'130' },
            { prop: 'tztpCzCompanyName',showOverflowTooltip: true, label:'资产权属单位名称',align:'center', width:'150' },
            { prop: 'tztpRevitalizeAssetsm',showOverflowTooltip: true, label:'拟处置资产名称',align:'center', width:'150' },
            { prop: 'tztpAssetsmCode',showOverflowTooltip: true, label:'资产编号',align:'center', width:'120' },
            { prop: 'tztpAssetsTypeName',showOverflowTooltip: true, label:'资产种类',align:'center', width:'120' },
            { prop: 'tztpHandleSituation',showOverflowTooltip: true, label:'拟处置资产情况',align:'center', width:'150' },
             { type: 'action', prop: 'tztpProvince',showOverflowTooltip: true, label:'拟处置资产所在地区',align:'center', width:'150',render:(h,scope)=>{
              return <div>{scope.row.tztpProvince + scope.row.tztpCity + scope.row.tztpCounty}</div>
            } },
            { prop: 'tztpTransferArea',showOverflowTooltip: true, label:'拟处置资产数量（平方米、台套）',align:'center', width:'140' },
            { prop: 'tztpValue',showOverflowTooltip: true, label:'资产账面净值（单位：万元）',align:'center', width:'120' },
            { prop: 'tztpWayName',showOverflowTooltip: true, label:'转让方式',align:'center', width:'120' },
            { prop: 'tztpFinishRecovery',showOverflowTooltip: true, label:'资产处置完成预计回收资金（单位：万元）',align:'center', width:'190' },
            { prop: 'tztpFinishIncome',showOverflowTooltip: true, label:'资产处置完成预计形成利润（单位：万元）',align:'center', width:'190' },
            { prop: 'tztpContactPerson',showOverflowTooltip: true, label:'联络人',align:'center', width:'120' },
            { prop: 'tztpContactTel',showOverflowTooltip: true, label:'联络人联系方式',align:'center', width:'130' },
            { prop: 'tztpRemark',showOverflowTooltip: true, label:'备注',align:'center', width:'120' },
          ]
        },
        tableHeader: [],
        tableData: [],
        taleCheckList: [],
        tableHeight: this.$baseTableHeight(2) - 100, 
        tablePage: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        },
        tableHeaderKey: Date.now(),  // 初始值,
        allTableData:[],
        allTablePage:[],
      }
    },
    async mounted(){
      this.radioValue = this.typeOptions[0];
      this.tableHeader = this.tableHeaderList[this.radioValue];
      this.taleCheckList = this.tableHeader.map(item => item.label);
      await this.initTableData();
      this.getsecondaryunitsList()
      // this.tableData = this.allTableData[0][0];
      // this.tablePage = this.allTablePage[0][0];
    },
    watch:{
      radioValue(val){
        this.formItem = this.formItem.slice(-2);
        this.searchForm = {}
        switch (val) {
        case "自筹类固定资产投资":
          this.formItem.unshift({ name: 'input', prop: 'name', label: '项目名称', placeholder: '请输入项目名称', },)
          this.formItem.unshift({
            name: 'selectTree',
            props: { value: 'zcbiId', label: 'zcbiName', children: 'children' },
            options: [],
            label: '二级单位名称',
            isChangeParent: true,
            getSelectTreeValue: this.getSelectTreeValue
          },)
          break;
        case "股权投融资":
          this.formItem.unshift({ name: 'input', prop: 'name', label: '项目名称', placeholder: '请输入项目名称', },)
          this.formItem.unshift({
            name: 'selectTree',
            props: { value: 'zcbiId', label: 'zcbiName', children: 'children' },
            options: [],
            label: '二级单位名称',
            isChangeParent: true,
            getSelectTreeValue: this.getSelectTreeValue
          },)
          break;
        case "军工类固定资产投资":
          this.formItem.unshift({ name: 'input', prop: 'name', label: '项目名称', placeholder: '请输入项目名称', },)
          this.formItem.unshift({
            name: 'selectTree',
            props: { value: 'zcbiId', label: 'zcbiName', children: 'children' },
            options: [],
            label: '二级单位名称',
            isChangeParent: true,
            getSelectTreeValue: this.getSelectTreeValue
          },)
          break;
        case "资产经营":
          this.formItem.unshift({ name: 'input', prop: 'name', label: '拟处置资产名称', placeholder: '请输入拟处置资产名称', },)
          this.formItem.unshift({
            name: 'selectTree',
            props: { value: 'zcbiId', label: 'zcbiName', children: 'children' },
            options: [],
            label: '二级单位名称',
            isChangeParent: true,
            getSelectTreeValue: this.getSelectTreeValue
          },)
          break;
        default:
          break;
      }
        this.tableHeader = this.tableHeaderList[val];
        this.taleCheckList = this.tableHeader.map(item => item.label);
        this.tableHeaderKey = Date.now();  // 表头变化时更新key，强制重绘
        // 等待DOM更新后再加载数据
        const radioValueIndex = this.typeOptions.findIndex(item => item == val);
        const radioValue1Index = this.radioValue1Options.findIndex(item => item == this.radioValue1);
        this.tableData = this.allTableData[radioValueIndex][radioValue1Index];
        this.tablePage = this.allTablePage[radioValueIndex][radioValue1Index];
      },
      radioValue1(val){
        const radioValueIndex = this.typeOptions.findIndex(item => item == this.radioValue);
        const radioValue1Index = this.radioValue1Options.findIndex(item => item == val);
        this.tableData = this.allTableData[radioValueIndex][radioValue1Index];
        this.tablePage = this.allTablePage[radioValueIndex][radioValue1Index];
      }
    },
    computed: {
      dragOptions() {
        return {
          animation: 600,
          group: 'description',
        }
      }
    },
  
    methods:{
          //获取二级单位
    async getsecondaryunitsList() {
      const { data, code, msg } = await getsecondaryunitsList()
      if (code == 200) {
        this.formItem[0].options = [data];
      } else {
        this.$message.error(msg);
      }
    },
       getSelectTreeValue(id, name) {
      this.searchForm.companyName = id
    },
      async initTableData(){
        const radioValueArr = cloneDeep(this.typeOptions);
        const radioValue1Arr = cloneDeep(this.radioValue1Options);
        radioValueArr.forEach((item,index) => {
          this.allTableData.push([]);
          this.allTablePage.push([]);
          radioValue1Arr.forEach(async(item1,index1) => {
            this.allTableData[index].push([]);
            this.allTablePage[index].push({
              pageNo: 1,
              pageSize: 10,
              total: 0
            })
            await this.getTableData(item,item1);
          });
        });
      },
      searchTableDate() {
      this.getTableData(this.radioValue, this.radioValue1)
    },
    resTable(){
      this.searchForm = {}
       this.getTableData(this.radioValue, this.radioValue1)
    },
      async getTableData(radioValue,radioValue1){
        console.log(radioValue,radioValue1,'==');
        const radioValueIndex = this.typeOptions.findIndex(item => item == radioValue);
        const radioValue1Index = this.radioValue1Options.findIndex(item => item == radioValue1);
        const params = {
          type:radioValue,
          tppCurrentStage:radioValue1,
          ...this.allTablePage[radioValueIndex][radioValue1Index],
          ...this.searchForm,
        };
        const {data,code,msg} = await getProjectList(params);
        if (code == 200) {
          this.allTableData[radioValueIndex][radioValue1Index] = data.list;
          this.allTablePage[radioValueIndex][radioValue1Index].total = data.total;
          if(radioValueIndex == 0 && radioValue1Index == 0){
            this.tableData = this.allTableData[radioValueIndex][radioValue1Index];
            this.tablePage = this.allTablePage[radioValueIndex][radioValue1Index];
          }
        } else {
          this.$message.error(msg);
        }
      },
      // 导出
      async handleExportRear(){
        let uncolumn = []
        if (this.tableCheckList && this.tableCheckList.length > 0) {
          let columnMap = {}
          for (let i = 0; i < this.tableCheckList.length; i++) {
            columnMap[this.tableCheckList[i]] = 1
          }

          for (let i = 0; i < this.tableHeader.length; i++) {
            if (!columnMap[this.tableHeader[i].label]) {
              uncolumn.push(this.tableHeader[i].prop)
            }
          }
        }
        //此方法为后端执行查询数据该查询数据接口与表格加载数据接口一致
        //导出当前页方法2，分页导出pageNo=1,2,3
        let params = {
          "dataFields": { "createdTime": { "celltype": "date" }, "updatedTime": { "celltype": "date" } },
          "fileName": "项目.xls",
          "isnumber": true,
          "uncols": uncolumn.length == 0 ? "" : uncolumn.join(","),
          //"excelTitle":"投资规划指标",
          "queryForm": this.searchForm || {}
        }
        //导出全部设置pageSize=-1即可
        //params.queryForm.pageSize=-1
        //单行表头支持自定义导出列，多级表头不支持
        let qf = exportRearEnd("#TzPlanTargets", params)
        // const { msg } = await tzPlanTargetsDoExport(qf)
        // window.open(baseURL + "/" + msg)
      },
      handleSizeChange(val) {
        this.tablePage.pageSize = val
        this.getTableData(this.radioValue, this.radioValue1)
      },
      handleCurrentChange(val) {

        this.tablePage.pageNo = val
        this.getTableData(this.radioValue, this.radioValue1)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .tabs{
    background: white;

    ::v-deep .el-tabs__nav-scroll{
      background: white;
      border-bottom: 1px solid #dcdfe6;
    }
    ::v-deep .is-active{
      background:rgba(204,18,20,0.04)!important;
    }
    ::v-deep .is-active,::v-deep .el-tab-pane{
      background: white;
    }
  }
  ::v-deep tr{
    height: 63px;
  }
  ::v-deep .el-table__body-wrapper {
    height: auto;
  }
</style>