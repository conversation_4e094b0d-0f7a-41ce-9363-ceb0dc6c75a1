<template>
  <div class="ysqbStatDetail-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="仪器设备分布情况" name="fbqk">
        <FbDetail />
      </el-tab-pane>
      <el-tab-pane label="仪器设备使用情况" name="syqk">
        <UseDetail :year="year"/>
      </el-tab-pane>
      <!-- <el-tab-pane label="仪器设备经营情况" name="jyqk">
        <JyDetail :year="year"/>
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>

  import FbDetail from './FbDetail'
  import UseDetail from './UseDetail'
  import JyDetail from './JyDetail'
  export default {
    name: "YsqbStatDetail",
    components: {
      FbDetail,
      UseDetail,
      JyDetail
    },
    data() {
      return {
        activeName: 'fbqk'
      }
    },
  }
</script>

<style lang="scss" scoped>
  .ysqbStatDetail-container {
    width: 100%;
    height: 100%;
  }
  .el-tabs,.el-tab-pane {
    width: 100%;
    height: 100%;
  }
  ::v-deep .el-tabs__nav-scroll {
    display: flex;
    justify-content: center;
  }
</style>