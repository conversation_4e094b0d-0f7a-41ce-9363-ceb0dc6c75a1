<template>
  <div>
    <VabForm :formModel="searchForm" :formItem="formItem" :formConfig="{ inline: true }" width="100%" />
    <VabTable id="xmlProjectTable" :tableHeader="tableHeader" :tableData="tableData" :pageNo="tablePage.pageNo"
      :pageSize="tablePage.pageSize" :total="tablePage.total" :tableHeight="tableHeight" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" />
  </div>
</template>

<script>
import { LesFile } from '@/utils/lesFile'
import VabForm from 'components/VabForm'
import VabTable from 'components/VabTable'
import { mapGetters } from 'vuex';
import { 
  gqtzhpjTableHeader, 
  zctzhpjTableHeader, 
  gqtzhpjFormItem, 
  zctzhpjFormItem, 
  xmqkxqTableHeader, 
  xmqkxqFormItem, 
  xmdsbqkZcTableHeader,
  xmdsbqkJgTableHeader,
  xmdsbqkZcFormItem,
  xmdsbqkJgFormItem,
} from './constant';
import { getXmqkDetail, getXmqkZcJgDetail } from 'api/tzgl/investOverview/investAll'
import { getPageList as GQgetPageList } from '@/api/tzgl/investAfter/investAfterEvaluate'
import { getPageList as ZCgetPageList } from '@/api/tzgl/investAfter/zcInvest'

export default {
  name: 'SecoundPage',
  components: {
    VabForm,
    VabTable
  },
  props: {
    year: {
      type: String,
      default: new Date().getFullYear() + ''
    },
     orgValue: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    rightClickParams:{
      type: Array,
      default: []
    },
    xmqkType:{
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchForm: {},
      formItem: [],
      tableHeader: [],
      tableData: [],
      tablePage: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
      },
      tableHeight: this.$baseTableHeight(1, 1) + 100,
      tableHeightList: {
        '股权投资后评价': {
          tableHeader: [
            ...gqtzhpjTableHeader,
            {
              type: 'action',
              label: '结论附件',
              align: 'center',
              width: '280',
              render: (h, scope) => {
                try {
                  const raw = scope.row.conclusionAttachment;
                  if (!raw) {
                    return <span style="color: #999;">无附件</span>;
                  }
                  const attachments = JSON.parse(raw);
                  const formFiles = Array.isArray(attachments.formAttachment) ? attachments.formAttachment : [];
                  const reportFiles = Array.isArray(attachments.reportAttachment) ? attachments.reportAttachment : [];
                  if (formFiles.length === 0 && reportFiles.length === 0) {
                    return <span style="color: #999;">无附件</span>;
                  }
                  return (
                    <div>
                      {formFiles.map((file, index) => (
                        <div
                          key={'formAttachment' + index}
                          style="margin: 4px 0; padding: 2px; cursor: pointer; color: #f82019;"
                          onClick={() => this.handleDonLoad(file.fileId)}
                        >
                          {file.name || file.fileName || '未知文件'}
                        </div>
                      ))}
                      {reportFiles.map((file, index) => (
                        <div
                          key={'reportAttachment' + index}
                          style="margin: 4px 0; padding: 2px; cursor: pointer; color: #f82019;"
                          onClick={() => this.handleDonLoad(file.fileId)}
                        >
                          {file.name || file.fileName || '未知文件'}
                        </div>
                      ))}
                    </div>
                  );
                } catch (err) {
                  console.warn('解析附件失败:', err);
                  return <span style="color: #999;">解析错误</span>;
                }
              }
            },
            { prop: 'remarks',width: '240', label: '备注', align: 'center', showOverflowTooltip: true }
          ],
          formItem: gqtzhpjFormItem,
        },
        '自筹投资后评价': {
          tableHeader: [
            ...zctzhpjTableHeader,
            {
              type: 'action',
              label: '结论附件',
              align: 'center',
              width: '280',
               render: (h, scope) => {
                try {
                  const raw = scope.row.conclusionAttachment;
                  if (!raw) {
                    return <span style="color: #999;">无附件</span>;
                  }
                  const attachments = JSON.parse(raw);
                  const formFiles = Array.isArray(attachments.formAttachment) ? attachments.formAttachment : [];
                  const reportFiles = Array.isArray(attachments.reportAttachment) ? attachments.reportAttachment : [];
                  if (formFiles.length === 0 && reportFiles.length === 0) {
                    return <span style="color: #999;">无附件</span>;
                  }
                  return (
                    <div>
                      {formFiles.map((file, index) => (
                        <div
                          key={'formAttachment' + index}
                          style="margin: 4px 0; padding: 2px; cursor: pointer; color: #f82019;"
                          onClick={() => this.handleDonLoad(file.fileId)}
                        >
                          {file.name || file.fileName || '未知文件'}
                        </div>
                      ))}
                      {reportFiles.map((file, index) => (
                        <div
                          key={'reportAttachment' + index}
                          style="margin: 4px 0; padding: 2px; cursor: pointer; color: #f82019;"
                          onClick={() => this.handleDonLoad(file.fileId)}
                        >
                          {file.name || file.fileName || '未知文件'}
                        </div>
                      ))}
                    </div>
                  );
                } catch (err) {
                  console.warn('解析附件失败:', err);
                  return <span style="color: #999;">解析错误</span>;
                }
              }
            },
            { prop: 'remarks',width: '240', label: '备注', align: 'center', showOverflowTooltip: true }
          ],
          formItem: zctzhpjFormItem,
        },
        '项目情况详情':{
          tableHeader:xmqkxqTableHeader,
          formItem:xmqkxqFormItem
        },
        '项目自筹待上报情况':{
          tableHeader:xmdsbqkZcTableHeader,
          formItem:xmdsbqkZcFormItem
        },
        '项目军工待上报情况':{
          tableHeader:xmdsbqkJgTableHeader,
          formItem:xmdsbqkJgFormItem
        }
      }
    }
  },
  watch:{
    title(val){
      this.title = val
      this.handleInitData();
    }
  },
  created() {
    this.handleInitData();
  },
  computed: {
    ...mapGetters({
      loginUser: 'user/loginUser',
    }),
  },
  methods: {
    handleInitData(){
      console.log( this.tableHeightList,this.title,'===');
      this.tableHeader = this.tableHeightList[this.title].tableHeader;
      this.formItem = [
        ...this.tableHeightList[this.title].formItem,
        { name: 'button', prop: 'button', label: '查询', type: "primary", click: this.getTableData },
        { name: 'button', prop: 'button', label: '重置', click: this.handleResetFun },
      ];
      this.getTableData()
    },
    //获取表格数据
    async getTableData() {
      const params = {
        ...this.searchForm,
        // year:new Date().getFullYear(),
        // zcbiId:this.loginUser.zcbiId,
        ...this.tablePage,
      }
      let res = { code: 0 }
      if (this.title == "股权投资后评价") {
        delete params.total
        params.economicBehaviorType = ''
        params.projectName = ''
        params.tsfpCompanyId = this.orgValue 
        params.tsfpYear = this.year
        res = await GQgetPageList(params)
      } else if(this.title == "自筹投资后评价") {
        params.txfpCompanyId = this.orgValue 
        params.txfpYear = this.year
        params.invested = 1
        res = await ZCgetPageList(params)
      }else if(this.title == "项目情况详情" || this.title == '项目自筹待上报情况' || this.title == '项目军工待上报情况') {
        params.year = this.year;
        params.zcbiId = this.orgValue;
        params.type = this.xmqkType;
        if(this.xmqkType == '股权类' || this.xmqkType == '资产处置'){
          params.phase = this.rightClickParams[2]
          res = await getXmqkDetail(params);
        }else{
          // 自筹和军工换接口和加传参
          params.phase = this.rightClickParams[3];
          params.state = this.rightClickParams[4];
          res = await getXmqkZcJgDetail(params);
        }
      }
      if (res.code == "200") {
        this.tableData = res.data.list
        this.tablePage.total = res.data.total;
      } else {
        this.$message.error(res.msg)
      }

    },
    handleDonLoad(fileId) {
      LesFile(fileId,'l')
    },
    // 重置
    handleResetFun() {
      this.searchForm = {};
      this.getTableData();
    },
    // 分页页数，页码切换
    handleSizeChange(val) {
      this.tablePage.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.tablePage.pageNo = val
      this.getTableData()
    },
  }
}
</script>

<style lang="scss" scoped></style>