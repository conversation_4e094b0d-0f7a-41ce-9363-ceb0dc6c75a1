<template>
  <div class="container">
    <div class="top-cont">
      <div 
        class="top-cont-item"
        v-for="(item,index) in contData"
        :key="index">
        <p>{{ item.title }} <span class="nums">{{ item.nums }}</span> {{ item.util }}</p>
      </div>
    </div>
    <VabChartPie style="margin-top: 24px;" :option="option"/>
  </div>
</template>

<script>
  import VabChartPie from '@/components/VabChartPie/VabChartPie.vue'
  // import { monthlyFundRecovery,sumPlanning } from '@/api/sam/land'
  export default {
    name: "YqsbZrDetail",
    props: ['year'],
    components: {
      VabChartPie
    },
    data() {
      return {
        contData: [
          {
            title: '转让项数',
            nums: '123',
            util: '项'
          },
          {
            title: '预计本年度回收资金',
            nums: '0',
            util: '万元'
          },
        ],
        option: {}
      }
    },
    methods: {
      async getStatData(name) {
        let params = {
          year: this.year,
          start: 3,
          name
        }
        // let { data } = await sumPlanning(params)
        // this.contData[0].nums = data[0].total || 0
        // this.contData[1].nums = data[0].zctpEstimateThisyear || 0
      },
      async getEchartData() {
        let params = {
          year: this.year,
          start: 3
        }
        // let { data } = await monthlyFundRecovery(params)
        // let yData1 = [],yData2 = []
        // data.forEach(item => {
        //   yData1.push(item.zctcActualFunds) 
        //   yData2.push(item.zctcActualFundsTo)
        // })
        // this.option = {
        //   tooltip: {
        //     trigger: 'axis',
        //     axisPointer: {
        //       type: 'shadow'
        //     }
        //   },
        //   legend: {},
        //   color: ['rgba(91,143,249,0.85)','rgba(90,216,166,0.85)'],
        //   grid: {
        //     left: '2%',
        //     right: '3%',
        //     bottom: '5%',
        //     top:'15%',
        //     containLabel: true
        //   },
        //   xAxis: {
        //     name: '月份',
        //     type: 'category',
        //     data: ['1', '2', '3', '4', '5', '6','7','8','9','10','11','12']
        //   },
        //   yAxis: [{
        //     name: '回收金额 (万元)',
        //     type: 'value'
        //   },],
        //   series: [
        //     {
        //       name: '当月值',
        //       data: yData1,
        //       type: 'bar'
        //     },
        //     {
        //       name: '累计值',
        //       data: yData2,
        //       type: 'bar'
        //     }
        //   ]
        // }
      }
    },
    watch: {
      year() {
        this.getEchartData()
        this.getStatData('转让')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
    .top-cont {
      width: 100%;
      height: 66px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .top-cont-item {
        width: calc((100% - 24px) / 2);
        height: 100%;
        background: rgba(255,132,135,0.04);
        box-shadow: 0px 8px 20px 0px rgba(177,197,197,0.08);
        border-radius: 4px;
        border: 1px solid #E4E7ED;
        text-align: center;
        line-height: 66px;
        p {
          margin: 0px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #000000;
          .nums {
            font-weight: 500;
            font-size: 24px;
          }
        }
      }
    }
  }
  :deep() {
    .echarts {
      width: 100%;
      height: 280px;
    }
  }
</style>